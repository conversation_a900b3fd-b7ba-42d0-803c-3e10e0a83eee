# TunnyCode 项目开发规范

## 项目概述

**技术栈：** Spring Cloud微服务 + Vue3前后端分离架构
- **后端：** Spring Boot 2.0.4 + Spring Cloud Finchley + MyBatis Plus + Nacos + Redis
- **前端：** Vue3 + Element Plus + Vite + Pinia + Axios
- **构建工具：** Maven (后端) + pnpm (前端)
- **数据库：** MySQL + Redis

## 强制性文件协调规则

### 后端API修改协调
- **修改Controller接口时**：必须同步检查并更新 `frontend/src/api/` 对应的API调用文件
- **修改实体类时**：必须同步更新对应的Mapper.xml文件和数据库表结构
- **修改Service接口时**：必须检查所有调用该Service的Controller和其他Service

### 前端组件修改协调
- **修改通用组件时**：必须检查 `frontend/src/views/` 下所有引用该组件的页面
- **修改路由时**：必须同步更新 `frontend/src/router/index.js` 和相关的权限配置
- **修改状态管理时**：必须检查所有使用该状态的组件

### 配置文件协调
- **修改bootstrap.yml时**：必须检查所有环境的对应配置文件
- **修改pom.xml依赖时**：必须检查是否影响其他模块的依赖关系
- **修改前端package.json时**：必须确保版本兼容性

## 后端开发规则

### 模块结构规则
- **新增微服务模块**：必须遵循 `tunny-xxx-client` / `tunny-xxx-server` 双模块结构
- **通用功能**：必须放在 `backend/tunny-common/` 对应子模块中
- **包命名**：必须遵循 `com.huazheng.tunny.{module}.{layer}` 格式

### 代码编写规则
- **实体类**：必须使用Lombok注解，禁止手写getter/setter
- **Controller方法**：必须添加 `@ApiOperation` 和 `@SysLog` 注解
- **Service层**：必须通过接口定义，实现类放在impl包中
- **数据访问**：禁止在Controller中直接调用Mapper，必须通过Service层
- **异常处理**：必须使用统一的R类返回结果，禁止直接抛出异常到前端

### 权限和安全规则
- **对外接口**：必须添加 `@PreAuthorize` 权限验证注解
- **敏感信息**：禁止硬编码，必须使用配置文件或环境变量
- **数据查询**：必须考虑分页处理，使用Page对象

## 前端开发规则

### 组件开发规则
- **新增页面**：必须在 `frontend/src/views/` 下按功能模块分类创建
- **通用组件**：必须放在 `frontend/src/components/` 中
- **API调用**：必须在 `frontend/src/api/` 中定义，禁止在组件中直接写API地址
- **Vue语法**：必须使用Composition API，禁止使用Options API

### 样式和资源规则
- **样式文件**：必须使用SCSS，全局样式放在 `frontend/src/assets/styles/`
- **图标使用**：优先使用Element Plus图标，自定义图标放在 `frontend/src/assets/icons/`
- **路由配置**：必须在 `frontend/src/router/index.js` 中统一管理

### 状态管理规则
- **全局状态**：必须使用Pinia，store文件放在 `frontend/src/store/modules/`
- **本地状态**：简单状态使用ref/reactive，复杂状态使用Pinia

## 数据库操作规则

### 实体和映射规则
- **实体类命名**：必须以DO结尾，放在对应模块的entity包中
- **Mapper接口**：必须继承BaseMapper，放在mapper包中
- **Mapper XML**：必须放在resources/mapper目录下，文件名与Mapper接口一致

### SQL编写规则
- **查询语句**：复杂查询必须在XML中编写，禁止在注解中写复杂SQL
- **参数传递**：必须使用 `@Param` 注解标注参数
- **结果映射**：复杂结果必须定义resultMap

## 禁止操作清单

### 严格禁止
- **禁止**直接修改生产环境配置文件
- **禁止**在代码中硬编码数据库连接信息
- **禁止**绕过Service层直接在Controller中调用Mapper
- **禁止**在前端组件中直接写API地址
- **禁止**修改通用组件而不检查影响范围
- **禁止**在没有权限验证的情况下暴露敏感接口

### 谨慎操作
- **谨慎**修改公共依赖版本
- **谨慎**修改数据库表结构
- **谨慎**修改通用工具类
- **谨慎**修改全局配置文件

## 决策优先级规则

### 冲突处理优先级
1. **安全性** > 功能性 > 性能 > 代码美观
2. **数据一致性** > 用户体验 > 开发效率
3. **现有架构约束** > 新技术引入

### 技术选择优先级
1. **项目已有技术栈** > 新技术
2. **团队熟悉度** > 技术先进性
3. **稳定性** > 新特性

## 工具配置要求

### 代码质量工具
- **后端**：必须遵循项目中的Checkstyle配置
- **前端**：必须通过ESLint检查，使用项目配置的Prettier格式化
- **提交**：必须遵循约定式提交规范

### 开发环境要求
- **Java版本**：必须使用JDK 1.8
- **Node版本**：必须使用16.15.0（参考frontend/package.json中volta配置）
- **包管理器**：前端必须使用pnpm，后端使用Maven

## 特殊文件处理规则

### 关键配置文件
- **bootstrap.yml**：修改时必须确保所有环境配置一致性
- **pom.xml**：修改依赖时必须检查版本冲突
- **package.json**：修改时必须考虑依赖兼容性

### 重要业务文件
- **权限相关**：修改时必须考虑安全影响
- **数据库迁移**：必须提供回滚方案
- **配置中心**：修改时必须考虑多环境影响

## 具体实施规范

### 新增Controller示例规范
```java
@RestController
@RequestMapping("/api/v1/users")
@Api(tags = "用户管理")
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping("/page")
    @ApiOperation("分页查询用户")
    @PreAuthorize("hasRole('ADMIN')")
    @SysLog("查询用户列表")
    public R<Page<UserVO>> getUserPage(@RequestParam Map<String, Object> params) {
        // 实现逻辑
    }
}
```

### 新增前端页面规范
```vue
<template>
  <div class="user-management">
    <!-- 页面内容 -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getUserList } from '@/api/system/user'

// 使用Composition API
const userList = ref([])

onMounted(() => {
  loadUserList()
})
</script>

<style lang="scss" scoped>
.user-management {
  // 样式定义
}
</style>
```

### API定义规范
```javascript
// frontend/src/api/system/user.js
import request from '@/utils/request'

export function getUserList(params) {
  return request({
    url: '/api/v1/users/page',
    method: 'get',
    params
  })
}
```

## 版本控制规范

### Git分支策略
- **主分支**：`main` - 生产环境代码
- **开发分支**：`develop` - 开发环境代码
- **功能分支**：`feature/功能名称` - 新功能开发
- **修复分支**：`hotfix/问题描述` - 紧急修复

### 提交信息规范
```
type(scope): subject

body

footer
```

**类型说明：**
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 代码审查检查清单
- [ ] 是否遵循命名规范
- [ ] 是否添加必要的注释和文档
- [ ] 是否进行了充分的错误处理
- [ ] 是否考虑了安全性问题
- [ ] 是否影响了其他模块的功能
- [ ] 是否需要更新相关的测试用例

## 性能优化指导

### 后端性能规范
- **数据库查询**：必须使用索引，避免全表扫描
- **缓存策略**：频繁查询的数据必须使用Redis缓存
- **分页处理**：大数据量查询必须实现分页
- **异步处理**：耗时操作必须使用异步处理

### 前端性能规范
- **组件懒加载**：路由组件必须使用懒加载
- **图片优化**：必须压缩图片，使用适当的格式
- **打包优化**：必须配置代码分割和压缩
- **缓存策略**：静态资源必须配置缓存策略

## 测试规范

### 单元测试要求
- **覆盖率**：核心业务逻辑测试覆盖率不低于80%
- **测试文件位置**：`src/test/java` 目录下
- **命名规范**：测试类名以 `Test` 结尾

### 集成测试要求
- **API测试**：所有对外接口必须有集成测试
- **数据库测试**：使用H2内存数据库进行测试
- **测试环境**：必须与生产环境配置分离

## 部署和运维规范

### 环境配置
- **开发环境**：`dev` profile
- **测试环境**：`test` profile
- **生产环境**：`prod` profile

### 日志规范
- **日志级别**：开发环境DEBUG，生产环境INFO
- **日志格式**：必须包含时间戳、线程、级别、类名、消息
- **敏感信息**：禁止在日志中输出密码、token等敏感信息

### 监控要求
- **健康检查**：所有服务必须提供健康检查接口
- **性能监控**：必须集成APM监控工具
- **错误追踪**：必须配置错误日志收集和告警
