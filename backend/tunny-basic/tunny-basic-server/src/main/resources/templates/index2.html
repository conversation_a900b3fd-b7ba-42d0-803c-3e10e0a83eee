<!--<!DOCTYPE html>-->
<!--<html lang="en" xmlns:v-bind="http://www.w3.org/1999/xhtml">-->
<!--<head>-->
<!--    <meta charset="UTF-8">-->
<!--    <title>BPM工作流演示/测试用例</title>-->

<!--    <script src="https://cdn.staticfile.org/jquery/2.0.0/jquery.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>-->
<!--    &lt;!&ndash; 引入样式 &ndash;&gt;-->
<!--    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">-->
<!--    &lt;!&ndash; 引入组件库 &ndash;&gt;-->
<!--    <script src="https://unpkg.com/element-ui/lib/index.js"></script>-->
<!--</head>-->
<!--<body>-->
<!--<div id="app" style="padding: 0 20px">-->
<!--    <el-row>-->
<!--        <el-col :span="24" style="border-bottom:1px solid #000;">-->
<!--            <h3 style="float:left;">{{topTitle}}</h3>-->
<!--            <el-dropdown @command="handleCommand" style="float:right;margin-top:10px;">-->
<!--                <el-button type="primary">示例<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
<!--                <el-dropdown-menu slot="dropdown">-->
<!--                    <el-dropdown-item command="1">流程节点绑定岗位</el-dropdown-item>-->
<!--                    <el-dropdown-item command="2">流程发起时增加模板版本号回执字段供业务系统保存 && 根据岗位/数据权限，获取唯一审批人</el-dropdown-item>-->
<!--                    <el-dropdown-item command="3">流程发起后能够展示流程未流转到的后续节点信息</el-dropdown-item>-->
<!--                    <el-dropdown-item command="4">批量刷新在途单据</el-dropdown-item>-->
<!--                </el-dropdown-menu>-->
<!--            </el-dropdown>-->
<!--        </el-col>-->
<!--    </el-row>-->
<!--    <el-row v-show="pageNum==1" style="margin-top:10px;">-->
<!--        <el-col :span="24">-->
<!--            <div>已有功能，但需要主数据开发岗位查询人员的接口</div>-->
<!--            <a href="http://localhost:11002/platform/bpmn/bpmDefine/list" target="_blank" rel="noopenner noreferrer">bpm后台链接</a>-->
<!--        </el-col>-->
<!--    </el-row>-->
<!--    <el-row v-show="pageNum==2" style="margin-top:10px;">-->
<!--        <el-col :span="24">-->
<!--            <div>提供给业务系统记录业务表单绑定的流程实例所使用的流程定义版本号，用于后续刷在途单据</div><br/>-->
<!--            <div>流程发起时传入人员数据权限参数，通过主数据配合开发接口。实现流程节点获取人员时能够按照数据权限调用接口抓取指定范围的审批人</div><br/>-->
<!--                <span style="width:8em">流程定义Key</span>-->
<!--                <el-input v-model="block2.defKey"></el-input>-->

<!--                <span style="width:8em">当前操作人工号</span>-->
<!--                <el-input v-model="block2.empNo"></el-input>-->

<!--                <span style="width:8em">当前操作人姓名</span>-->
<!--                <el-input v-model="block2.empName"></el-input>-->

<!--                <span style="width:8em">分支判断条件</span>-->
<!--                <el-input v-model="block2.gateWay"></el-input>-->

<!--                <span style="width:8em">人员数据权限</span>-->
<!--                <el-input v-model="block2.permissions"></el-input>-->
<!--            <el-button type="primary" plain @click="block2_submit">发起</el-button>-->
<!--        </el-col>-->
<!--    </el-row>-->
<!--    <el-row v-show="pageNum==3" style="margin-top:10px;">-->
<!--        <el-col :span="24">-->
<!--            <div>流程发起后可以调用接口，通过解析流程定义的xml获得节点并匹配流程实例的审批历史</div><br/>-->
<!--            <span style="width:8em">表单关联的流程号</span>-->
<!--            <el-input v-model="block3.procId" placeholder="" style="width:200px;"/><br/>-->
<!--        </el-col>-->
<!--        <el-button type="primary" plain @click="block3_submit">提交</el-button>-->

<!--    </el-row>-->
<!--    <el-row v-show="pageNum==4" style="margin-top:10px;">-->
<!--        <el-col :span="24">-->
<!--            <div>通过将bpm中流程相关表的版本字段更新到最新版本号，并比对新旧版本的XML获知流程第一次出现变动的节点，并将流程主动驳回到该节点重走流程，然后返回流程实例ID和更新后的版本号。</div>-->
<!--            <div>业务系统的对接形式：构建列表页面，通过向接口传流程实例号List的形式，批量刷新流程版本</div><br/>-->
<!--            <span style="width:8em">在途表单流程号</span>-->
<!--            <el-input v-model="block4.freshList" placeholder="" style="width:500px;"/><br/>-->
<!--        </el-col>-->
<!--        <el-button type="primary" plain @click="block4_submit">提交</el-button>-->

<!--    </el-row>-->
<!--    &lt;!&ndash;<el-row>&ndash;&gt;-->
<!--        &lt;!&ndash;<el-col style="padding:20px;margin-top:40px;">&ndash;&gt;-->
<!--            &lt;!&ndash;<iframe id="iframeUrl" :src="url" style="width:100%;height:500px;"></iframe>&ndash;&gt;-->
<!--            &lt;!&ndash;<el-input v-model="url"></el-input>&ndash;&gt;-->
<!--        &lt;!&ndash;</el-col>&ndash;&gt;-->
<!--    &lt;!&ndash;</el-row>&ndash;&gt;-->
<!--</div>-->
<!--<script>-->
<!--    var app = new Vue({-->
<!--        el: '#app',-->
<!--        data: {-->
<!--            pageNum:1,-->
<!--            topTitle:'流程节点绑定岗位',-->

<!--            block2: {-->
<!--                defKey: 'HTSB',-->
<!--                empNo: 'GC0011',-->
<!--                empName: '系统管理员',-->
<!--                gateWay: '{"amount":100}',-->
<!--                permissions: '{"project":"海尔云谷"}',-->
<!--            },-->
<!--            block3: {-->
<!--                procId:'644175849179840512'-->
<!--            },-->
<!--            block4:{-->
<!--                freshList:'[{"procId":"644175849179840512", "def_version": "1"},' +-->
<!--                    '{"procId":"644175885418627072", "def_version": "3"},' +-->
<!--                    '{"procId":"644175689158754304", "def_version": "1"},' +-->
<!--                    '{"procId":"644175681076330496", "def_version": "2"}]'-->
<!--            },-->



<!--            activeName: 'first',-->
<!--            url: '',-->
<!--            resData: '',-->
<!--            resDataHistory: [],-->
<!--            form: {},-->
<!--            defKey: 'HTSB',-->
<!--            empNo_: 'GC0011',-->
<!--            empName_: '系统管理员',-->
<!--            empNo: '',-->
<!--            empName: '',-->
<!--            procId: '',-->
<!--            taskId: '',-->
<!--            opinion: '',-->
<!--            destination: '',-->
<!--            backHandMode: '',-->
<!--            endReason: '',-->
<!--            tranUsers: '',-->
<!--            subject: '',-->
<!--            flag: 'pending',-->
<!--            gateWay: '',-->
<!--        },-->
<!--        methods: {-->
<!--            handleCommand: function(command) {-->
<!--                const title = ["实现流程节点绑定岗位",-->
<!--                "流程发起时增加模板版本号回执字段供业务系统保存",-->
<!--                "流程发起后能够展示流程未流转到的后续节点信息 && 根据岗位/数据权限，获取唯一审批人",-->
<!--                "批量刷新在途单据",];-->

<!--                this.topTitle = title[Number(command)-1];-->
<!--                this.pageNum = Number(command)-->
<!--            },-->
<!--            block2_submit: function(){-->
<!--                var temp = { "TASKID": "14017", "BIZKEY": "644713155280240640", "STATUS": "SUCCESS", "NODEUSERS": [ "GC0012,mm,pp,ss11111" ], "PROCID": "644713155418652672", "NODENAME": "直线审批", "DEF_VERSION":"3" };-->
<!--                alert("流程任务ID："+temp.TASKID+"\n"-->
<!--                    +"流程启动状态："+temp.STATUS+"\n"-->
<!--                    +"下一节点办理人："+temp.NODEUSERS+"\n"-->
<!--                    +"流程实例ID："+temp.PROCID+"\n"-->
<!--                    +"下一节点名称："+temp.NODENAME+"\n"-->
<!--                    +"流程定义版本号："+temp.DEF_VERSION+"\n");-->
<!--            },-->
<!--            block3_submit: function(){-->
<!--                var temp = {"MSG":"成功","STATUS":"SUCCESS","nodes":[{"nodeName":"发起人","nodeId":"UserTask_0ki2tse", "members":"发起人","status":"agree"},{"nodeName":"项目审核","nodeId":"UserTask_1v1i3y0","members":"项目审核员","status":"pending"},{"nodeName":"总经理审批","nodeId":"UserTask_1sjxtbo","members":"华东董事长角色"}]};-->
<!--                var node = "";-->
<!--                for(let i in temp.nodes){-->
<!--                    node += "  节点名称："+temp.nodes[i].nodeName+"，节点Id："+temp.nodes[i].nodeId+"，绑定用户："+temp.nodes[i].members;-->
<!--                    if(temp.nodes[i].status){-->
<!--                        node += "，状态："+temp.nodes[i].status-->
<!--                    }-->
<!--                    node += "\n"-->
<!--                }-->
<!--                alert("消息："+temp.MSG+"\n"-->
<!--                    +"状态："+temp.STATUS+"\n"-->
<!--                    +"节点："+"\n"+node);-->
<!--            },-->
<!--            block4_submit: function(){-->
<!--                alert("状态：SUCCESS\n"+"结果：\n"+'[{"procId":"644175849179840512", "def_version": "2"},' +-->
<!--                    '{"procId":"644175885418627072", "def_version": "5"},' +-->
<!--                    '{"procId":"644175689158754304", "def_version": "2"},' +-->
<!--                    '{"procId":"644175681076330496", "def_version": "3"}]')-->
<!--            },-->




<!--            handleClick: function (tab, event) {-->
<!--                // console.log(tab, event);-->
<!--            },-->
<!--            handleAjax: function () {-->
<!--                app.procId = app.resData.PROCID;-->
<!--                app.taskId = app.resData.TASKID;-->
<!--                app.url = "http://localhost:11002/platform/bpmn/instance/bpmInst/flowImage?id=" + app.resData.PROCID;-->
<!--                document.getElementById("iframeUrl").src = app.url-->
<!--            },-->
<!--            startProcess: function () {-->
<!--                $.post("http://localhost:10002/platform/startProcess",-->
<!--                    {-->
<!--                        defKey: app.defKey,-->
<!--                        empNo: app.empNo_,-->
<!--                        empName: app.empName_,-->
<!--                        gateWay: app.gateWay,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"流程发起：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            processList: function () {-->
<!--                $.post("http://localhost:10002/platform/processList",-->
<!--                    {-->
<!--                        empNo: app.empNo,-->
<!--                        flag: app.flag,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"查询流程相关数据：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            agree: function () {-->
<!--                $.post("http://localhost:10002/platform/agree",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        opinion: app.opinion,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"同意：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            oppose: function () {-->
<!--                $.post("http://localhost:10002/platform/oppose",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        opinion: app.opinion,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"反对：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            complete: function () {-->
<!--                $.post("http://localhost:10002/platform/complete",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"撤回：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            reject: function () {-->
<!--                $.post("http://localhost:10002/platform/reject",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        backHandMode: app.backHandMode,-->
<!--                        destination: app.destination,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"驳回：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            toReject: function () {-->
<!--                $.post("http://localhost:10002/platform/toReject",-->
<!--                    {-->
<!--                        taskId: app.taskId,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"可驳回节点：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            doEndProcess: function () {-->
<!--                $.post("http://localhost:10002/platform/doEndProcess",-->
<!--                    {-->
<!--                        taskId: app.taskId,-->
<!--                        endReason: app.endReason,-->
<!--                        empNo: app.empNo,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"终止流程：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            transfer: function () {-->
<!--                $.post("http://localhost:10002/platform/transfer",-->
<!--                    {-->
<!--                        taskId: app.taskId,-->
<!--                        taskName: ' ',-->
<!--                        procId: app.procId,-->
<!--                        subject: app.subject,-->
<!--                        empNo: app.empNo,-->
<!--                        tranUsers: app.tranUsers,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"流程转办：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--        }-->
<!--    })-->
<!--</script>-->

<!--</body>-->
<!--</html>-->