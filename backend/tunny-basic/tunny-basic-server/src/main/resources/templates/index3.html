<!--<!DOCTYPE html>-->
<!--<html lang="en" xmlns:v-bind="http://www.w3.org/1999/xhtml">-->
<!--<head>-->
<!--    <meta charset="UTF-8">-->
<!--    <title>BPM工作流业务系统应用示例</title>-->

<!--    <script src="https://cdn.staticfile.org/jquery/2.0.0/jquery.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>-->
<!--    &lt;!&ndash; 引入样式 &ndash;&gt;-->
<!--    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">-->
<!--    &lt;!&ndash; 引入组件库 &ndash;&gt;-->
<!--    <script src="https://unpkg.com/element-ui/lib/index.js"></script>-->
<!--</head>-->
<!--<body>-->
<!--<div id="app" style="padding: 20px">-->
<!--    <template v-if="login" style="width: 100%;">-->
<!--        <el-form ref="loginform" :model="form" label-width="80px" style="border:1px solid #000;width:33.2%;margin:0 auto;margin-top:10%;padding:20px 0;text-align: center;">-->
<!--            <el-row>-->
<!--                <el-col :span="23">-->
<!--                    <el-form-item label="账号">-->
<!--                        <el-select v-model="loginform.empNo" filterable allow-create default-first-option placeholder="" style="width:100%">-->
<!--                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>-->
<!--                        </el-select>-->
<!--                    </el-form-item>-->
<!--                </el-col>-->
<!--            </el-row>-->
<!--            <el-row>-->
<!--                <el-col :span="23">-->
<!--                    <el-form-item label="密码">-->
<!--                        <el-input v-model="loginform.pwd"></el-input>-->
<!--                    </el-form-item>-->
<!--                </el-col>-->
<!--            </el-row>-->
<!--            <el-row>-->
<!--                <el-col :span="23">-->
<!--                    <el-button type="primary" @click="loginin">登陆</el-button>-->
<!--                </el-col>-->
<!--            </el-row>-->
<!--        </el-form>-->
<!--    </template>-->
<!--    <template v-if="table">-->
<!--        <div type="primary" @click="newObject" style="color:deepskyblue;float:left;margin-bottom: 10px;cursor:pointer;">新增</div>-->
<!--&lt;!&ndash;        <div type="primary" @click="loginout" style="color:deepskyblue;float:left;margin-left:29%;margin-bottom: 10px;cursor:pointer;">退出</div>&ndash;&gt;-->
<!--        <div style="clear:both"></div>-->
<!--        <el-table :data="tableData" style="width: 33.2%;border:1px solid #000;">-->
<!--            <el-table-column prop="title" label="标题"></el-table-column>-->
<!--            <el-table-column prop="empNo" label="审批人"></el-table-column>-->
<!--            <el-table-column prop="procId" label="操作" width="120">-->
<!--                <template slot-scope="scope">-->
<!--                    <el-button @click="complete(scope.$index, scope.row)" type="text" size="small">审批</el-button>-->
<!--                </template>-->
<!--            </el-table-column>-->
<!--        </el-table>-->
<!--    </template>-->
<!--    <template v-if="info">-->
<!--        <div type="primary" @click="goback" style="color:deepskyblue;float:left;margin-bottom: 10px;cursor:pointer;">返回</div>-->
<!--&lt;!&ndash;        <div type="primary" @click="loginout" style="color:deepskyblue;float:left;margin-left:29%;margin-bottom: 10px;cursor:pointer;">退出</div>&ndash;&gt;-->
<!--        <div style="clear:both"></div>-->
<!--        <div style="clear:both"></div>-->
<!--        <el-button type="primary" @click="startProcess" v-if="!procId">启动</el-button>-->
<!--        <template v-if="procId">-->
<!--            <el-button type="primary" @click="agree" v-if="status!='END'">同意</el-button>-->
<!--    &lt;!&ndash;        <el-button type="primary" @click="oppose">反对</el-button>&ndash;&gt;-->
<!--    &lt;!&ndash;        <el-button type="primary" @click="revoke">撤回</el-button>&ndash;&gt;-->
<!--            <el-button type="primary" @click="reject" v-if="status!='END'">驳回</el-button>-->
<!--    &lt;!&ndash;        <el-button type="primary" @click="doEndProcess">终止</el-button>&ndash;&gt;-->
<!--            <el-button type="primary" @click="fullNodeInfo">刷新</el-button>-->
<!--        </template>-->
<!--        <el-row>-->
<!--            <el-col :span="8">-->
<!--                <div style="text-align: center"><h3>BPM应用示例表单</h3></div>-->
<!--            </el-col>-->
<!--        </el-row>-->
<!--        <el-form ref="form" :model="form" label-width="80px" style="border:1px solid #000;width:33.2%;padding-top:25px;margin-bottom:10px;">-->
<!--            <el-row>-->
<!--                <el-col :span="23">-->
<!--                    <el-form-item label="标题">-->
<!--                        <el-input v-model="form.title"></el-input>-->
<!--                    </el-form-item>-->
<!--                </el-col>-->
<!--            </el-row>-->
<!--            <el-row>-->
<!--                <el-col :span="23">-->
<!--                    <el-form-item label="金额">-->
<!--                        <el-input v-model="form.money"></el-input>-->
<!--                    </el-form-item>-->
<!--                </el-col>-->
<!--            </el-row>-->
<!--        </el-form>-->
<!--        <table class="sign_table" cellspacing="0" v-if="fullNodeInfoData!=''">-->
<!--            <tr style="background: #66CBFF">-->
<!--                <td colspan="2" width="60%">经营体成员</td>-->
<!--                <td rowspan="2">成员承诺</td>-->
<!--            </tr>-->
<!--            <tr style="background: #66CBFF">-->
<!--                <td>承诺人签字</td>-->
<!--                <td>部门</td>-->
<!--            </tr>-->
<!--            <tr v-for="(node,index) in fullNodeInfoData" v-if="index!=0">-->
<!--                <td style="background: #DDECFF">-->
<!--                    <div>-->
<!--                        <template v-for="(auditor, index2) in node.auditor">-->
<!--                            {{auditor.auditorName}}<template v-if="(index2+1) < node.auditor.length">、</template><template v-if="node.status!=null && (index2+1) == node.auditor.length">({{node.statusName}})</template>-->
<!--                        </template>-->
<!--                    </div>-->
<!--                    <div>-->
<!--                        <template v-for="(auditor, index2) in node.auditor">-->
<!--                            {{auditor.completeTime}}-->
<!--                        </template>-->
<!--                    </div>-->
<!--                    <div v-if="node.status!='pending' && node.status!=null">-->
<!--                        <template v-for="(auditor, index2) in node.auditor">-->
<!--                            <img :src="auditor.signName"/>-->
<!--                        </template>-->
<!--                    </div>-->
<!--                </td>-->
<!--                <td style="background: #DDECFF">-->
<!--                    <div>-->
<!--                        <template v-for="(roleName, index2) in node.role">-->
<!--                            {{roleName}}-->
<!--                        </template>-->
<!--                    </div>-->
<!--                </td>-->
<!--                <td>-->
<!--                    <template v-for="(auditor, index2) in node.auditor">-->
<!--                        {{auditor.opinion}}-->
<!--                    </template>-->
<!--                </td>-->
<!--            </tr>-->
<!--        </table>-->
<!--    </template>-->
<!--</div>-->
<!--<script>-->
<!--    var app = new Vue({-->
<!--        el: '#app',-->
<!--        data: {-->
<!--            login:false,-->
<!--            table:true,-->
<!--            info:false,-->
<!--            loginEmpNo: '',-->
<!--            loginform: {-->
<!--                empNo:"",-->
<!--                pwd:"******",-->
<!--            },-->
<!--            resData: '',-->
<!--            resDataHistory: [],-->
<!--            form: {-->
<!--                title:"",-->
<!--                money:"",-->
<!--            },-->
<!--            defKey: 'JDZXSP',-->
<!--            empNo_: '',-->
<!--            empName_: '',-->
<!--            empNo: '',-->
<!--            empName: '',-->
<!--            procId: '',-->
<!--            taskId: '',-->
<!--            opinion: '',-->
<!--            gateWay: '',//'{"bbbb":"1","cccc":"2"}',-->
<!--            fullNodeInfoData:[],-->
<!--            backHandMode: "normal",-->
<!--            destination: "A",-->
<!--            status: "",-->
<!--            tableData: [],-->
<!--            editIndex:-1,-->
<!--            options: [{-->
<!--                value: 'admin',-->
<!--                label: '六点'-->
<!--            },{-->
<!--                value: 'ss11111',-->
<!--                label: '小红'-->
<!--            }, {-->
<!--                value: '00086554',-->
<!--                label: '刘艳鹏'-->
<!--            }, {-->
<!--                value: 'pp',-->
<!--                label: 'ceshi'-->
<!--            }],-->
<!--        },-->
<!--        methods: {-->
<!--            loginin: function(){-->
<!--                if(!app.loginform.empNo){-->
<!--                    alert("请填写登陆账号");-->
<!--                    retrun-->
<!--                }-->
<!--                app.loginEmpNo = app.loginform.empNo;-->
<!--                app.login = false;-->
<!--                app.table = true;-->
<!--                app.info = false-->
<!--            },-->
<!--            loginout: function(){-->
<!--                app.loginEmpNo = "";-->
<!--                app.login = true;-->
<!--                app.table = false;-->
<!--                app.info = false-->
<!--            },-->
<!--            complete: function(index, row){-->
<!--                app.editIndex = index;-->
<!--                app.procId = row.procId;-->
<!--                app.taskId = row.taskId;-->
<!--                app.empNo = row.empNo;-->
<!--                app.form.title = row.title;-->
<!--                app.form.money = row.money;-->
<!--                this.fullNodeInfo();-->
<!--                app.login = false;-->
<!--                app.table = false;-->
<!--                app.info = true-->
<!--            },-->
<!--            goback: function(){-->
<!--                console.log(app.editIndex);-->
<!--                console.log(app.editIndex=="");-->
<!--                console.log(app.procId);-->
<!--                console.log(app.procId!='');-->
<!--                if(app.editIndex==-1 && app.procId!=''){-->
<!--                    app.tableData.push({-->
<!--                        title: app.form.title,-->
<!--                        money: app.form.money,-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        empNo: app.empNo,-->
<!--                        status: app.status,-->
<!--                    })-->
<!--                }else{-->
<!--                    app.tableData.splice(parseInt(app.editIndex), 1, {-->
<!--                        title: app.form.title,-->
<!--                        money: app.form.money,-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        empNo: app.empNo,-->
<!--                        status: app.status,-->
<!--                    })-->
<!--                }-->
<!--                app.editIndex = -1;-->
<!--                app.login = false;-->
<!--                app.table = true;-->
<!--                app.info = false-->
<!--            },-->
<!--            newObject: function(){-->
<!--                app.editIndex = -1;-->
<!--                app.procId = '';-->
<!--                app.taskId = '';-->
<!--                app.empNo = app.loginEmpNo;-->
<!--                app.form.title = '';-->
<!--                app.form.money = '';-->
<!--                app.fullNodeInfoData = [];-->
<!--                app.editIndex = -1;-->
<!--                app.login = false;-->
<!--                app.table = false;-->
<!--                app.info = true-->
<!--            },-->
<!--            handleAjax: function () {-->
<!--                if (app.resData.PROCID) {-->
<!--                    app.procId = app.resData.PROCID-->
<!--                }-->
<!--                app.taskId = app.resData.TASKID;-->
<!--                app.empNo = app.resData.NODEUSERS[0].split(",")[0];-->
<!--                app.fullNodeInfo();-->
<!--            },-->
<!--            formatDate: function(timestamp) {-->
<!--                var date = new Date(timestamp * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000-->
<!--                Y = date.getFullYear() + '-';-->
<!--                M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';-->
<!--                D = date.getDate() + ' ';-->
<!--                h = date.getHours() + ':';-->
<!--                m = date.getMinutes() + ':';-->
<!--                s = date.getSeconds();-->
<!--                return Y+M+D+h+m+s;-->
<!--            },-->
<!--            startProcess: function () {-->
<!--                $.post("http://localhost:10002/platform/startProcess",-->
<!--                    {-->
<!--                        defKey: app.defKey,-->
<!--                        empNo: app.empNo_,-->
<!--                        empName: app.empName_,-->
<!--                        gateWay: app.gateWay,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"流程发起：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->

<!--                    }-->
<!--                );-->
<!--            },-->
<!--            fullNodeInfo: function() {-->
<!--                $.post("http://localhost:10002/platform/fullNodeInfo",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.fullNodeInfoData = eval("("+(JSON.parse(result))+")").nodes;-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            agree: function () {-->
<!--                $.post("http://localhost:10002/platform/agree",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        opinion: app.opinion,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"同意：": JSON.parse(result)});-->
<!--                        app.status = app.resData.STATUS;-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            oppose: function () {-->
<!--                $.post("http://localhost:10002/platform/oppose",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        opinion: app.opinion,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"反对：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            reject: function () {-->
<!--                $.post("http://localhost:10002/platform/reject",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        backHandMode: app.backHandMode,-->
<!--                        destination: app.destination,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"驳回：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            doEndProcess: function () {-->
<!--                $.post("http://localhost:10002/platform/doEndProcess",-->
<!--                    {-->
<!--                        taskId: app.taskId,-->
<!--                        endReason: app.endReason,-->
<!--                        empNo: app.empNo,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"终止流程：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--        }-->
<!--    })-->
<!--</script>-->
<!--<style>-->
<!--    .sign_table {-->
<!--        text-align: center;-->
<!--        width: 33.4%;-->
<!--        border-bottom: 1px solid #000;-->
<!--        border-right: 1px solid #000;-->
<!--    }-->

<!--    .sign_table td {-->
<!--        border-width: 1px 0 0 1px;-->
<!--        border-style: solid;-->
<!--        border-color: #000;-->
<!--    }-->
<!--</style>-->
<!--</body>-->
<!--</html>-->