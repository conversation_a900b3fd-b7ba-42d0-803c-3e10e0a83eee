<!--<!DOCTYPE html>-->
<!--<html lang="en" xmlns:v-bind="http://www.w3.org/1999/xhtml">-->
<!--<head>-->
<!--    <meta charset="UTF-8">-->
<!--    <title>BPM工作流演示/测试用例</title>-->

<!--    <script src="https://cdn.staticfile.org/jquery/2.0.0/jquery.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>-->
<!--    &lt;!&ndash; 引入样式 &ndash;&gt;-->
<!--    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">-->
<!--    &lt;!&ndash; 引入组件库 &ndash;&gt;-->
<!--    <script src="https://unpkg.com/element-ui/lib/index.js"></script>-->
<!--</head>-->
<!--<body>-->
<!--<div id="app" style="padding: 20px">-->
<!--    <el-row>-->
<!--        <el-col :span="10" style="padding-right:40px;margin-right:40px;border-right:1px solid #666;">-->
<!--            <template>-->
<!--                <el-tabs v-model="activeName" @tab-click="handleClick">-->
<!--                    <el-tab-pane label="流程发起" name="first">-->
<!--                        <el-form ref="form" :model="form" label-width="120px">-->
<!--                            <el-form-item label="流程定义Key">-->
<!--                                <el-input v-model="defKey"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="当前操作人工号">-->
<!--                                <el-input v-model="empNo_"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="当前操作人姓名">-->
<!--                                <el-input v-model="empName_"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="网关条件">-->
<!--                                <el-input v-model="gateWay"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="后续节点办理人">-->
<!--                                <el-input v-model="nodeUsers"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="项目权限过滤">-->
<!--                                <el-input v-model="userFilter"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="业务表单信息">-->
<!--                            <el-input v-model="formInfo"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-button type="primary" plain @click="startProcess">发起</el-button>-->
<!--                        </el-form>-->
<!--                    </el-tab-pane>-->

<!--                    <el-tab-pane label="审批" name="second">-->
<!--                        <el-form ref="form" :model="form" label-width="120px">-->
<!--                            <el-form-item label="流程ID">-->
<!--                                <el-input v-model="procId"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="任务ID">-->
<!--                                <el-input v-model="taskId"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="当前操作人工号">-->
<!--                                <el-input v-model="empNo"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="当前操作人姓名">-->
<!--                                <el-input v-model="empName"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="审批意见">-->
<!--                                <el-input v-model="opinion"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="后续节点办理人">-->
<!--                                <el-input v-model="nodeUser"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="驳回节点ID">-->
<!--                                <el-input v-model="destination"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="是否重走流程">-->
<!--                                <template>-->
<!--                                    <el-radio v-model="backHandMode" label="direct">不重走</el-radio>-->
<!--                                    <el-radio v-model="backHandMode" label="normal">重走</el-radio>-->
<!--                                </template>-->
<!--                            </el-form-item>-->
<!--                            <el-button type="primary" plain @click="agree">同意</el-button>-->
<!--                            <el-button type="primary" plain @click="oppose">反对</el-button>-->
<!--                            <el-button type="primary" plain @click="complete">撤回</el-button>-->
<!--                            <el-button type="primary" plain @click="reject">驳回</el-button>-->
<!--                            <el-button type="primary" plain @click="toReject">查询可驳回节点</el-button>-->
<!--                        </el-form>-->

<!--                    </el-tab-pane>-->
<!--                    <el-tab-pane label="查询流程相关数据" name="fourth">-->
<!--                        <el-form ref="form" :model="form" label-width="120px">-->
<!--                            <el-form-item label="当前操作人工号">-->
<!--                                <el-input v-model="empNo"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="状态">-->
<!--                                <template>-->
<!--                                    <el-radio v-model="flag" label="pending">待办</el-radio>-->
<!--                                    <el-radio v-model="flag" label="handle">已办理</el-radio>-->
<!--                                    <el-radio v-model="flag" label="complete">已完结</el-radio>-->
<!--                                </template>-->
<!--                            </el-form-item>-->
<!--                            <el-button type="primary" plain @click="processList">查询</el-button>-->
<!--                        </el-form>-->
<!--                    </el-tab-pane>-->

<!--                    <el-tab-pane label="终止流程" name="fifth">-->
<!--                        <el-form ref="form" :model="form" label-width="120px">-->
<!--                            <el-form-item label="当前操作人工号">-->
<!--                                <el-input v-model="empNo"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="终止原因">-->
<!--                                <el-input v-model="endReason"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-button type="primary" plain @click="doEndProcess">终止</el-button>-->
<!--                        </el-form>-->
<!--                    </el-tab-pane>-->

<!--                    <el-tab-pane label="流程转办" name="sixth">-->
<!--                        <el-form ref="form" :model="form" label-width="120px">-->
<!--                            <el-form-item label="流程ID">-->
<!--                                <el-input v-model="procId"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="任务ID">-->
<!--                                <el-input v-model="taskId"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="当前操作人工号">-->
<!--                                <el-input v-model="empNo"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="任务办理人工号">-->
<!--                                <el-input v-model="tranUsers"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item label="原因">-->
<!--                                <el-input v-model="subject"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-button type="primary" plain @click="transfer">转办</el-button>-->
<!--                        </el-form>-->
<!--                    </el-tab-pane>-->


<!--                    <el-tab-pane label="流程审批历史" name="seventh">-->
<!--                        <el-form ref="form" :model="form" label-width="120px">-->
<!--                            <el-form-item label="流程ID">-->
<!--                                <el-input v-model="procId"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-button type="primary" plain @click="flowHistory">查询</el-button>-->
<!--                        </el-form>-->
<!--                    </el-tab-pane>-->

<!--                    <el-tab-pane label="刷新在途流程版本" name="eighth">-->
<!--                        <el-form ref="form" :model="form" label-width="120px">-->
<!--                            <el-form-item label="流程ID">-->
<!--                                <el-input v-model="processVersion"></el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-button type="primary" plain @click="updateProcessVersion">提交</el-button>-->
<!--                        </el-form>-->
<!--                    </el-tab-pane>-->
<!--                </el-tabs>-->
<!--            </template>-->
<!--        </el-col>-->
<!--        <el-col :span="10" style="max-height:500px;overflow-y: auto;">-->
<!--            <div v-for="his in resDataHistory" style="margin-bottom:20px;">-->
<!--                {{his}}-->
<!--            </div>-->
<!--        </el-col>-->
<!--    </el-row>-->
<!--    <el-row>-->
<!--        <el-col style="padding:20px;margin-top:40px;">-->
<!--            <iframe id="iframeUrl" :src="url" style="width:100%;height:500px;"></iframe>-->
<!--            <el-input v-model="url"></el-input>-->
<!--        </el-col>-->
<!--    </el-row>-->
<!--</div>-->
<!--<script>-->
<!--    var app = new Vue({-->
<!--        el: '#app',-->
<!--        data: {-->
<!--            activeName: 'first',-->
<!--            url: '',-->
<!--            resData: '',-->
<!--            resDataHistory: [],-->
<!--            form: {},-->
<!--            defKey: 'Process_2vqt8nd',-->
<!--            empNo_: 'admin',-->
<!--            empName_: '系统管理员',-->
<!--            empNo: '',-->
<!--            empName: '',-->
<!--            procId: '',-->
<!--            taskId: '',-->
<!--            opinion: '',-->
<!--            destination: '',-->
<!--            backHandMode: '',-->
<!--            endReason: '',-->
<!--            tranUsers: '',-->
<!--            subject: '',-->
<!--            flag: 'pending',-->
<!--            gateWay: '',-->
<!--            nodeUsers: '{"BBBB":["mm"]}',-->
<!--            nodeUser: '',-->
<!--            userFilter:'',-->
<!--            formInfo:'{"formKey":"表单主键","formType":"表单类型","formName":"表单名称","formSource":"表单系统来源"}',-->
<!--            processVersion:'[{"procId":"", "gateWay":{"条件名1":"条件值1","条件名2":"条件值2"}}]'-->
<!--        },-->
<!--        methods: {-->
<!--            handleClick: function (tab, event) {-->
<!--                // console.log(tab, event);-->
<!--            },-->
<!--            handleAjax: function () {-->
<!--                app.procId = app.resData.PROCID;-->
<!--                app.taskId = app.resData.TASKID;-->
<!--                app.empNo = app.resData.NODEUSERS.toString();-->
<!--                app.empName = app.resData.NODEUSERS.toString();-->
<!--                app.url = "http://localhost:11002/platform/bpmn/instance/bpmInst/flowImage?id=" + app.resData.PROCID;-->
<!--                document.getElementById("iframeUrl").src = app.url-->
<!--            },-->
<!--            startProcess: function () {-->
<!--                $.post("http://localhost:10002/platform/startProcess",-->
<!--                    {-->
<!--                        defKey: app.defKey,-->
<!--                        empNo: app.empNo_,-->
<!--                        empName: app.empName_,-->
<!--                        gateWay: app.gateWay,-->
<!--                        nodeUsers: app.nodeUsers,-->
<!--                        userFilter: app.userFilter,-->
<!--                        formInfo: app.formInfo,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"流程发起：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            processList: function () {-->
<!--                $.post("http://localhost:10002/platform/processList",-->
<!--                    {-->
<!--                        empNo: app.empNo,-->
<!--                        flag: app.flag,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"查询流程相关数据：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            agree: function () {-->
<!--                $.post("http://localhost:10002/platform/agree",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        opinion: app.opinion,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                        nodeUser: app.nodeUser,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"同意：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            oppose: function () {-->
<!--                $.post("http://localhost:10002/platform/oppose",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        opinion: app.opinion,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"反对：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            complete: function () {-->
<!--                $.post("http://localhost:10002/platform/complete",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"撤回：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            reject: function () {-->
<!--                $.post("http://localhost:10002/platform/reject",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                        taskId: app.taskId,-->
<!--                        backHandMode:app.backHandMode,-->
<!--                        destination:app.destination,-->
<!--                        empNo: app.empNo,-->
<!--                        empName: app.empName,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"驳回：": JSON.parse(result)});-->
<!--                        app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            toReject: function () {-->
<!--                $.post("http://localhost:10002/platform/toReject",-->
<!--                    {-->
<!--                        taskId: app.taskId,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"可驳回节点：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            doEndProcess: function () {-->
<!--                $.post("http://localhost:10002/platform/doEndProcess",-->
<!--                    {-->
<!--                        taskId: app.taskId,-->
<!--                        endReason: app.endReason,-->
<!--                        empNo: app.empNo,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"终止流程：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            transfer: function () {-->
<!--                $.post("http://localhost:10002/platform/transfer",-->
<!--                    {-->
<!--                        taskId: app.taskId,-->
<!--                        taskName: ' ',-->
<!--                        procId: app.procId,-->
<!--                        subject: app.subject,-->
<!--                        empNo: app.empNo,-->
<!--                        tranUsers: app.tranUsers,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"流程转办：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            flowHistory: function () {-->
<!--                $.post("http://localhost:10002/platform/flowHistory",-->
<!--                    {-->
<!--                        procId: app.procId,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"流程审批历史：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--            updateProcessVersion: function () {-->
<!--                $.post("http://localhost:10002/platform/updateProcessVersion",-->
<!--                    {-->
<!--                        process: app.processVersion,-->
<!--                    },-->
<!--                    function (result) {-->
<!--                        app.resData = JSON.parse(result);-->
<!--                        app.resDataHistory.push({"刷在途结果：": JSON.parse(result)});-->
<!--                        // app.handleAjax();-->
<!--                    }-->
<!--                );-->
<!--            },-->
<!--        }-->
<!--    })-->
<!--</script>-->

<!--</body>-->
<!--</html>-->