server:
  port: 10022
#微服务名称
spring:
  application:
    name: tunny-basic
#Flyway相关配置
  flyway:
    baseline-on-migrate: true #当迁移时发现目标schema非空，而且带有没有元数据的表时，是否自动执行基准迁移，默认false.
    enabled: true #enabled是否开启flywary，默认true.
    encoding: UTF-8 #设置迁移时的编码，默认UTF-8.
    locations: classpath:db/migration #flyway迁移版本路径
  #dev环境
  profiles:
    active: dev
#注册与配置中心地址
  cloud:
    nacos:
      username: nacos
      password: Tunny_huazheng@2024
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        #默认为application.name的值
        #prefix:
        #配置语法格式
        file-extension: yaml
        #Tunny微服务的通用配置，按实际需要配置即可，若存在多个用，隔开
        shared-dataids: tunny-dev.yaml
        #当以下dataids发生变化时，应用中动态刷新
        refreshable-dataids: tunny-dev.yaml
        #若不配置，则为public(保留空间)
        #namespace:
        #若不配置，则为DEFAULT_GROUP，可以用group来区分不同的项目或环境
        #group:

hello:
  ip-whitelist:
    mappings:
      ***********:
        - /user/userlist
        - /info/*
      ***********:
        - /info/*

#corsConfig:
#  origins: http://localhost,http://127.0.0.1
#  methods: GET,POST,PUT,DELETE


