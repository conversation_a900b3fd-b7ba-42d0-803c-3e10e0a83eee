package com.huazheng.tunny.basic.controller;

import com.huazheng.tunny.common.core.util.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Created by Lenovo on 2018/9/14.
 */
@Controller
public class ViewController {

    @RequestMapping("/index{version}")
    public String index(@PathVariable("version")String version){
        if(StringUtils.isNotEmpty(version)){
            return "/index"+Integer.valueOf(version);
        }
        return "/index";
    }
}
