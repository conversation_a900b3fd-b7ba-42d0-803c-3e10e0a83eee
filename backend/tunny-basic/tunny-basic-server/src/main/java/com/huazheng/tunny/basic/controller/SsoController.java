package com.huazheng.tunny.basic.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Console;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.huazheng.tunny.basic.api.dto.SsoInfo;
import com.huazheng.tunny.basic.api.dto.TokenResult;
import com.huazheng.tunny.basic.service.SsoService;
import groovy.util.logging.Slf4j;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Lenovo on 2018/9/14.
 * 单点登录回调
 */
@RestController
@Slf4j
public class SsoController {
    @Autowired
    private SsoService ssoService;

    @RequestMapping("/ssoIndex")
    public String ssoIndex(RedirectAttributes  attributes, String code ) throws Exception{
        SsoInfo ssoInfo = ssoService.getSsoinfo(code);
        //发起登录授权请求
        Console.log("参数："+code);
       //链式构建请求
        String url=ssoInfo.getTokenUrl();   //"http://127.0.0.1:4001/auth/oauth/token";
        HttpRequest httpRequest;
        if("POST".equals(ssoInfo.getTokenRequestType())) {
            httpRequest = HttpRequest.post(url);
        }else if("GET".equals(ssoInfo.getTokenRequestType())) {
            httpRequest = HttpRequest.get(url);
        }else{
            return null;
        }
        String result = httpRequest
//       .header("Authorization", "Basic dHVubnk6YUNkcVlXWUgjUmVCNE9qWA==")//头信息，多个头信息多次调用此方法即可
                .header("Authorization", "Basic " + Base64.encode(ssoInfo.getClientId() + ":" + ssoInfo.getClientSecret()))//头信息，多个头信息多次调用此方法即可
    //                .body("username=tunny&password=Tunny_huazheng&grant_type=password&scope=server")
            .body("username=" + ssoInfo.getUsername() + "&password=" + ssoInfo.getPassword() + "&grant_type=" + ssoInfo.getGrantType() + "&scope=" + ssoInfo.getScope())
            .timeout(20000)//超时，毫秒
            .execute().body();
        Console.log("ssoResult", result);
       // TokenResult tokenResult=JSONUtil.toBean(result,TokenResult.class);
        TokenResult tokenResult= JSON.parseObject(result,TokenResult.class);
        System.out.println("----------------------重定向--------------------------------------------------------");
       // attributes.addAttribute("name","joy");
        //attributes.addAttribute("token",tokenResult.access_token);
        return ssoInfo.getRedirectUrl() + "?token=" + tokenResult.getAccess_token();

    }

}
