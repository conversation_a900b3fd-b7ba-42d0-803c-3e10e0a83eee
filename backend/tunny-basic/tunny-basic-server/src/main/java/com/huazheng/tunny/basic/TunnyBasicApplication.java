package com.huazheng.tunny.basic;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


@EnableDiscoveryClient
@SpringBootApplication
@EnableFeignClients({"com.huazheng.tunny.basic.api.feign"})
public class TunnyBasicApplication {

	public static void main(String[] args) {
		SpringApplication.run(TunnyBasicApplication.class, args);
	}
}
