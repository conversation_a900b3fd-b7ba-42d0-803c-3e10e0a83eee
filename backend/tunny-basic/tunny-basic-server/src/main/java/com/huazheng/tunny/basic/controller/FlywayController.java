package com.huazheng.tunny.basic.controller;

import com.huazheng.tunny.basic.service.FlywayService;
import lombok.extern.slf4j.Slf4j;
import lombok.extern.slf4j.XSlf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class FlywayController {

    @Autowired
    private  FlywayService flywayService;

    @GetMapping("/rollback")
    public void rollback() {

        log.info("回滚到上个版本");
//        报错信息 只有pro版本才支持， 社区版本不支持
//        Flyway Pro Edition or Flyway Enterprise Edition upgrade required: undo is not supported by Flyway Community Edition.

        flywayService.rollbackToPreviousVersion();
    }

//    @GetMapping("/rollback/{targetVersion}")
//    public void rollback(@PathVariable String targetVersion) {
//        flywayService.rollbackToVersion(targetVersion);
//    }


}
