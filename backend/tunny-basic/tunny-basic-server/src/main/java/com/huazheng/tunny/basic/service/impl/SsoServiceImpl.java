package com.huazheng.tunny.basic.service.impl;

import com.huazheng.tunny.basic.api.dto.SsoInfo;
import com.huazheng.tunny.basic.mapper.SsoMapper;
import com.huazheng.tunny.basic.service.SsoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("ssoService")
public class SsoServiceImpl implements SsoService {
    @Autowired
    private SsoMapper ssoMapper;

    @Override
    public SsoInfo getSsoinfo(String sysCode) {
        return ssoMapper.getSsoinfo(sysCode);
    }
}
