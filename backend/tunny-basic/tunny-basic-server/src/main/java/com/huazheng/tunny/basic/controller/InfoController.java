package com.huazheng.tunny.basic.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * Created by Lenovo on 2018/9/14.
 */
@RestController
//配置如下注解，当Nacos的配置发生变更时，动态更新
@RefreshScope
@Slf4j
public class InfoController {

    @Value("${spring.application.name}")
    private String applicationName;
    @RequestMapping("/info")
    public  String info(){

        /*脱敏测试*/
//        log.info("localMobile:{}  localMobile:{}","023-55327459","123456");
//        log.info("your email:{}, your phone:{}","<EMAIL>","15310763497");
//        log.info("your email={}, your cellphone={}","<EMAIL>","15310763497");
//        log.info("email:{} ， mobile: {}","<EMAIL>","023-55327459");
//        log.info("identity:[]","123456789012345");
//        // Map类型
//        HashMap<String, String> map = new HashMap<>();
//        map.put("phone","15310763497");
//        map.put("email","<EMAIL>");
//        log.info("one map={}",map);
//        log.info("password:{}","189498asd6489s");

        return "Tunny标准开发脚手架，当前服务是："+applicationName;
    }

    @RequestMapping("/tokenTest")
    public  String  tokenTest(){
        return "测试带token访问白名单的接口";
    }

}
