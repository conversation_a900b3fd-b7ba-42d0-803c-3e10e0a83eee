package com.huazheng.tunny.basic.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Lenovo on 2020/7/24.
 */
@RestController
@Slf4j
@RequestMapping("/test")
public class LogController {
    @RequestMapping("/errorlog")
    public  String error(){
        log.error("error接口请求成功，并抛出空指针异常");
        System.out.println(1/0);
        return "success";
    }
    @RequestMapping("/infolog")
    public  String info(){
        System.out.println("控制台打印！！！");
        log.info("info接口请求成功");
        return "success";
    }
}
