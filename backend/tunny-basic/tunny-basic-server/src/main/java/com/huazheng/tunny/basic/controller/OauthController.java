package com.huazheng.tunny.basic.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * Created by Lenovo on 2020/5/9.
 * 授权码demo
 */
@RestController
public class OauthController {


    /**
     * 获取github的token
     * @param code
     * @return
     */
    @RequestMapping("/token/gettoken")
    public String getToken2(@RequestParam  String code ){
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("client_id", "600ef8c6c25d68e65213");
        paramMap.put("client_secret", "7d96c71de488a0d3418a044bb051bb005f5b3f4e");
        paramMap.put("code", code);
        String token_result= HttpUtil.post("https://github.com/login/oauth/access_token", paramMap);
        return token_result;
    }



    /**
     * 根据token获取github信息
     * @param token
     * @return
     */
    @RequestMapping("/token/getnfobytoken")
    public String getInfoByToken(@RequestParam String token ){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("Authorization", "token "+token);
        paramMap.put("accept", "application/json");
        String token_result= HttpRequest.get("https://api.github.com/user").header("Authorization", "token "+token).header("accept", "application/json").execute().body();
        return token_result;
    }
}
