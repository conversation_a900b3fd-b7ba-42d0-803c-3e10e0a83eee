package com.huazheng.tunny.basic.service;

import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.MigrationVersion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FlywayService {

    private final Flyway flyway;

    @Autowired
    public FlywayService(Flyway flyway) {
        this.flyway = flyway;
    }

    public void rollbackToPreviousVersion() {
        flyway.undo();
    }

    //貌似flyway不支持回滚到上个版本 todo
/*    public void rollbackToVersion(String targetVersion) {
        MigrationVersion mv=new MigrationVersion(targetVersion);
        // 执行回滚操作
        flyway.setTarget(targetVersion);
        flyway.migrate();
    }*/
}
