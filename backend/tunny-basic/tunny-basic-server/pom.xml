<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>tunny-basic</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>tunny-basic-server</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>

	<name>tunny-basic-server</name>
	<description>tunny 通用basic业务处理模块</description>

	<dependencies>
		<!--basic api、model 模块-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-basic-client</artifactId>
			<version>1.3.2</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>

		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>${mbp.boot.version}</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<!--common-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-core</artifactId>
			<version>1.3.2</version>
		</dependency>
		<!--swagger 内置安全模块-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-swagger</artifactId>
			<version>1.3.2</version>
			<exclusions>
				<!--为安全考虑移除actuator监控-->
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-actuator</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--web 模块-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
  			<!--排除tomcat依赖-->
				<exclusion>
					<artifactId>spring-boot-starter-tomcat</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>

		<!--xxljob 分布式任务执行器依赖-->
		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
			<version>2.2.0</version>
		</dependency>
		<!--日志采集上报-->
		<dependency>
			<groupId>com.plumelog</groupId>
			<artifactId>plumelog-logback</artifactId>
			<version>3.0</version>
		</dependency>

		<!--日志数据脱敏组件  按需引用-->
		<dependency>
			<groupId>pers.liuchengyin</groupId>
			<artifactId>logback-desensitization</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-starter</artifactId>
			<version>1.3.2</version>
		</dependency>

<!--		&lt;!&ndash;接入sentinel 流量控制&ndash;&gt;
		<dependency>
			<groupId>com.alibaba.csp</groupId>
			<artifactId>sentinel-core</artifactId>
			<version>1.8.0</version>
		</dependency>
		&lt;!&ndash;注解方式支持sentinel&ndash;&gt;
		<dependency>
			<groupId>com.alibaba.csp</groupId>
			<artifactId>sentinel-annotation-aspectj</artifactId>
			<version>x.y.z</version>
		</dependency>-->

		<!--集成flyway实现数据库版本管理-->
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
			<version>5.2.4</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>

		<resources>

		</resources>
	<!--<finalName>${project.artifactId}-${maven.build.timestamp}</finalName>-->
	</build>
	<properties>
		<!--自定义默认的编码格式-->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!--自定义默认的时间格式 年-月-日-时-分-->
		<maven.build.timestamp.format>
			yyyy-MM-dd_HH_mm
		</maven.build.timestamp.format>
	</properties>

</project>
