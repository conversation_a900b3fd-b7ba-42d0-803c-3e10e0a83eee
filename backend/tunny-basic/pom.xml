﻿<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>tunny</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>tunny-basic</artifactId>
	<version>1.3.2</version>
	<name>tunny-basic</name>
	<description>tunny 通用basic聚合模块</description>
	<packaging>pom</packaging>

	<modules>
		<module>tunny-basic-client</module>
		<module>tunny-basic-server</module>
	</modules>

	<repositories>

<!--	<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/libs-milestone</url>
		</repository>-->
		<!-- 使用aliyun镜像 -->

		<!--配置私服镜像-->
		<repository>
			<id>nexus</id>
<!--			<id>tunny</id>-->
			<name>tunny_group</name>
			<url>http://*************:30037/repository/tunny_group/</url>
			<!--			<url>http://************:8082/nexus/repository/tunny_group/</url>-->
			<releases>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</releases>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>

		<repository>
			<id>aliyun</id>
			<name>aliyun</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public/</url>
		</repository>


	</repositories>

	<distributionManagement>
		<snapshotRepository>
<!--			<id>tunny</id>-->
			<id>nexus</id>
			<url>http://*************:30037/repository/tunny_snapshots/</url>
		</snapshotRepository>
		<repository>
<!--			<id>tunny</id>-->
			<id>nexus</id>
			<url>http://*************:30037/repository/tunny_release/</url>
		</repository>
	</distributionManagement>
</project>
