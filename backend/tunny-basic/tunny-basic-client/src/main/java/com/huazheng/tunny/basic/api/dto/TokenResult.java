package com.huazheng.tunny.basic.api.dto;

import lombok.Data;

@Data
public class TokenResult {
    String access_token;
    String token_type;
    String refresh_token;
    String expires_in;
    String scope;
    String license;

    public TokenResult(String access_token, String token_type, String refresh_token, String expires_in, String scope, String license) {
        this.access_token=access_token;
        this.token_type=token_type;
        this.refresh_token=refresh_token;
        this.expires_in=expires_in;
        this.scope=scope;
        this.license=license;
    }
}
