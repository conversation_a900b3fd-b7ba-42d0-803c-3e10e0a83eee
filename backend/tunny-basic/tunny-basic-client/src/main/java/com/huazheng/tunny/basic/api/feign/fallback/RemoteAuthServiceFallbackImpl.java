package com.huazheng.tunny.basic.api.feign.fallback;

import com.huazheng.tunny.basic.api.feign.RemoteAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class RemoteAuthServiceFallbackImpl implements RemoteAuthService {

	@Override
	public OAuth2AccessToken getAccessToken(Map<String, String> parameters) {
		return null;
	}
}
