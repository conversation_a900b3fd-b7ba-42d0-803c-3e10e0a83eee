package com.huazheng.tunny.basic.api.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(value = "tunny-auth")
public interface RemoteAuthService {

    @GetMapping("/oauth/token")
    OAuth2AccessToken getAccessToken(@RequestParam Map<String, String> formData);

}
