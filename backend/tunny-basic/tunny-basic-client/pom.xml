<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>tunny-basic</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>tunny-basic-client</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>
	<name>tunny-basic-client</name>
	<description>tunny basic公共api模块</description>


	<dependencies>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <version>2.1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>org.springframework.security.oauth</groupId>
			<artifactId>spring-security-oauth2</artifactId>
			<version>2.3.3.RELEASE</version>
		</dependency>
	</dependencies>
</project>
