<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>tunny-admin</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>tunny-admin-server</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>

	<name>tunny-admin-server</name>
	<description>tunny 通用用户权限管理系统业务处理模块</description>

	<dependencies>
		<!-- 接口注解扫描 -->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-api</artifactId>
			<version>1.3.2</version>
		</dependency>
		<!--upms api、model 模块-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-admin-client</artifactId>
			<version>1.3.2</version>
		</dependency>
		<!--日志处理-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-log</artifactId>
			<version>1.3.2</version>
		</dependency>
		<!--swagger-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-swagger</artifactId>
			<version>1.3.2</version>
		</dependency>

		<!--spring security 、oauth、jwt依赖-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-security</artifactId>
			<exclusions>
				<!--为安全考虑移除actuator监控-->
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-actuator</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--监控客户端-->
<!--		<dependency>
			<groupId>de.codecentric</groupId>
			<artifactId>spring-boot-admin-starter-client</artifactId>
			<version>${monitor.version}</version>
		</dependency>-->

		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>${mbp.boot.version}</version>
		</dependency>
		<!--数据库-->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>

		<!--web 模块-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<!--<exclusions>-->
				<!--&lt;!&ndash;排除tomcat依赖&ndash;&gt;-->
				<!--<exclusion>-->
					<!--<artifactId>spring-boot-starter-tomcat</artifactId>-->
					<!--<groupId>org.springframework.boot</groupId>-->
				<!--</exclusion>-->
			<!--</exclusions>-->
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>


		<!--配置druid连接池-->
<!--		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.10</version>
		</dependency>-->


		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>

		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-data</artifactId>
			<version>1.3.2</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-core</artifactId>
			<version>${mbp.boot.version}</version>
		</dependency>

        <!-- tomcat的支持.-->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
            <scope>provided</scope>
        </dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
		</dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>asm</artifactId>
					<groupId>org.ow2.asm</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--poi-->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
			<exclusions>
				<exclusion>
					<artifactId>poi</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- rabbitmq -->
<!--		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-amqp</artifactId>
		</dependency>-->


	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.huazheng.tunny.admin.TunnyAdminApplication</mainClass>
				</configuration>
				<version>1.4.2.RELEASE</version>
			</plugin>
		</plugins>

		<resources>
			<!-- 打包时将jsp文件拷贝到META-INF目录下-->
			<resource>
				<!-- 指定resources插件处理哪个目录下的资源文件 -->
				<directory>src/main/webapp</directory>
				<!--注意此次必须要放在此目录下才能被访问到-->
				<targetPath>META-INF/resources</targetPath>
				<includes>
					<include>**/**</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/**</include>
				</includes>
				<filtering>false</filtering>
			</resource>
		</resources>
<!--		<finalName>${project.artifactId}-${maven.build.timestamp}</finalName>-->
	</build>
	<properties>
		<!--自定义默认的编码格式-->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!--自定义默认的时间格式 年-月-日-时-分-->
		<maven.build.timestamp.format>
			yyyy-MM-dd_HH_mm
		</maven.build.timestamp.format>
	</properties>

</project>
