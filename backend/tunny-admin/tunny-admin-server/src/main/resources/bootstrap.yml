server:
  port: 8001
spring:
  application:
    name: tunny-admin
  #dev环境
  profiles:
    active: dev
  #注册与配置中心地址
  cloud:
    nacos:
      username: nacos
      password: Tu<PERSON>_huazheng@2024
      discovery:
        server-addr: localhost:8848
#        namespace: #987e2ffb-3625-4043-be7a-3c4b0ea92df0
        group: DEFAULT_GROUP #TUNNY_YUN_GROUP
      config:
        server-addr: localhost:8848
        #默认为application.name的值
        #prefix:
        #配置语法格式
        file-extension: yaml
        #Tunny微服务的通用配置，按实际需要配置即可，若存在多个用，隔开
        #        shared-dataids: tunny-${spring.profiles.active}.yaml
        #当以下dataids发生变化时，应用中动态刷新
        #        refreshable-dataids: tunny-${spring.profiles.active}.yaml
        #若不配置，则为public(保留空间)
#        namespace: #987e2ffb-3625-4043-be7a-3c4b0ea92df0
        #若不配置，则为DEFAULT_GROUP，可以用group来区分不同的项目或环境
        group: DEFAULT_GROUP #TUNNY_YUN_GROUP
        ext-config:
          - data-id: tunny-${spring.profiles.active}.yaml
            group: DEFAULT_GROUP #TUNNY_YUN_GROUP
#            namespcae: #987e2ffb-3625-4043-be7a-3c4b0ea92df0
            refresh: true

