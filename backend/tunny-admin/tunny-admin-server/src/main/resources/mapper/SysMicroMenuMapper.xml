<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.SysMicroMenuMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.SysMicroMenu" id="sysMicroMenuResult">
        <result property="menuId" column="menu_id"/> <!-- 菜单id -->
        <result property="name" column="name"/> <!-- 菜单名称 -->
        <result property="permission" column="permission"/> <!-- 菜单权限标识 -->
        <result property="path" column="path"/> <!-- 前端url -->
        <result property="parentId" column="parent_id"/> <!-- 父菜单id -->
        <result property="icons" column="icons"/> <!-- 图标 -->
        <result property="component" column="component"/> <!-- vue页面 -->
        <result property="sort" column="sort"/> <!-- 排序值 -->
        <result property="type" column="type"/> <!-- 菜单类型 （0菜单 1按钮,9目录） -->
        <result property="microId" column="micro_id"/> <!-- 子应用id -->
        <result property="outside" column="outside"/> <!-- 是否外链 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateBy" column="update_by"/> <!-- 更新人 -->
        <result property="updateTime" column="update_time"/> <!-- 更新时间 -->
        <result property="delFlag" column="del_flag"/> <!-- 1:正常 0:删除 -->
    </resultMap>


    <sql id="selectSysMicroMenuVo">
        select menu_id, name, permission, path, parent_id, icons, component, sort, type, micro_id, outside, create_by, create_time, update_by, update_time, del_flag from sys_micro_menu
    </sql>
    <!-- 查询对象List -->
    <select id="selectSysMicroMenuList" parameterType="SysMicroMenu" resultMap="sysMicroMenuResult">
        <include refid="selectSysMicroMenuVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSysMicroMenuListByLike" parameterType="SysMicroMenu" resultMap="sysMicroMenuResult">
        <include refid="selectSysMicroMenuVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSysMicroMenuById" parameterType="Integer" resultMap="sysMicroMenuResult">
        <include refid="selectSysMicroMenuVo"/>
        where menu_id = #{menuId}
    </select>


    <update id="updateSysMicroMenu" parameterType="SysMicroMenu">
        update sys_micro_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null  and name != ''  ">name = #{name},</if>
            <if test="permission != null  and permission != ''  ">permission = #{permission},</if>
            <if test="path != null  and path != ''  ">path = #{path},</if>
            <if test="parentId != null  ">parent_id = #{parentId},</if>
            <if test="icons != null  and icons != ''  ">icons = #{icons},</if>
            <if test="component != null  and component != ''  ">component = #{component},</if>
            <if test="sort != null  ">sort = #{sort},</if>
            <if test="type != null  and type != ''  ">type = #{type},</if>
            <if test="microId != null  ">micro_id = #{microId},</if>
            <if test="outside != null  ">outside = #{outside},</if>
            <if test="createBy != null  and createBy != ''  ">create_by = #{createBy},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by = #{updateBy},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
        </trim>
        where menu_id = #{menuId}
    </update>

    <delete id="deleteSysMicroMenuById" parameterType="Integer">
        delete from sys_micro_menu where menu_id = #{menuId}
    </delete>

    <delete id="deleteSysMicroMenuByIds" parameterType="Integer">
        delete from sys_micro_menu where menu_id in
        <foreach item="menuId" collection="array" open="(" separator="," close=")">
            #{menuId}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSysMicroMenu" parameterType="SysMicroMenu" useGeneratedKeys="true" keyProperty="menuId">
        insert into sys_micro_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''  ">name,</if>
            <if test="permission != null  and permission != ''  ">permission,</if>
            <if test="path != null  and path != ''  ">path,</if>
            <if test="parentId != null  ">parent_id,</if>
            <if test="icons != null  and icons != ''  ">icons,</if>
            <if test="component != null  and component != ''  ">component,</if>
            <if test="sort != null  ">sort,</if>
            <if test="type != null  and type != ''  ">type,</if>
            <if test="microId != null  ">micro_id,</if>
            <if test="outside != null  ">outside,</if>
            <if test="createBy != null  and createBy != ''  ">create_by,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''  ">#{name},</if>
            <if test="permission != null  and permission != ''  ">#{permission},</if>
            <if test="path != null  and path != ''  ">#{path},</if>
            <if test="parentId != null  ">#{parentId},</if>
            <if test="icons != null  and icons != ''  ">#{icons},</if>
            <if test="component != null  and component != ''  ">#{component},</if>
            <if test="sort != null  ">#{sort},</if>
            <if test="type != null  and type != ''  ">#{type},</if>
            <if test="microId != null  ">#{microId},</if>
            <if test="outside != null  ">#{outside},</if>
            <if test="createBy != null  and createBy != ''  ">#{createBy},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">#{updateBy},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        menu_id,  <!-- 菜单id -->
        name,  <!-- 菜单名称 -->
        permission,  <!-- 菜单权限标识 -->
        path,  <!-- 前端url -->
        parent_id,  <!-- 父菜单id -->
        icons,  <!-- 图标 -->
        component,  <!-- vue页面 -->
        sort,  <!-- 排序值 -->
        type,  <!-- 菜单类型 （0菜单 1按钮,9目录） -->
        micro_id,  <!-- 子应用id -->
        outside,  <!-- 是否外链 -->
        create_by,  <!-- 创建人 -->
        create_time,  <!-- 创建时间 -->
        update_by,  <!-- 更新人 -->
        update_time,  <!-- 更新时间 -->
        del_flag  <!-- 1:正常 0:删除 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="menuId != null ">
            AND menu_id = #{menuId}  <!-- 菜单id -->
        </if>
        <if test="name != null and name != ''">
            AND name = #{name}  <!-- 菜单名称 -->
        </if>
        <if test="permission != null and permission != ''">
            AND permission = #{permission}  <!-- 菜单权限标识 -->
        </if>
        <if test="path != null and path != ''">
            AND path = #{path}  <!-- 前端url -->
        </if>
        <if test="parentId != null ">
            AND parent_id = #{parentId}  <!-- 父菜单id -->
        </if>
        <if test="icons != null and icons != ''">
            AND icons = #{icons}  <!-- 图标 -->
        </if>
        <if test="component != null and component != ''">
            AND component = #{component}  <!-- vue页面 -->
        </if>
        <if test="sort != null ">
            AND sort = #{sort}  <!-- 排序值 -->
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}  <!-- 菜单类型 （0菜单 1按钮,9目录） -->
        </if>
        <if test="microId != null ">
            AND micro_id = #{microId}  <!-- 子应用id -->
        </if>
        <if test="outside != null ">
            AND outside = #{outside}  <!-- 是否外链 -->
        </if>
        <if test="createBy != null and createBy != ''">
            AND create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="createTime != null ">
            AND create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            AND update_by = #{updateBy}  <!-- 更新人 -->
        </if>
        <if test="updateTime != null ">
            AND update_time = #{updateTime}  <!-- 更新时间 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}  <!-- 1:正常 0:删除 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="name != null and name != ''">
            AND name like concat('%', #{name}, '%')  <!-- 菜单名称 -->
        </if>
        <if test="permission != null and permission != ''">
            AND permission like concat('%', #{permission}, '%')  <!-- 菜单权限标识 -->
        </if>
        <if test="path != null and path != ''">
            AND path like concat('%', #{path}, '%')  <!-- 前端url -->
        </if>
        <if test="icons != null and icons != ''">
            AND icons like concat('%', #{icons}, '%')  <!-- 图标 -->
        </if>
        <if test="component != null and component != ''">
            AND component like concat('%', #{component}, '%')  <!-- vue页面 -->
        </if>
        <if test="type != null and type != ''">
            AND type like concat('%', #{type}, '%')  <!-- 菜单类型 （0菜单 1按钮,9目录） -->
        </if>
        <if test="createBy != null and createBy != ''">
            AND create_by like concat('%', #{createBy}, '%')  <!-- 创建人 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            AND update_by like concat('%', #{updateBy}, '%')  <!-- 更新人 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag like concat('%', #{delFlag}, '%')  <!-- 1:正常 0:删除 -->
        </if>
    </sql>

    <select id="selectWithMicroMenuVoPage" resultMap="sysMicroMenuResult">
        SELECT
        t.menu_id,
        t.name,
        t.permission,
        t.parent_id,
        t.icons,
        t.path,
        t.component,
        t.sort,
        t.type,
        t.micro_id,
        t.create_time,
        t.update_time,
        t.del_flag,
        IFNULL(d.name, '菜单') AS parent_name,
        t.outside
        FROM
        (
        SELECT
        menu_id,
        `name`,
        permission,
        parent_id,
        icons,
        path,
        component,
        sort,
        `type`,
        micro_id,
        create_time,
        update_time,
        del_flag,
        outside
        FROM sys_micro_menu
        WHERE
        1 = 1
        and del_flag = 1
        AND micro_id = #{microId}
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="dataSearch != null and  dataSearch != ''">
            AND name LIKE CONCAT('%',#{dataSearch},'%')
        </if>
        ) t
        LEFT JOIN sys_micro_menu d ON t.parent_id = d.menu_id
        ORDER BY t.menu_id
    </select>
</mapper>
