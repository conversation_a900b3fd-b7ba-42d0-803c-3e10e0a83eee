<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huazheng.tunny.admin.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huazheng.tunny.admin.api.entity.SysRole">
        <id column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
        <result column="role_desc" property="roleDesc"/>
        <result column="ds_type" property="dsType"/>
        <result column="ds_scope" property="dsScope"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <resultMap id="RoleDtoMap" type="com.huazheng.tunny.admin.api.dto.RoleDTO">
        <id column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
        <result column="role_desc" property="roleDesc"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="dept_id" property="roleDeptId"/>
        <result column="name" property="deptName"/>
        <result column="post_id" property="postId"/>
        <result column="post_name" property="postName"/>
    </resultMap>

    <select id="selectRolePage" resultMap="RoleDtoMap">
		SELECT
		r.role_id,
		r.role_name,
		r.role_code,
		r.role_desc,
		r.create_time,
		r.update_time,
		r.del_flag,
		FROM
		sys_role r
		WHERE r.del_flag = 1
		ORDER BY r.role_id ASC
	</select>

    <!--根据角色名称查询-->
    <select id="selectRolePageByRoleName" resultMap="RoleDtoMap">
        SELECT
        r.role_id,
        r.role_name,
        r.role_code,
        r.role_desc,
        r.create_time,
        r.update_time,
        r.del_flag
        FROM
        sys_role r
        WHERE r.del_flag = 1
        <if test="roleName != null and roleName != ''">
            and r.role_name like concat('%', #{roleName}, '%')
        </if>
        ORDER BY r.role_id ASC
    </select>
    <!-- 通过用户ID，查询角色信息-->
    <select id="findRolesByUserId" resultMap="BaseResultMap">
        SELECT
            r.*
        FROM
        sys_role r, sys_user_role ur WHERE r.role_id = ur.role_id AND r.del_flag = 1 and  ur.user_id IN (#{userId})
    </select>

	<select id="selectrolebyempno" parameterType="String" resultMap="BaseResultMap">
		SELECT
			*
		FROM
			sys_role
		WHERE
			role_id IN (
			SELECT
				role_id
			FROM
				sys_user_role
			WHERE
				user_id = ( SELECT user_id FROM sys_user WHERE empno = #{empno}
				)
			)
  </select>
</mapper>
