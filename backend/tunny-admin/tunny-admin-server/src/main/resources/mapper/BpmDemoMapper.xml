<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.BpmDemoMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.BpmDemo" id="bpmDemoResult">
        <result property="id" column="id"/> <!--  -->
        <result property="formName" column="form_name"/> <!-- 合同名称 -->
        <result property="formContent" column="form_content"/> <!-- 合同内容 -->
        <result property="contractAmount" column="contract_amount"/> <!-- 合同金额 -->
        <result property="procId" column="proc_id"/> <!-- 流程实例id -->
        <result property="bpmStatus" column="bpm_status"/> <!-- 流程状态 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="createByName" column="create_by_name"/> <!-- 创建人姓名 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time"/> <!-- 更新时间 -->
        <result property="delFlag" column="del_flag"/> <!-- 0-删除，1-正常 -->
    </resultMap>


    <sql id="selectBpmDemoVo">
        select id, form_name, form_content, contract_amount, proc_id, bpm_status, create_by, create_by_name, create_time, update_time, del_flag from bpm_demo
    </sql>
    <!-- 查询对象List -->
    <select id="selectBpmDemoList" parameterType="BpmDemo" resultMap="bpmDemoResult">
        <include refid="selectBpmDemoVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectBpmDemoListByLike" parameterType="BpmDemo" resultMap="bpmDemoResult">
        <include refid="selectBpmDemoVo"/>
        <where>
            <if test="delFlag != null and delFlag != ''">
                AND del_flag = #{delFlag}  <!-- 0-删除，1-正常 -->
            </if>
            <if test="processFlag=='my'">
                AND create_by = #{createBy}<!-- 创建人 -->
            </if>
            <if test="processFlag=='pending'">
                <if test="pendingProcIds != null and pendingProcIds.size() > 0">
                    AND proc_id IN (
                    <foreach collection="pendingProcIds" item="item" index="index" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="pendingProcIds.size() == 0">
                    AND proc_id = "-1"
                </if>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectBpmDemoById" parameterType="Integer" resultMap="bpmDemoResult">
        <include refid="selectBpmDemoVo"/>
        where id = #{id}
    </select>


    <update id="updateBpmDemo" parameterType="BpmDemo">
        update bpm_demo
        <trim prefix="SET" suffixOverrides=",">
            <if test="formName != null  and formName != ''  ">form_name = #{formName},</if>
            <if test="formContent != null  and formContent != ''  ">form_content = #{formContent},</if>
            <if test="contractAmount != null  and contractAmount != ''  ">contract_amount = #{contractAmount},</if>
            <if test="procId != null  and procId != ''  ">proc_id = #{procId},</if>
            <if test="bpmStatus != null  and bpmStatus != ''  ">bpm_status = #{bpmStatus},</if>
            <if test="createBy != null  and createBy != ''  ">create_by = #{createBy},</if>
            <if test="createByName != null  and createByName != ''  ">create_by_name = #{createByName},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBpmDemoById" parameterType="Integer">
        delete from bpm_demo where id = #{id}
    </delete>

    <delete id="deleteBpmDemoByIds" parameterType="Integer">
        delete from bpm_demo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertBpmDemo" parameterType="BpmDemo" useGeneratedKeys="true" keyProperty="id">
        insert into bpm_demo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formName != null  and formName != ''  ">form_name,</if>
            <if test="formContent != null  and formContent != ''  ">form_content,</if>
            <if test="contractAmount != null  and contractAmount != ''  ">contract_amount,</if>
            <if test="procId != null  and procId != ''  ">proc_id,</if>
            <if test="bpmStatus != null  and bpmStatus != ''  ">bpm_status,</if>
            <if test="createBy != null  and createBy != ''  ">create_by,</if>
            <if test="createByName != null  and createByName != ''  ">create_by_name,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formName != null  and formName != ''  ">#{formName},</if>
            <if test="formContent != null  and formContent != ''  ">#{formContent},</if>
            <if test="contractAmount != null  and contractAmount != ''  ">#{contractAmount},</if>
            <if test="procId != null  and procId != ''  ">#{procId},</if>
            <if test="bpmStatus != null  and bpmStatus != ''  ">#{bpmStatus},</if>
            <if test="createBy != null  and createBy != ''  ">#{createBy},</if>
            <if test="createByName != null  and createByName != ''  ">#{createByName},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!--  -->
        form_name,  <!-- 合同名称 -->
        form_content,  <!-- 合同内容 -->
        contract_amount,  <!-- 合同金额 -->
        proc_id,  <!-- 流程实例id -->
        bpm_status,  <!-- 流程状态 -->
        create_by,  <!-- 创建人 -->
        create_by_name,  <!-- 创建人姓名 -->
        create_time,  <!-- 创建时间 -->
        update_time,  <!-- 更新时间 -->
        del_flag  <!-- 0-删除，1-正常 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            AND id = #{id}  <!--  -->
        </if>
        <if test="formName != null and formName != ''">
            AND form_name = #{formName}  <!-- 合同名称 -->
        </if>
        <if test="formContent != null and formContent != ''">
            AND form_content = #{formContent}  <!-- 合同内容 -->
        </if>
        <if test="procId != null and procId != ''">
            AND proc_id = #{procId}  <!-- 流程实例id -->
        </if>
        <if test="bpmStatus != null and bpmStatus != ''">
            AND bpm_status = #{bpmStatus}  <!-- 流程状态 -->
        </if>
        <if test="createBy != null and createBy != ''">
            AND create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="createByName != null and createByName != ''">
            AND create_by_name = #{createByName}  <!-- 创建人姓名 -->
        </if>
        <if test="createTime != null ">
            AND create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="updateTime != null ">
            AND update_time = #{updateTime}  <!-- 更新时间 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}  <!-- 0-删除，1-正常 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="formName != null and formName != ''">
            AND form_name like concat('%', #{formName}, '%')  <!-- 合同名称 -->
        </if>
        <if test="formContent != null and formContent != ''">
            AND form_content like concat('%', #{formContent}, '%')  <!-- 合同内容 -->
        </if>
        <if test="procId != null and procId != ''">
            AND proc_id like concat('%', #{procId}, '%')  <!-- 流程实例id -->
        </if>
        <if test="bpmStatus != null and bpmStatus != ''">
            AND bpm_status like concat('%', #{bpmStatus}, '%')  <!-- 流程状态 -->
        </if>
        <if test="createBy != null and createBy != ''">
            AND create_by like concat('%', #{createBy}, '%')  <!-- 创建人 -->
        </if>
        <if test="createByName != null and createByName != ''">
            AND create_by_name like concat('%', #{createByName}, '%')  <!-- 创建人姓名 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag like concat('%', #{delFlag}, '%')  <!-- 0-删除，1-正常 -->
        </if>
    </sql>
</mapper>
