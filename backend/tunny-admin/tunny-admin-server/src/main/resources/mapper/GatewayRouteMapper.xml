<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.GatewayRouteMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.GatewayRoute" id="BaseResultMap">
        <id property="routeId" column="route_id" jdbcType="BIGINT"/>
        <result property="routeName" column="route_name" jdbcType="VARCHAR"/>
        <result property="serviceId" column="service_id" jdbcType="VARCHAR"/>
        <result property="uri" column="uri" jdbcType="VARCHAR"/>
        <result property="stripPrefix" column="strip_prefix" jdbcType="VARCHAR"/>
        <result property="predicatesPath" column="predicates_path" jdbcType="VARCHAR"/>
        <result property="predicates" column="predicates" jdbcType="VARCHAR"/>
        <result property="filters" column="filters" jdbcType="VARCHAR"/>
        <result property="routeOrder" column="route_order" jdbcType="VARCHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateId" column="update_id" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>

        <result property="removeRequestHeader" column="remove_request_header" jdbcType="VARCHAR"/>
        <result property="swaggerHeaderFilter" column="swagger_header_filter" jdbcType="VARCHAR"/>
        <result property="passwordDecoderFilter" column="password_decoder_filter" jdbcType="VARCHAR"/>
        <result property="imageCodeGatewayFilter" column="image_code_gateway_filter" jdbcType="VARCHAR"/>
        <result property="requestRateLimiter" column="request_rate_limiter" jdbcType="VARCHAR"/>
        <result property="hystrix" column="hystrix" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>
