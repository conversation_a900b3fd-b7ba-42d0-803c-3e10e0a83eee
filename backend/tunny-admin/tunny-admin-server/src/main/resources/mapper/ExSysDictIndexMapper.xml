<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.ExSysDictIndexMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.SysDictIndex" id="sysDictIndexResult">
        <result property="id" column="id"/> <!-- 主键 -->
        <result property="code" column="code"/> <!-- 名称 -->
        <result property="name" column="name"/> <!-- 名称 -->
        <result property="description" column="description"/> <!-- 详细 -->
        <result property="sort" column="sort"/> <!-- 排序 -->
        <result property="remarks" column="remarks"/> <!-- 备注 -->
        <result property="dictType" column="dict_type"/> <!-- 分类ID -->
        <result property="delFlag" column="del_flag"/> <!-- 有效标识1有效0失效 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time"/> <!-- 更新时间 -->
    </resultMap>

    <sql id="selectSysDictIndexVo">
        select id, code, name, description, sort, remarks, dict_type, del_flag, create_time, update_time from sys_dict_index
    </sql>

    <select id="selectDictIndex" parameterType="java.lang.Integer" resultMap="sysDictIndexResult">
        <include refid="selectSysDictIndexVo"/>
        where 1= 1
        and del_flag = 1
        <if test="typeIds != null">
            and dict_type IN
            <foreach collection="typeIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>
