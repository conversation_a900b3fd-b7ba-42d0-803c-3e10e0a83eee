<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.SysMicroMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.SysMicro" id="sysMicroResult">
        <result property="microId" column="micro_id"/> <!-- 子应用ID -->
        <result property="microName" column="micro_name"/> <!-- 子应用中文名称 -->
        <result property="microCode" column="micro_code"/> <!-- 	子应用英文名称（唯一） -->
        <result property="microPath" column="micro_path"/> <!-- 路由前缀 -->
        <result property="microIcon" column="micro_icon"/> <!-- 子应用图标 -->
        <result property="microDesc" column="micro_desc"/> <!-- 	子应用描述 -->
        <result property="microState" column="micro_state"/> <!-- 子应用启用状态 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateBy" column="update_by"/> <!-- 更新人 -->
        <result property="updateTime" column="update_time"/> <!-- 更新时间 -->
        <result property="delFlag" column="del_flag"/> <!-- 1:正常 0:删除 -->
    </resultMap>


    <sql id="selectSysMicroVo">
        select micro_id, micro_name, micro_code, micro_path, micro_icon, micro_desc, micro_state, create_by, create_time, update_by, update_time, del_flag from sys_micro
    </sql>
    <!-- 查询对象List -->
    <select id="selectSysMicroList" parameterType="SysMicro" resultMap="sysMicroResult">
        <include refid="selectSysMicroVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSysMicroListByLike" parameterType="SysMicro" resultMap="sysMicroResult">
        <include refid="selectSysMicroVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSysMicroById" parameterType="Integer" resultMap="sysMicroResult">
        <include refid="selectSysMicroVo"/>
        where micro_id = #{microId}
    </select>


    <update id="updateSysMicro" parameterType="SysMicro">
        update sys_micro
        <trim prefix="SET" suffixOverrides=",">
            <if test="microName != null  and microName != ''  ">micro_name = #{microName},</if>
            <if test="microCode != null  and microCode != ''  ">micro_code = #{microCode},</if>
            <if test="microPath != null  and microPath != ''  ">micro_path = #{microPath},</if>
            <if test="microIcon != null  and microIcon != ''  ">micro_icon = #{microIcon},</if>
            <if test="microDesc != null  and microDesc != ''  ">micro_desc = #{microDesc},</if>
            <if test="microState != null  ">micro_state = #{microState},</if>
            <if test="createBy != null  and createBy != ''  ">create_by = #{createBy},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by = #{updateBy},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
        </trim>
        where micro_id = #{microId}
    </update>

    <delete id="deleteSysMicroById" parameterType="Integer">
        delete from sys_micro where micro_id = #{microId}
    </delete>

    <delete id="deleteSysMicroByIds" parameterType="Integer">
        delete from sys_micro where micro_id in
        <foreach item="microId" collection="array" open="(" separator="," close=")">
            #{microId}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSysMicro" parameterType="SysMicro" useGeneratedKeys="true" keyProperty="microId">
        insert into sys_micro
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="microName != null  and microName != ''  ">micro_name,</if>
            <if test="microCode != null  and microCode != ''  ">micro_code,</if>
            <if test="microPath != null  and microPath != ''  ">micro_path,</if>
            <if test="microIcon != null  and microIcon != ''  ">micro_icon,</if>
            <if test="microDesc != null  and microDesc != ''  ">micro_desc,</if>
            <if test="microState != null  ">micro_state,</if>
            <if test="createBy != null  and createBy != ''  ">create_by,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="updateBy != null  and updateBy != ''  ">update_by,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="microName != null  and microName != ''  ">#{microName},</if>
            <if test="microCode != null  and microCode != ''  ">#{microCode},</if>
            <if test="microPath != null  and microPath != ''  ">#{microPath},</if>
            <if test="microIcon != null  and microIcon != ''  ">#{microIcon},</if>
            <if test="microDesc != null  and microDesc != ''  ">#{microDesc},</if>
            <if test="microState != null  ">#{microState},</if>
            <if test="createBy != null  and createBy != ''  ">#{createBy},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="updateBy != null  and updateBy != ''  ">#{updateBy},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        micro_id,  <!-- 子应用ID -->
        micro_name,  <!-- 子应用中文名称 -->
        micro_code,  <!-- 	子应用英文名称（唯一） -->
        micro_path,  <!-- 路由前缀 -->
        micro_icon,  <!-- 子应用图标 -->
        micro_desc,  <!-- 	子应用描述 -->
        micro_state,  <!-- 子应用启用状态 -->
        create_by,  <!-- 创建人 -->
        create_time,  <!-- 创建时间 -->
        update_by,  <!-- 更新人 -->
        update_time,  <!-- 更新时间 -->
        del_flag  <!-- 1:正常 0:删除 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="microId != null ">
            AND micro_id = #{microId}  <!-- 子应用ID -->
        </if>
        <if test="microName != null and microName != ''">
            AND micro_name = #{microName}  <!-- 子应用中文名称 -->
        </if>
        <if test="microCode != null and microCode != ''">
            AND micro_code = #{microCode}  <!-- 	子应用英文名称（唯一） -->
        </if>
        <if test="microPath != null and microPath != ''">
            AND micro_path = #{microPath}  <!-- 路由前缀 -->
        </if>
        <if test="microIcon != null and microIcon != ''">
            AND micro_icon = #{microIcon}  <!-- 子应用图标 -->
        </if>
        <if test="microDesc != null and microDesc != ''">
            AND micro_desc = #{microDesc}  <!-- 	子应用描述 -->
        </if>
        <if test="microState != null ">
            AND micro_state = #{microState}  <!-- 子应用启用状态 -->
        </if>
        <if test="createBy != null and createBy != ''">
            AND create_by = #{createBy}  <!-- 创建人 -->
        </if>
        <if test="createTime != null ">
            AND create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            AND update_by = #{updateBy}  <!-- 更新人 -->
        </if>
        <if test="updateTime != null ">
            AND update_time = #{updateTime}  <!-- 更新时间 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}  <!-- 1:正常 0:删除 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="microName != null and microName != ''">
            AND micro_name like concat('%', #{microName}, '%')  <!-- 子应用中文名称 -->
        </if>
        <if test="microCode != null and microCode != ''">
            AND micro_code like concat('%', #{microCode}, '%')  <!-- 	子应用英文名称（唯一） -->
        </if>
        <if test="microPath != null and microPath != ''">
            AND micro_path like concat('%', #{microPath}, '%')  <!-- 路由前缀 -->
        </if>
        <if test="microIcon != null and microIcon != ''">
            AND micro_icon like concat('%', #{microIcon}, '%')  <!-- 子应用图标 -->
        </if>
        <if test="microDesc != null and microDesc != ''">
            AND micro_desc like concat('%', #{microDesc}, '%')  <!-- 	子应用描述 -->
        </if>
        <if test="createBy != null and createBy != ''">
            AND create_by like concat('%', #{createBy}, '%')  <!-- 创建人 -->
        </if>
        <if test="updateBy != null and updateBy != ''">
            AND update_by like concat('%', #{updateBy}, '%')  <!-- 更新人 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag like concat('%', #{delFlag}, '%')  <!-- 1:正常 0:删除 -->
        </if>
    </sql>
</mapper>
