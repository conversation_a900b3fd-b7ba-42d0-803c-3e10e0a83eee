<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huazheng.tunny.admin.mapper.SysUserMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.SysUser" id="sysUserNewResult">
        <result property="userId" column="user_id"/> <!-- 主键ID -->
        <result property="username" column="username"/> <!-- 登陆名称 -->
        <result property="password" column="password"/> <!-- 登陆密码 -->
        <result property="userRealname" column="user_realname"/> <!-- 用户真实姓名 -->
        <result property="sex" column="sex"/> <!-- 0-男  1-女 -->
        <result property="phone" column="phone"/> <!-- 电话 -->
        <result property="email" column="email"/> <!-- 邮箱 -->
        <result property="empno" column="empno"/> <!-- 员工号 -->
        <result property="signName" column="sign_name"/> <!-- 用户电子签名 -->
        <result property="deptId" column="dept_id"/> <!-- 部门ID -->
        <result property="salt" column="salt"/> <!-- 随机盐 -->
        <result property="avatar" column="avatar"/> <!-- 头像 -->
        <result property="userFlag" column="user_flag"/> <!-- 用户状态 0-正常 1-停用 -->
        <result property="passwordEndtime" column="password_endtime"/> <!-- 密码到期时间 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="createBy" column="create_by"/> <!-- 创建人 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
        <result property="updateBy" column="update_by"/> <!-- 修改人 -->
        <result property="delFlag" column="del_flag"/> <!-- 1-正常，0-删除 -->
        <result property="wxOpenid" column="wx_openid"/> <!-- 微信openid -->
        <result property="qqOpenid" column="qq_openid"/> <!-- QQ openid -->
        <result property="remark" column="remark"/> <!--  -->
        <result property="adminRemark" column="admin_remark"/> <!-- 工程师备注 -->
    </resultMap>

    <!-- userVo结果集 -->
    <resultMap id="userVoResultMap" type="com.huazheng.tunny.admin.api.vo.UserVO">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="user_realname" property="userRealname"/>
        <result column="sex" property="sex"/>
        <result column="email" property="email"/>
        <result column="empno" property="empno"/>
        <result column="password" property="password"/>
        <result column="salt" property="salt"/>
        <result column="phone" property="phone"/>
        <result column="avatar" property="avatar"/>
        <result column="wx_openid" property="wxOpenid"/>
        <result column="qq_openid" property="qqOpenid"/>
        <result column="sign_name" property="signName"/>
        <result column="ucreate_time" property="createTime"/>
        <result column="uupdate_time" property="updateTime"/>
        <result column="udel_flag" property="delFlag"/>
        <result column="user_flag" property="userFlag"/>
        <result column="deptId" property="deptId"/>
        <result column="deptName" property="deptName"/>

        <collection property="roleList" ofType="com.huazheng.tunny.admin.api.entity.SysRole">
            <id column="role_id" property="roleId"/>
            <result column="role_name" property="roleName"/>
            <result column="role_code" property="roleCode"/>
            <result column="role_desc" property="roleDesc"/>
            <result column="rcreate_time" property="createTime"/>
            <result column="rupdate_time" property="updateTime"/>
        </collection>
        <collection property="postList" ofType="com.huazheng.tunny.admin.api.entity.SysPost">
            <id column="post_id" property="id"/>
            <result column="post_code" property="postCode"/>
            <result column="post_name" property="postName"/>
        </collection>
    </resultMap>

    <!--新用户列表查询resultMap-->
    <resultMap type="com.huazheng.tunny.admin.api.vo.UserVO" id="newUserVoResultMap">
        <result property="userId" column="user_id"/> <!-- 主键ID -->
        <result property="username" column="username"/> <!-- 登陆名称 -->
        <result property="userRealname" column="user_realname"/> <!-- 用户真实姓名 -->
        <result property="sex" column="sex"/> <!-- 0-男  1-女 -->
        <result property="empno" column="empno"/> <!-- 员工号 -->
        <result property="deptId" column="dept_id"/> <!-- 部门ID -->
        <result property="deptName" column="dept_name"/> <!-- 部门名称-->
        <result property="userFlag" column="user_flag"/> <!-- 用户状态 0-正常 1-停用 -->
        <result property="postIds" column="post_id"/> <!-- 岗位ID -->
        <result property="postNames" column="post_name"/> <!-- 岗位名称 -->
    </resultMap>

    <sql id="selectUserVo">
        SELECT
            `user`.user_id,
            `user`.username,
            `user`.`password`,
            `user`.salt,
            `user`.phone,
            `user`.avatar,
            `user`.wx_openid,
            `user`.qq_openid,
            `user`.dept_id,
            `user`.create_time AS ucreate_time,
            `user`.update_time AS uupdate_time,
            `user`.del_flag AS udel_flag,
            `user`.dept_id AS deptId,
            `user`.post_id AS postId,
            r.role_id,
            r.role_name,
            r.role_code,
            r.role_desc,
            r.create_time AS rcreate_time,
            r.update_time AS rupdate_time
        FROM
            sys_user AS `user`
            LEFT JOIN sys_user_role AS ur ON ur.user_id = `user`.user_id
            LEFT JOIN sys_role AS r ON r.role_id = ur.role_id
    </sql>

    <sql id="selectSimpleUserVo">
		SELECT
		`user`.user_id,
		`user`.username,
		`user`.`password`,
		`user`.salt,
		`user`.phone,
		`user`.avatar
		FROM
		sys_user AS `user`
	</sql>

    <select id="selectUserVoByUsername" resultMap="userVoResultMap">
        <include refid="selectUserVo"/>
        WHERE `user`.username = #{username}
    </select>

    <select id="selectSimpleUserVoByUsername" resultMap="userVoResultMap">
        <include refid="selectSimpleUserVo"/>
        WHERE `user`.username = #{username}
    </select>

    <select id="selectUserVoById" resultMap="userVoResultMap">
		SELECT
		`user`.user_id,
		`user`.username,
		`user`.`password`,
		`user`.user_realname,
		`user`.sex,
		`user`.email,
		`user`.empno,
		`user`.salt,
		`user`.phone,
		`user`.sign_name,
		`user`.avatar,
		`user`.wx_openid,
		`user`.qq_openid,
		`user`.create_time AS ucreate_time,
		`user`.update_time AS uupdate_time,
		`user`.del_flag AS udel_flag,
		`user`.user_flag AS user_flag,
		r.role_id,
		r.role_name,
		r.role_code,
		r.role_desc,
		r.create_time AS rcreate_time,
		r.update_time AS rupdate_time,
		d.name AS deptName,
		d.dept_id AS deptId,
		p.id AS post_id,
		p.post_name AS post_name,
		p.post_code AS post_code
		FROM
		sys_user AS `user`
		LEFT JOIN sys_user_role AS ur ON ur.user_id = `user`.user_id
		LEFT JOIN sys_role AS r ON r.role_id = ur.role_id
		LEFT JOIN sys_dept AS d ON d.dept_id = `user`.dept_id
		LEFT JOIN sys_user_post AS up ON up.user_id = `user`.user_id
		LEFT JOIN sys_post AS p ON  up.post_id = p.id
		WHERE
		`user`.user_id = #{id}
    </select>

    <select id="selectUserVoPage" resultMap="newUserVoResultMap">
        SELECT
        user_id,
        min(username) as username,
        min( phone ) AS phone,
        min(user_realname) as user_realname,
        min(sex) as sex,
        min(empno) as empno,
        min(dept_id) as dept_id,
        min(dept_name) as dept_name,
        min(user_flag) as user_flag,
        GROUP_CONCAT( post_id ) AS post_id,
        GROUP_CONCAT( post_name ) AS post_name
        FROM (
        SELECT u.user_id, u.username,u.phone, u.user_realname, u.sex, u.empno, u.dept_id, d.name AS dept_name, u.user_flag, p.id
        AS post_id, p.post_name AS post_name
        FROM sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN sys_user_post up ON u.user_id = up.user_id
        LEFT JOIN sys_post p ON up.post_id = p.id
        WHERE 1 = 1 AND u.del_flag= 1 AND d.del_flag = 1 AND u.dept_id IN
        <foreach collection="deptIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="username != null and username != ''">
            AND u.username LIKE CONCAT('%',#{username},'%')
        </if>
        <if test="userSearch != null and userSearch != ''">
            AND u.user_realname LIKE CONCAT('%',#{userSearch},'%')
        </if>
        )t
        GROUP BY user_id
    </select>

    <insert id="insertUserPost" parameterType="com.huazheng.tunny.admin.api.entity.SysUserPost">
		INSERT INTO sys_user_post (user_id,post_id) VALUES (#{userId},#{psotId})
	</insert>

    <delete id="deletePostByUser" parameterType="java.lang.Integer">
		DELETE  FROM sys_user_post WHERE user_id = #{_parameter}
	</delete>

    <select id="selectAuthCode" parameterType="java.lang.String" resultMap="sysUserNewResult">
        SELECT
        user_id,
        username, PASSWORD,
        user_realname,
        sex,
        phone,
        email,
        empno,
        sign_name,
        dept_id,
        salt,
        avatar,
        user_flag,
        password_endtime,
        create_time,
        create_by,
        update_time,
        update_by,
        del_flag,
        wx_openid,
        qq_openid,
        remark,
        admin_remark
        FROM sys_user
        where 1=1 and del_flag = 1
        <if test="email != null and email != ''">
            and email = #{email}
        </if>
        <if test="phone != null and phone != ''">
            and phone = #{phone}
        </if>
    </select>

    <delete id="deleteUserPosDummy" parameterType="java.lang.Integer">
		 delete from sys_user_postdummy where user_id = #{userId}
	</delete>

    <insert id="insertUserPosDummy">
        insert into sys_user_postdummy (user_id,posdummy_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{userId}, #{item})
        </foreach>
    </insert>

    <select id="selectUserByPost" parameterType="java.util.List" resultMap="sysUserNewResult">
        SELECT
        u.user_id,
        u.username,
        u.user_realname,
        u.sex,
        u.phone,
        u.email,
        u.empno,
        u.dept_id,
        u.salt,
        u.avatar,
        u.user_flag,
        u.del_flag,
        u.wx_openid,
        u.qq_openid,
        u.remark
        FROM
        sys_user u
        LEFT JOIN sys_user_post up ON u.user_id = up.user_id
        WHERE
        u.del_flag = '1'
        and up.post_id IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectUserByRole" parameterType="java.util.List" resultMap="sysUserNewResult">
        SELECT
        u.user_id,
        u.username,
        u.user_realname,
        u.sex,
        u.phone,
        u.email,
        u.empno,
        u.dept_id,
        u.salt,
        u.avatar,
        u.user_flag,
        u.del_flag,
        u.wx_openid,
        u.qq_openid,
        u.remark
        FROM
        sys_user u
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        WHERE
        u.del_flag = '1'
        and ur.role_id IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>
