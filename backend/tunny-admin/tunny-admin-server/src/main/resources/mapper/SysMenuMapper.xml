<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huazheng.tunny.admin.mapper.SysMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huazheng.tunny.admin.api.entity.SysMenu">
        <id column="menu_id" property="menuId"/>
        <result column="name" property="name"/>
        <result column="permission" property="permission"/>
        <result column="path" property="path"/>
        <result column="parent_id" property="parentId"/>
        <result column="icons" property="icons"/>
        <result column="component" property="component"/>
        <result column="sort" property="sort"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="outside" property="outside"/>
        <result column="micro_id" property="microId"/> <!-- 子应用ID -->
        <result column="micro_name" property="microName"/> <!-- 子应用中文名称 -->
        <result column="micro_code" property="microCode"/> <!-- 子应用英文名称 -->
        <result column="micro_path" property="microPath"/> <!-- 路由前缀 -->
        <result column="micro_menu_parent_path" property="microMenuParentPath"/> <!-- 子应用父级path -->
        <result column="micro_menu_id" property="microMenuId"/> <!-- 子应用菜单ID -->
        <result column="micro_menu_parent_id" property="microMenuParentId"/> <!-- 子应用菜单父级ID -->
        <result column="router_name" property="routerName"/>
        <result column="is_cache" property="isCache"/>
        <result column="is_hidden" property="isHidden"/>
    </resultMap>

    <resultMap id="MenuVoResultMap" type="com.huazheng.tunny.admin.api.vo.MenuVO">
        <id column="menu_id" property="menuId"/>
        <result column="name" property="name"/>
        <result column="permission" property="permission"/>
        <result column="parent_id" property="parentId"/>
        <result column="icons" property="icons"/>
        <result column="path" property="path"/>
        <result column="component" property="component"/>
        <result column="sort" property="sort"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="parent_name" property="parentName"/>
        <result column="outside" property="outside"/>
        <result column="micro_id" property="microId"/> <!-- 子应用ID -->
        <result column="micro_name" property="microName"/> <!-- 子应用中文名称 -->
        <result column="micro_code" property="microCode"/> <!-- 子应用英文名称 -->
        <result column="micro_path" property="microPath"/> <!-- 路由前缀 -->
        <result column="micro_menu_parent_path" property="microMenuParentPath"/> <!-- 子应用父级path -->
        <result column="micro_menu_id" property="microMenuId"/> <!-- 子应用菜单ID -->
        <result column="micro_menu_parent_id" property="microMenuParentId"/> <!-- 子应用菜单父级ID -->
        <result column="router_name" property="routerName"/>
        <result column="is_cache" property="isCache"/>
        <result column="is_hidden" property="isHidden"/>
    </resultMap>

    <!--通过角色查询菜单信息-->
    <select id="findMenuByRoleCode" resultMap="MenuVoResultMap">
        SELECT
            sys_menu.*
        FROM
        sys_role
        LEFT JOIN sys_role_menu ON sys_role_menu.role_id = sys_role.role_id
        LEFT JOIN sys_menu ON sys_menu.menu_id = sys_role_menu.menu_id
        WHERE
        sys_role.del_flag = 1
        AND sys_menu.del_flag = 1
        AND sys_role.role_code = #{role}
        <if test="microCode != null and  microCode != ''">
            AND micro_code = #{microCode}
        </if>
        ORDER BY sys_menu.sort DESC
    </select>

    <!--通过角色ID 查询权限-->
    <select id="findPermissionsByRoleIds" resultType="java.lang.String">
        SELECT
            m.permission
        FROM
        sys_menu m, sys_role_menu rm WHERE m.menu_id = rm.menu_id AND m.del_flag = 1 AND rm.role_id IN (#{roleIds})
    </select>

    <select id="selectWithMenuVoPage" resultMap="MenuVoResultMap">
        SELECT
        t.menu_id,
        t.name,
        t.permission,
        t.parent_id,
        t.icons,
        t.path,
        t.component,
        t.sort,
        t.TYPE,
        t.create_time,
        t.update_time,
        t.del_flag,
        IFNULL(d.name, '菜单') AS parent_name,
        t.outside,
        t.micro_id,
        t.micro_name,
        t.micro_code,
        t.micro_path,
        t.micro_menu_parent_path,
        t.micro_menu_id,
        t.micro_menu_parent_id,
        t.router_name,
        t.is_cache,
        t.is_hidden
        FROM
        (
        SELECT
        menu_id,
        name,
        permission,
        parent_id,
        icons,
        path,
        component,
        sort, TYPE,
        create_time,
        update_time,
        del_flag,
        outside,
        micro_id,
        micro_name,
        micro_code,
        micro_path,
        micro_menu_parent_path,
        micro_menu_id,
        micro_menu_parent_id,
        router_name,
        is_cache,
        is_hidden
        FROM sys_menu
        WHERE
        1 = 1
        and del_flag = 1
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="dataSearch != null and  dataSearch != ''">
            AND name LIKE CONCAT('%',#{dataSearch},'%')
        </if>
        ) t
        LEFT JOIN sys_menu d ON t.parent_id = d.menu_id
        ORDER BY t.menu_id
    </select>

    <select id="getChildrenOrgOfMenu" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT menu_id FROM sys_menu WHERE FIND_IN_SET(parent_id,getChildrenOrgOfMenu(${_parameter}))
    </select>

    <select id="getParentOrgOfMenu" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		SELECT menu_id FROM sys_menu WHERE FIND_IN_SET(menu_id,getParentOrgOfMenu(${_parameter}))
    </select>

    <select id="getMenuByType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT
		t.menu_id,
		t.name,
		t.permission,
		t.parent_id,
		t.icons,
		t.path,
		t.component,
		t.sort,
		t.TYPE,
		t.create_time,
		t.update_time,
		t.del_flag,
		t.outside,
		t.router_name,
		t.is_cache,
		t.is_hidden
		FROM sys_menu t
		where 1 = 1
		and t.del_flag = 1
		and t.TYPE = #{_parameter}
	</select>


</mapper>
