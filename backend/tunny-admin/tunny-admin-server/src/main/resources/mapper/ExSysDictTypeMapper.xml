<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.ExSysDictTypeMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.SysDictType" id="SysDictTypeResult">
        <result property="id" column="id"/> <!-- 序列 -->
        <result property="name" column="name"/> <!-- 名字 -->
        <result property="description" column="description"/> <!-- 描述 -->
        <result property="sort" column="sort"/> <!-- 排序 -->
        <result property="remarks" column="remarks"/> <!-- 备注 -->
        <result property="parentId" column="parent_id"/> <!-- 上级ID -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标记 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time"/> <!-- 更新时间 -->
    </resultMap>


    <sql id="selectSysDictTypeVo">
        select id, name, description, sort, remarks, parent_id, del_flag, create_time, update_time from sys_dict_type
    </sql>

    <select id="getChildrenOrgOfDictType" parameterType="java.lang.Integer" resultType="java.lang.Integer">
       select id from  sys_dict_type where FIND_IN_SET(id,getChildrenOrgOfDictType(${_parameter})) and del_flag = 1
    </select>
</mapper>
