<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.SysPowerLabelMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.SysPowerLabel" id="sysPowerLabelResult">
        <result property="id" column="id"/> <!--  -->
        <result property="menuId" column="menu_id"/> <!-- 所属菜单ID -->
        <result property="powerName" column="power_name"/> <!-- 权限名称 -->
        <result property="powerTable" column="power_table"/> <!-- 涉及的权限表 -->
        <result property="powerFiled" column="power_filed"/> <!-- 权限字段 -->
        <result property="powerValue" column="power_value"/> <!-- 权限值 -->
        <result property="powerControl"
                column="power_control"/> <!-- 权限控制类型 0：等于 1：不等于 2：包含 3：不包含 4:大于 5：大于等于 6：小于 7：小于等于 -->
        <result property="powerFlag" column="power_flag"/> <!-- 权限状态 0：有效 1：无效 -->
        <result property="powerDesc" column="power_desc"/> <!-- 权限描述 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time"/> <!-- 更新时间 -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标志 0：正常 1：删除 -->
    </resultMap>


    <resultMap type="com.huazheng.tunny.admin.api.vo.SysPowerLabelVO" id="sysPowerLabelResultVO">
        <result property="id" column="id"/> <!--  -->
        <result property="menuId" column="menu_id"/> <!-- 所属菜单ID -->
        <result property="powerName" column="power_name"/> <!-- 权限名称 -->
        <result property="powerTable" column="power_table"/> <!-- 涉及的权限表 -->
        <result property="powerFiled" column="power_filed"/> <!-- 权限字段 -->
        <result property="powerValue" column="power_value"/> <!-- 权限值 -->
        <result property="powerControl"
                column="power_control"/> <!-- 权限控制类型 0：等于 1：不等于 2：包含 3：不包含 4:大于 5：大于等于 6：小于 7：小于等于 -->
        <result property="powerFlag" column="power_flag"/> <!-- 权限状态 0：有效 1：无效 -->
        <result property="powerDesc" column="power_desc"/> <!-- 权限描述 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time"/> <!-- 更新时间 -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标志 0：正常 1：删除 -->
        <result property="menuName" column="menu_name"/> <!--菜单名称-->
    </resultMap>


    <sql id="selectSysPowerLabelVo">
        select id, menu_id, power_name, power_table, power_filed, power_value, power_control, power_flag, power_desc, create_time, update_time, del_flag from sys_power_label
    </sql>
    <!-- 查询对象List -->
    <select id="selectSysPowerLabelList" parameterType="SysPowerLabel" resultMap="sysPowerLabelResult">
        <include refid="selectSysPowerLabelVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSysPowerLabelListByLike" parameterType="SysPowerLabel" resultMap="sysPowerLabelResultVO">
        SELECT
        spl.id,
        spl.menu_id,
        sm.name AS menu_name,
        spl.power_name,
        spl.power_table,
        spl.power_filed,
        spl.power_value,
        spl.power_control,
        spl.power_flag,
        spl.power_desc,
        spl.create_time,
        spl.update_time,
        spl.del_flag
        FROM
        sys_power_label spl
        LEFT JOIN sys_menu sm ON sm.menu_id = spl.menu_id
        WHERE
         1 = 1
        AND spl.del_flag = 1
    </select>

    <!--列表不分页查询-->
    <select id="sysPowerLabelList" resultMap="sysPowerLabelResultVO">
        SELECT
        spl.id,
        spl.menu_id,
        sm.name AS menu_name,
        spl.power_name,
        spl.power_table,
        spl.power_filed,
        spl.power_value,
        spl.power_control,
        spl.power_flag,
        spl.power_desc,
        spl.create_time,
        spl.update_time,
        spl.del_flag
        FROM
        sys_power_label spl
        LEFT JOIN sys_menu sm ON sm.menu_id = spl.menu_id
        WHERE
        1 = 1
        AND spl.del_flag = 1
        <if test="menuId != null and menuId != ''">
            AND spl.menu_id = #{menuId}
        </if>
        <if test="powerName != null and powerName != ''">
            AND spl.power_name like concat('%', #{powerName}, '%')
        </if>
        <if test="powerTable != null and powerTable != ''">
            AND spl.power_table like concat('%', #{powerTable}, '%')
        </if>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSysPowerLabelById" parameterType="Integer" resultMap="sysPowerLabelResult">
        <include refid="selectSysPowerLabelVo"/>
        where id = #{id}
    </select>


    <update id="updateSysPowerLabel" parameterType="SysPowerLabel">
        update sys_power_label
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuId != null  ">menu_id = #{menuId},</if>
            <if test="powerName != null  and powerName != ''  ">power_name = #{powerName},</if>
            <if test="powerTable != null  and powerTable != ''  ">power_table = #{powerTable},</if>
            <if test="powerFiled != null  and powerFiled != ''  ">power_filed = #{powerFiled},</if>
            <if test="powerValue != null  and powerValue != ''  ">power_value = #{powerValue},</if>
            <if test="powerControl != null  and powerControl != ''  ">power_control = #{powerControl},</if>
            <if test="powerFlag != null  and powerFlag != ''  ">power_flag = #{powerFlag},</if>
            <if test="powerDesc != null  and powerDesc != ''  ">power_desc = #{powerDesc},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysPowerLabelById" parameterType="Integer">
        delete from sys_power_label where id = #{id}
    </delete>

    <delete id="deleteSysPowerLabelByIds" parameterType="String">
        delete from sys_power_label where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSysPowerLabel" parameterType="SysPowerLabel" useGeneratedKeys="true" keyProperty="id">
        insert into sys_power_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="menuId != null  ">menu_id,</if>
            <if test="powerName != null  and powerName != ''  ">power_name,</if>
            <if test="powerTable != null  and powerTable != ''  ">power_table,</if>
            <if test="powerFiled != null  and powerFiled != ''  ">power_filed,</if>
            <if test="powerValue != null  and powerValue != ''  ">power_value,</if>
            <if test="powerControl != null  and powerControl != ''  ">power_control,</if>
            <if test="powerFlag != null  and powerFlag != ''  ">power_flag,</if>
            <if test="powerDesc != null  and powerDesc != ''  ">power_desc,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="menuId != null  ">#{menuId},</if>
            <if test="powerName != null  and powerName != ''  ">#{powerName},</if>
            <if test="powerTable != null  and powerTable != ''  ">#{powerTable},</if>
            <if test="powerFiled != null  and powerFiled != ''  ">#{powerFiled},</if>
            <if test="powerValue != null  and powerValue != ''  ">#{powerValue},</if>
            <if test="powerControl != null  and powerControl != ''  ">#{powerControl},</if>
            <if test="powerFlag != null  and powerFlag != ''  ">#{powerFlag},</if>
            <if test="powerDesc != null  and powerDesc != ''  ">#{powerDesc},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!--  -->
        menu_id,  <!-- 所属菜单ID -->
        power_name,  <!-- 权限名称 -->
        power_table,  <!-- 涉及的权限表 -->
        power_filed,  <!-- 权限字段 -->
        power_value,  <!-- 权限值 -->
        power_control,  <!-- 权限控制类型 0：等于 1：不等于 2：包含 3：不包含 4:大于 5：大于等于 6：小于 7：小于等于 -->
        power_flag,  <!-- 权限状态 0：有效 1：无效 -->
        power_desc,  <!-- 权限描述 -->
        create_time,  <!-- 创建时间 -->
        update_time,  <!-- 更新时间 -->
        del_flag  <!-- 删除标志 0：正常 1：删除 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            AND id = #{id}  <!--  -->
        </if>
        <if test="menuId != null ">
            AND menu_id = #{menuId}  <!-- 所属菜单ID -->
        </if>
        <if test="powerName != null and powerName != ''">
            AND power_name = #{powerName}  <!-- 权限名称 -->
        </if>
        <if test="powerTable != null and powerTable != ''">
            AND power_table = #{powerTable}  <!-- 涉及的权限表 -->
        </if>
        <if test="powerFiled != null and powerFiled != ''">
            AND power_filed = #{powerFiled}  <!-- 权限字段 -->
        </if>
        <if test="powerValue != null and powerValue != ''">
            AND power_value = #{powerValue}  <!-- 权限值 -->
        </if>
        <if test="powerControl != null and powerControl != ''">
            AND power_control = #{powerControl}  <!-- 权限控制类型 0：等于 1：不等于 2：包含 3：不包含 4:大于 5：大于等于 6：小于 7：小于等于 -->
        </if>
        <if test="powerFlag != null and powerFlag != ''">
            AND power_flag = #{powerFlag}  <!-- 权限状态 0：有效 1：无效 -->
        </if>
        <if test="powerDesc != null and powerDesc != ''">
            AND power_desc = #{powerDesc}  <!-- 权限描述 -->
        </if>
        <if test="createTime != null ">
            AND create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="updateTime != null ">
            AND update_time = #{updateTime}  <!-- 更新时间 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}  <!-- 删除标志 1：正常 0：删除 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="powerName != null and powerName != ''">
            AND power_name like concat('%', #{powerName}, '%')  <!-- 权限名称 -->
        </if>
        <if test="powerTable != null and powerTable != ''">
            AND power_table like concat('%', #{powerTable}, '%')  <!-- 涉及的权限表 -->
        </if>
        <if test="powerFiled != null and powerFiled != ''">
            AND power_filed like concat('%', #{powerFiled}, '%')  <!-- 权限字段 -->
        </if>
        <if test="powerValue != null and powerValue != ''">
            AND power_value like concat('%', #{powerValue}, '%')  <!-- 权限值 -->
        </if>·
        <if test="powerControl != null and powerControl != ''">
            AND power_control like concat('%', #{powerControl}, '%')  <!-- 权限控制类型 0：等于 1：不等于 2：包含 3：不包含 4:大于 5：大于等于 6：小于 7：小于等于 -->
        </if>
        <if test="powerFlag != null and powerFlag != ''">
            AND power_flag like concat('%', #{powerFlag}, '%')  <!-- 权限状态 0：有效 1：无效 -->
        </if>
        <if test="powerDesc != null and powerDesc != ''">
            AND power_desc like concat('%', #{powerDesc}, '%')  <!-- 权限描述 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag like concat('%', #{delFlag}, '%')  <!-- 删除标志 0：正常 1：删除 -->
        </if>
    </sql>
</mapper>
