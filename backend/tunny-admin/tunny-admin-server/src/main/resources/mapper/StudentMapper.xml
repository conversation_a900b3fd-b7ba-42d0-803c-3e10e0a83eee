<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.StudentMappper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.Student" id="studentMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="age" column="age"/>
        <result property="address" column="address"/>
        <result property="createtime" column="createtime"/>
        <result property="updatetime" column="updatetime"/>
    </resultMap>


</mapper>
