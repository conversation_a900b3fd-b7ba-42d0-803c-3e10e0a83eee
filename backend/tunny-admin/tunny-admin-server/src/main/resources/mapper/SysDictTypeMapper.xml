<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.SysDictTypeMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.SysDictType" id="SysDictTypeResult">
        <result property="id" column="id"/> <!-- 序列 -->
        <result property="name" column="name"/> <!-- 名字 -->
        <result property="description" column="description"/> <!-- 描述 -->
        <result property="sort" column="sort"/> <!-- 排序 -->
        <result property="remarks" column="remarks"/> <!-- 备注 -->
        <result property="parentId" column="parent_id"/> <!-- 上级ID -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标记 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time"/> <!-- 更新时间 -->
    </resultMap>


    <sql id="selectSysDictTypeVo">
        select id, name, description, sort, remarks, parent_id, del_flag, create_time, update_time from sys_dict_type
    </sql>
    <!-- 查询对象List -->
    <select id="selectSysDictTypeList" parameterType="com.huazheng.tunny.admin.api.entity.SysDictType" resultMap="SysDictTypeResult">
        <include refid="selectSysDictTypeVo"/>
        <where>
            <include refid="equal"/>
        </where>
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectSysDictTypeListByLike" parameterType="com.huazheng.tunny.admin.api.entity.SysDictType" resultMap="SysDictTypeResult">
        <include refid="selectSysDictTypeVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectSysDictTypeById" parameterType="Integer" resultMap="SysDictTypeResult">
        <include refid="selectSysDictTypeVo"/>
        where id = #{id}
    </select>


    <update id="updateSysDictType" parameterType="com.huazheng.tunny.admin.api.entity.SysDictType">
        update sys_dict_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null  and name != ''  ">name = #{name},</if>
            <if test="description != null  and description != ''  ">description = #{description},</if>
            <if test="sort != null  ">sort = #{sort},</if>
            <if test="remarks != null  and remarks != ''  ">remarks = #{remarks},</if>
            <if test="parentId != null  ">parent_id = #{parentId},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
            <if test="createTime != null  ">create_time = #{createTime},</if>
            <if test="updateTime != null  ">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysDictTypeById" parameterType="Integer">
        delete from sys_dict_type where id = #{id}
    </delete>

    <delete id="deleteSysDictTypeByIds" parameterType="String">
        delete from sys_dict_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertSysDictType" parameterType="com.huazheng.tunny.admin.api.entity.SysDictType" useGeneratedKeys="true" keyProperty="id">
        insert into sys_dict_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''  ">name,</if>
            <if test="description != null  and description != ''  ">description,</if>
            <if test="sort != null  ">sort,</if>
            <if test="remarks != null  and remarks != ''  ">remarks,</if>
            <if test="parentId != null  ">parent_id,</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="updateTime != null  ">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''  ">#{name},</if>
            <if test="description != null  and description != ''  ">#{description},</if>
            <if test="sort != null  ">#{sort},</if>
            <if test="remarks != null  and remarks != ''  ">#{remarks},</if>
            <if test="parentId != null  ">#{parentId},</if>
            <if test="delFlag != null  and delFlag != ''  ">#{delFlag},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 序列 -->
        name,  <!-- 名字 -->
        description,  <!-- 描述 -->
        sort,  <!-- 排序 -->
        remarks,  <!-- 备注 -->
        parent_id,  <!-- 上级ID -->
        del_flag,  <!-- 删除标记 -->
        create_time,  <!-- 创建时间 -->
        update_time  <!-- 更新时间 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            AND id = #{id}  <!-- 序列 -->
        </if>
        <if test="name != null and name != ''">
            AND name = #{name}  <!-- 名字 -->
        </if>
        <if test="description != null and description != ''">
            AND description = #{description}  <!-- 描述 -->
        </if>
        <if test="sort != null ">
            AND sort = #{sort}  <!-- 排序 -->
        </if>
        <if test="remarks != null and remarks != ''">
            AND remarks = #{remarks}  <!-- 备注 -->
        </if>
        <if test="parentId != null ">
            AND parent_id = #{parentId}  <!-- 上级ID -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}  <!-- 删除标记 -->
        </if>
        <if test="createTime != null ">
            AND create_time = #{createTime}  <!-- 创建时间 -->
        </if>
        <if test="updateTime != null ">
            AND update_time = #{updateTime}  <!-- 更新时间 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="name != null and name != ''">
            AND name like concat('%', #{name}, '%')  <!-- 名字 -->
        </if>
        <if test="description != null and description != ''">
            AND description like concat('%', #{description}, '%')  <!-- 描述 -->
        </if>
        <if test="remarks != null and remarks != ''">
            AND remarks like concat('%', #{remarks}, '%')  <!-- 备注 -->
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag like concat('%', #{delFlag}, '%')  <!-- 删除标记 -->
        </if>
    </sql>
</mapper>
