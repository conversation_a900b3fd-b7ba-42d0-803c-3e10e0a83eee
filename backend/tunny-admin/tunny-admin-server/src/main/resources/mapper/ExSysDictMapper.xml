<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.ExSysDictMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.SysDict" id="sysDictResult">
        <result property="id" column="id"/> <!-- 序列 -->
        <result property="name" column="name"/> <!-- 名称 -->
        <result property="code" column="code"/> <!-- 名称 -->
        <result property="description" column="description"/> <!-- 描述 -->
        <result property="sort" column="sort"/> <!-- 排序 -->
        <result property="remarks" column="remarks"/> <!-- 备注 -->
        <result property="delFlag" column="del_flag"/> <!-- 有效判断 -->
        <result property="dictFlag" column="dict_flag"/> <!-- 有效判断 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time"/> <!-- 更新时间 -->
    </resultMap>


    <sql id="selectSysDictVo">
        select id, code,name, description, sort, remarks, del_flag,dict_flag, create_time, update_time from sys_dict
    </sql>

    <select id="selectDicts" parameterType="java.lang.Integer" resultMap="sysDictResult">
        SELECT
        DISTINCT
        d.id,
        d.code,
        d.name,
        d.description,
        d.sort,
        d.remarks,
        d.del_flag,
        d.dict_flag,
        d.create_time,
        d.update_time
        FROM
        sys_dict d
        LEFT JOIN sys_dict_relevance dr ON dr.dict_id = d.id
        LEFT JOIN sys_dict_index di ON di.id = dr.index_id
        where 1=1
        and d.del_flag = 1
        and di.del_flag = 1
        <if test="dictId != null">
            and dr.index_id = #{dictId}
        </if>
    </select>

    <insert id="insetDictRelevance">
        INSERT INTO sys_dict_relevance (index_id, dict_id) VALUES (#{indexId},#{dictId})
    </insert>
</mapper>
