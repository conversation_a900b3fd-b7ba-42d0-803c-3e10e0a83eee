<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huazheng.tunny.admin.mapper.SysDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huazheng.tunny.admin.api.entity.SysDept">
        <result property="deptId" column="dept_id"/>
        <result property="name" column="name"/>
        <result property="orderNum" column="order_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptSimplify" column="dept_simplify"/>
        <result property="deptLeader" column="dept_leader"/>
        <result property="deptType" column="dept_type"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deptFlag" column="dept_flag"/>
    </resultMap>

    <resultMap id="DeptResultVoMap" type="com.huazheng.tunny.admin.api.vo.DeptVO">
        <result property="deptId" column="dept_id"/>
        <result property="name" column="name"/>
        <result property="orderNum" column="order_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptSimplify" column="dept_simplify"/>
        <result property="deptLeader" column="dept_leader"/>
        <result property="deptType" column="dept_type"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="parentName" column="parent_name"/>
        <result property="deptFlag" column="dept_flag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        dept_id AS deptId, parent_id AS parentId, name, order_num AS orderNum, create_time AS createTime, update_time AS updateTime, del_flag AS delFlag, dept_flag AS deptFlag

    </sql>
    <delete id="deleteDeptRealtion">
        DELETE
        FROM
            sys_dept_relation
        WHERE
            descendant IN (
                SELECT
                    temp.descendant
                FROM
                    (
                        SELECT
                            descendant
                        FROM
                            sys_dept_relation
                        WHERE
                            ancestor = #{id}
                    ) temp
            )
    </delete>

    <!--关联查询部门列表-->
    <select id="selectDeptDtoList" resultType="com.huazheng.tunny.admin.api.entity.SysDept">
		SELECT
			t.*
		FROM
			sys_dept t
		LEFT JOIN sys_dept_relation dr ON t.dept_id = dr.descendant
		WHERE dr.ancestor = 0
	</select>

    <select id="selectDeptVoPage" resultMap="DeptResultVoMap">
        SELECT
        t.dept_id,
        t.NAME,
        t.order_num,
        t.create_time,
        t.update_time,
        t.del_flag,
        t.parent_id,
        t.dept_simplify,
        t.dept_leader,
        t.dept_type,
        t.create_by,
        t.update_by,
        IFNULL( d.NAME, '集团' ) AS parent_name,
        t.dept_flag
        FROM
        (
        SELECT
        dept_id,
        NAME,
        order_num,
        create_time,
        update_time,
        del_flag,
        parent_id,
        dept_simplify,
        dept_leader,
        dept_type,
        create_by,
        update_by,
        dept_flag
        FROM
        sys_dept
        WHERE
        1 = 1
        AND del_flag = 1
        <if test="deptId != null and deptId != ''">
            AND parent_id = #{deptId}
        </if>
        <if test="dataSearch != null and dataSearch != ''">
            AND NAME LIKE CONCAT('%',#{dataSearch},'%')
        </if>
        ) t
        LEFT JOIN sys_dept d ON t.parent_id = d.dept_id
        ORDER BY
        create_time DESC
    </select>

    <select id="getChildrenOrgOfDept" parameterType="java.lang.Integer" resultType="java.lang.String">
--         select dept_id from  sys_dept where FIND_IN_SET(dept_id,getChildrenOrgOfDept(${_parameter})) and del_flag = 1
        select getChildrenOrgOfDept(${_parameter})
    </select>

    <select id="getParentOrgOfDept" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select dept_id from  sys_dept where FIND_IN_SET(dept_id,getParentOrgOfDept(${_parameter})) and del_flag = 1
    </select>
</mapper>
