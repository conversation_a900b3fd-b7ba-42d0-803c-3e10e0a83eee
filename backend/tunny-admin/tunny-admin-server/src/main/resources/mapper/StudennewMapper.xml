<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.tunny.admin.mapper.StudennewMapper">

    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.tunny.admin.api.entity.Studennew" id="studennewResult">
        <result property="id" column="id"/> <!-- 主键 -->
        <result property="name" column="name"/> <!-- 名称 -->
        <result property="age" column="age"/> <!-- 年龄 -->
        <result property="deptId" column="dept_id"/> <!-- 部门ID -->
        <result property="createtime" column="createtime"/> <!-- 创建时间 -->
        <result property="updatetime" column="updatetime"/> <!-- 修改时间 -->
    </resultMap>


    <sql id="selectStudennewVo">
        select id, name, age, dept_id, createtime, updatetime from student_new
    </sql>
    <!-- 查询对象List -->
    <select id="selectStudennewList" parameterType="com.huazheng.tunny.admin.api.entity.Studennew"
            resultMap="studennewResult">
        <include refid="selectStudennewVo"/>
 <!--       <where>
            <include refid="equal"/>
        </where>-->
    </select>

    <!-- 模糊查询对象List -->
    <select id="selectStudennewListByLike" parameterType="com.huazheng.tunny.admin.api.entity.Studennew"
            resultMap="studennewResult">
        <include refid="selectStudennewVo"/>
        <where>
            <include refid="like"/>
        </where>
    </select>

    <!-- 根据主键查询对象 -->
    <select id="selectStudennewById" parameterType="Integer" resultMap="studennewResult">
        <include refid="selectStudennewVo"/>
        where id = #{id}
    </select>


    <update id="updateStudennew" parameterType="com.huazheng.tunny.admin.api.entity.Studennew">
        update student_new
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null  and name != ''  ">name = #{name},</if>
            <if test="age != null  ">age = #{age},</if>
            <if test="deptId != null  and deptId != ''  ">dept_id = #{deptId},</if>
            <if test="createtime != null  ">createtime = #{createtime},</if>
            <if test="updatetime != null  ">updatetime = #{updatetime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStudennewById" parameterType="Integer">
        delete from student_new where id = #{id}
    </delete>

    <delete id="deleteStudennewByIds" parameterType="String">
        delete from student_new where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!-- 新增对象 -->
    <insert id="insertStudennew" parameterType="com.huazheng.tunny.admin.api.entity.Studennew" useGeneratedKeys="true"
            keyProperty="id">
        insert into student_new
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''  ">name,</if>
            <if test="age != null  ">age,</if>
            <if test="deptId != null  and deptId != ''  ">dept_id,</if>
            <if test="createtime != null  ">createtime,</if>
            <if test="updatetime != null  ">updatetime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''  ">#{name},</if>
            <if test="age != null  ">#{age},</if>
            <if test="deptId != null  and deptId != ''  ">#{deptId},</if>
            <if test="createtime != null  ">#{createtime},</if>
            <if test="updatetime != null  ">#{updatetime},</if>
        </trim>
    </insert>


    <!-- 表字段 -->
    <sql id="column">
        id,  <!-- 主键 -->
        name,  <!-- 名称 -->
        age,  <!-- 年龄 -->
        dept_id,  <!-- 地址 -->
        createtime,  <!-- 创建时间 -->
        updatetime  <!-- 修改时间 -->
    </sql>

    <!-- Where精确匹配字段 -->
    <sql id="equal">
        <if test="id != null ">
            AND id = #{id}  <!-- 主键 -->
        </if>
        <if test="name != null and name != ''">
            AND name = #{name}  <!-- 名称 -->
        </if>
        <if test="age != null ">
            AND age = #{age}  <!-- 年龄 -->
        </if>
        <if test="deptId != null and deptId != ''">
            AND dept_id = #{deptId}  <!-- 地址 -->
        </if>
        <if test="createtime != null ">
            AND createtime = #{createtime}  <!-- 创建时间 -->
        </if>
        <if test="updatetime != null ">
            AND updatetime = #{updatetime}  <!-- 修改时间 -->
        </if>
    </sql>

    <!-- Where模糊匹配字段 -->
    <sql id="like">
        <if test="name != null and name != ''">
            AND name like concat('%', #{name}, '%')  <!-- 名称 -->
        </if>
        <if test="deptId != null and deptId != ''">
            AND dept_id like concat('%', #{deptId}, '%')  <!-- 地址 -->
        </if>
    </sql>
</mapper>
