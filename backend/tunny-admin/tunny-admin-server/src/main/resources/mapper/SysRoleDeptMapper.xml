<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huazheng.tunny.admin.mapper.SysRoleDeptMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.huazheng.tunny.admin.api.entity.SysRoleDept">
		<id column="id" property="id"/>
		<result column="role_id" property="roleId"/>
		<result column="dept_id" property="deptId"/>
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
        id, role_id AS roleId, dept_id AS deptId
    </sql>

</mapper>
