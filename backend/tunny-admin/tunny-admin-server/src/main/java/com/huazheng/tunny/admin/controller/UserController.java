package com.huazheng.tunny.admin.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.enums.SqlLike;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.admin.api.dto.UserDTO;
import com.huazheng.tunny.admin.api.dto.UserInfo;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.admin.api.entity.SysUserPost;
import com.huazheng.tunny.admin.api.entity.SysUserRole;
import com.huazheng.tunny.admin.api.vo.UserVO;
import com.huazheng.tunny.admin.service.SysUserService;
import com.huazheng.tunny.common.core.constant.CommonConstant;
import com.huazheng.tunny.common.core.constant.SecurityConstants;
import com.huazheng.tunny.common.core.constant.enums.EnumLoginType;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.log.annotation.SysLog;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.tools.file.AttackmentDto;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;


@RestController
@RequestMapping("/user")
public class UserController {
    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();
    @Autowired
    private SysUserService userService;
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private  RedisConnectionFactory redisConnectionFactory;
    /**
     * 获取当前用户信息（角色、权限）
     * 并且异步初始化用户部门信息
     *
     * @param from     请求标志，该接口会被 auth、 前端调用
     * @param username 用户名
     * @return 用户名
     */
    @GetMapping(value = {"/info", "/info/{username}"})
    public R<UserInfo> user(@PathVariable(required = false) String username,
                            @RequestHeader(required = false) String from) {
        // 查询用户不为空时判断是不是内部请求
        if (StrUtil.isNotBlank(username) && !StrUtil.equals(SecurityConstants.FROM_IN, from)) {
            return new R<>(null, "error");
        }
        //为空时查询当前用户
        if (StrUtil.isBlank(username)) {
            username = SecurityUtils.getUser();
        }

        return new R<>(userService.findUserInfo(EnumLoginType.PWD.getType(), username));
    }

    /**
     * 根据手机号获取当前用户信息（角色、权限）
     * 并且异步初始化用户部门信息
     *
     * @param from   请求标志，该接口会被 auth、 前端调用
     * @param mobile 用户名
     * @return 用户名
     */
    @GetMapping(value = {"/mobileinfo", "/mobileinfo/{mobile}"})
    public R<UserInfo> mobileuser(@PathVariable(required = false) String mobile,
                                  @RequestHeader(required = false) String from) {
        // 查询用户不为空时判断是不是内部请求
        if (StrUtil.isNotBlank(mobile) && !StrUtil.equals(SecurityConstants.FROM_IN, from)) {
            return new R<>(null, "error");
        }
        //为空时查询当前用户
        if (StrUtil.isBlank(mobile)) {
            mobile = SecurityUtils.getUser();
        }

        return new R<>(userService.findUserInfo(EnumLoginType.MOBILE.getType(), mobile));
    }


    /**
     * 通过ID查询当前用户信息
     *
     * @param id ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    public UserVO user(@PathVariable Integer id) {
        return userService.selectUserVoById(id);
    }

    /**
     * 删除用户信息
     *
     * @param id ID
     * @return R
     */
    @SysLog("删除用户信息")
    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('sys_user_del')")
    @ApiOperation(value = "删除用户", notes = "根据ID删除用户")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "int", paramType = "path", example = "1000")
    public R<Boolean> userDel(@PathVariable Integer id) {
        SysUser sysUser = userService.selectById(id);

        return new R<>(userService.deleteUserById(sysUser));
    }

    /**
     * 添加用户
     *
     * @param userDto 用户信息
     * @return success/false
     */
    @SysLog("添加用户")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sys_user_add')")
    public R<Boolean> user(@RequestBody UserDTO userDto) {
        SysUser sysUser = new SysUser();
        BeanUtils.copyProperties(userDto, sysUser);
        sysUser.setDelFlag(CommonConstant.STATUS_NORMAL);
        //sysUser.setPassword(ENCODER.encode(userDto.getPassword()));
        sysUser.setPassword(SecureUtil.md5((String) userDto.getPassword()));
        userService.insert(sysUser);

        List<Integer> roles = userDto.getRoles();
        if (roles != null && roles.size() > 0) {
            for (Integer roleId : roles) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(sysUser.getUserId());
                userRole.setRoleId(roleId);
                userRole.insert();
            }
        }
        List<Integer> posts = userDto.getPosts();
        if (posts != null && posts.size() > 0) {
            for (Integer postId : posts) {
                SysUserPost userPost = new SysUserPost();
                userPost.setPsotId(postId);
                userPost.setUserId(sysUser.getUserId());
                userService.insertUserPost(userPost);
            }
        }
        return new R<>(Boolean.TRUE);
    }

    /**
     * 更新用户信息
     *
     * @param userDto 用户信息
     * @return R
     */
    @PutMapping
    @PreAuthorize("@pms.hasPermission('sys_user_edit')")
    public R<Boolean> userUpdate(@RequestBody UserDTO userDto) {
        SysUser user = userService.selectById(userDto.getUserId());
        return new R<>(userService.updateUser(userDto, user.getUsername()));
    }

    /**
     * 分页查询用户
     *
     * @param params 参数集
     * @return 用户集合
     */
    @GetMapping("/userPage")
    public Page userPage(@RequestParam Map<String, Object> params) {
        return userService.selectWithRolePage(new Query(params));
    }

    /**
     * 修改个人信息
     *
     * @param userDto userDto
     * @return success/false
     */
    @PutMapping("/editInfo")
    public R<Boolean> editInfo(@RequestBody UserDTO userDto) {
        return userService.updateUserInfo(userDto, SecurityUtils.getUser());
    }

    /**
     * 设置用户虚拟岗位
     *
     * @param userId
     * @param posDummys
     * @return
     */
    @GetMapping("/userPosDummy")
    public R userPosDummy(@RequestParam() Integer userId,
                          @RequestParam() String posDummys) {

        userService.userPosDummy(userId, posDummys);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 查询用户列表
     *
     * @param searchUser
     * @return
     */
    @PostMapping(value = {"/selectUserList"})
    public List selectUserList(@RequestParam("searchUser") String searchUser) {
        SysUser user = new SysUser();
        user.setUserFlag("1");
        user .setDelFlag(CommonConstant.STATUS_NORMAL); 
        EntityWrapper<SysUser> wrapper = new EntityWrapper<SysUser>(user);
        if (searchUser != null && !"".equals(searchUser)) {
            wrapper.and().like("user_realname", searchUser, SqlLike.DEFAULT);
        }
        List<SysUser> users = userService.selectList(wrapper);
        if (users != null && users.size() > 0) {
            for (SysUser s : users) {
                s.setPassword(null);
            }
        }
        return users;
    }


    /**
     * 根据code查询用户
     *
     * @param code
     * @return
     */
    @GetMapping("/getUserByCode")
    public SysUser getUserByCode(@RequestParam("code") String code) {
        SysUser query = new SysUser();
        query.setEmpno(code);
        EntityWrapper<SysUser> wrapper = new EntityWrapper<SysUser>(query);
        return userService.selectOne(wrapper);
    }

    /**
     * 根据用户ID列表获取用户列表
     *
     * @param codes
     * @return
     */
    @GetMapping("/getUserListByCodes")
    public List<SysUser> getUserListByCodes(@RequestParam("codes") List<String> codes) {
        EntityWrapper<SysUser> wrapper = new EntityWrapper<SysUser>();
        return userService.selectList(wrapper.in("empno", codes));
    }

    /**
     * 上传个人电子签名
     *
     * @param files
     * @return
     */
    @PostMapping("/uploadSignName")
    public AttackmentDto uploadSignName(@RequestParam("file") MultipartFile[] files) {
        AttackmentDto dto = new AttackmentDto();
        try {
            if (files != null) {
                dto = userService.uploadSignName(files);
            } else {
                dto.setCode(500);
                dto.setMsg("电子签名上传异常");
            }
            return dto;
        } catch (Exception e) {
            dto.setCode(500);
            dto.setMsg("电子签名上传异常");
            return dto;
        }
    }

    /**
     * 查询所有在线用户
     *
     * @param
     * @return
     */
    @GetMapping("/selectOnlineUser")
    public R<List<SecruityUser>> selectOnlineUser() {
        RedisTokenStore tokenStore = new RedisTokenStore(redisConnectionFactory);
        tokenStore.setPrefix(SecurityConstants.TUNNY_PREFIX + SecurityConstants.OAUTH_PREFIX);
        return new R<>(new SecurityUtils().getAllOnlineUser(redisTemplate, tokenStore));
    }


}
