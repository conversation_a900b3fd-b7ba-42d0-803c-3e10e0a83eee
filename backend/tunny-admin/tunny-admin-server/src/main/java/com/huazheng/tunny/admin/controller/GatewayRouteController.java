package com.huazheng.tunny.admin.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.admin.api.common.PageParams;
import com.huazheng.tunny.admin.api.entity.GatewayRoute;
import com.huazheng.tunny.admin.api.feign.RemoteGatewayService;
import com.huazheng.tunny.admin.api.dto.GatewayRouteDto;
import com.huazheng.tunny.admin.service.GatewayRouteService;
import com.huazheng.tunny.common.core.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


@RestController
@RequestMapping("/gateway/route")
public class GatewayRouteController {

    @Autowired
    private RemoteGatewayService remoteGatewayService;

    @Autowired
    private GatewayRouteService gatewayRouteService;

    /**
     * 刷新路由配置
     *
     *
     * @return
     */
    @GetMapping("/refresh")
    public String refresh() throws Exception {
        return remoteGatewayService.refresh();
    }

    /**
     * 路由记录列表
     *
     *
     * @return
     */
    @GetMapping("/page")
    public Page<GatewayRoute> page(@RequestParam Map<String, Object> params) throws Exception {
        return gatewayRouteService.findListPage(new PageParams(params));
    }

    /**
     * 增加路由记录
     *
     *
     * @return
     */
    @Transactional
    @PostMapping("/add")
    public String add(@RequestBody GatewayRouteDto gatewayRouteDto) throws Exception {
        gatewayRouteService.add(gatewayRouteDto);
        return "success";
    }

    @Transactional
    @PostMapping("/update")
    public String update(@RequestBody GatewayRouteDto gatewayRouteDto) throws Exception {
        gatewayRouteService.update(gatewayRouteDto);
        return "success";
    }

    @GetMapping("/delete")
    public String delete(@RequestParam String id, @RequestParam String serviceId) {
        try {
            GatewayRoute gatewayRoute = new GatewayRoute();
            gatewayRoute.setRouteId(Long.parseLong(id));
            gatewayRoute.setDelFlag(CommonConstant.STATUS_DEL);

            gatewayRoute.setServiceId(serviceId);

            remoteGatewayService.deleteRoute(gatewayRoute);
            gatewayRouteService.updateById(gatewayRoute);

            return "success";
        }catch (Exception e){
            e.printStackTrace();
            return "faild";
        }
    }

    @GetMapping("/info")
    public GatewayRoute info(@RequestParam String id) throws Exception {
        return gatewayRouteService.find(id);
    }

}
