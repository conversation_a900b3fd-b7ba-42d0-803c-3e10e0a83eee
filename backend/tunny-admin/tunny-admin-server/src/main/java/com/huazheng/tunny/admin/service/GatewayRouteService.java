package com.huazheng.tunny.admin.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.admin.api.common.PageParams;
import com.huazheng.tunny.admin.api.entity.GatewayRoute;
import com.huazheng.tunny.admin.api.dto.GatewayRouteDto;

/**
 * 路由管理
 *
 *
 */
public interface GatewayRouteService extends IService<GatewayRoute> {

    /**
     * 分页查询
     *
     * @param pageParams
     * @return
     */
    Page<GatewayRoute> findListPage(PageParams pageParams);

    public Integer add(GatewayRouteDto gatewayRouteDto);

    public Integer update(GatewayRouteDto gatewayRouteDto);

    public Integer delete(String id);

    public GatewayRoute find(String id);
}
