package com.huazheng.tunny.admin.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.admin.api.dto.MenuTree;
import com.huazheng.tunny.admin.api.dto.TreeNode;
import com.huazheng.tunny.admin.api.entity.SysMenu;
import com.huazheng.tunny.admin.api.entity.SysMicro;
import com.huazheng.tunny.admin.api.entity.SysMicroMenu;
import com.huazheng.tunny.admin.api.vo.MenuVO;
import com.huazheng.tunny.admin.api.vo.TreeUtil;
import com.huazheng.tunny.admin.service.SysMenuService;
import com.huazheng.tunny.admin.service.SysMicroMenuService;
import com.huazheng.tunny.admin.service.SysMicroService;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.StringUtils;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.common.core.constant.CommonConstant;
import com.huazheng.tunny.common.core.util.R;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2017/10/31
 */
@RestController
@RequestMapping("/menu")
public class MenuController {
    @Autowired
    private SysMenuService sysMenuService;
    @Autowired
    private SysMicroService sysMicroService;

    @Autowired
    private SysMicroMenuService sysMicroMenuService;

    /**
     * 通过角色名称查询用户菜单
     *
     * @param role 角色名称
     * @return 菜单列表
     */
    @GetMapping("/findMenuByRole/{role}")
    public List<MenuVO> findMenuByRole(@PathVariable String role) {
        return sysMenuService.findMenuByRoleCode(role, null);
    }

    /**
     * 返回当前用户的树形菜单集合
     *
     * @return 当前用户的树形菜单
     */
    @GetMapping(value = "/userMenu")
    public List<MenuTree> userMenu(@RequestParam(name = "microCode", required = false) String microCode) {
        // 获取符合条件得菜单
        Set<MenuVO> all = new HashSet<>();
        List<String> roles = SecurityUtils.getRoles();
        if (roles != null && roles.size() > 0) {
            for (String roleName : roles) {
                List<MenuVO> menu = sysMenuService.findMenuByRoleCode(roleName, microCode);
                all.addAll(menu);
            }
        }
        List<MenuTree> menuTreeList = new ArrayList<>();
        all.forEach(menuVo -> {
            if (!CommonConstant.BUTTON.equals(menuVo.getType())) {
                menuTreeList.add(new MenuTree(menuVo));
            }
        });
        CollUtil.sort(menuTreeList, Comparator.comparingInt(MenuTree::getSort));
        return TreeUtil.bulid(menuTreeList, -1);
    }

    /**
     * 返回树形菜单集合
     *
     * @return 树形菜单
     */
    @GetMapping(value = "/allTree")
    public List<MenuTree> getTree() {
        SysMenu condition = new SysMenu();
        condition .setDelFlag(CommonConstant.STATUS_NORMAL); 
        List<SysMenu> sysMenus = sysMenuService.selectList(new EntityWrapper<>(condition));
        return TreeUtil.bulidTree(sysMenus, -1);
    }

    /**
     * 返回角色的菜单集合
     *
     * @param roleName 角色名称
     * @return 属性集合
     */
    @GetMapping("/roleTree/{roleName}")
    public List<MenuTree> roleTree(@PathVariable String roleName) {
        SysMenu condition = new SysMenu();
        condition.setDelFlag(CommonConstant.STATUS_NORMAL);
        List<MenuTree> trees = TreeUtil.bulidTree(sysMenuService.selectList(new EntityWrapper<>(condition)), -1);

        List<MenuVO> menus = sysMenuService.findMenuByRoleCode(roleName, null);
        List<Integer> menuList = new ArrayList<>();
        for (MenuVO menuVo : menus) {
            menuList.add(menuVo.getMenuId());
        }
        if (menuList != null && menuList.size() > 0 && trees != null && trees.size() > 0) {
            trees = menuSelected(trees, menuList);
        }
        return trees;
    }

    public <T extends TreeNode> List<T> menuSelected(List<T> trees, List<Integer> menuList) {
        for (TreeNode menuTree : trees) {
            for (Integer integer : menuList) {
                if (menuTree.getId() == integer) {
                    menuTree.setChecked(true);
                }
            }
            List<TreeNode> children = menuTree.getChildren();
            if (children != null && children.size() > 0) {
                for (TreeNode treeNode : children) {
                    menuSelected(children, menuList);
                }
            }
        }
        return trees;
    }

    /**
     * 通过ID查询菜单的详细信息
     *
     * @param id 菜单ID
     * @return 菜单详细信息
     */
    @GetMapping("/{id}")
    public MenuVO menu(@PathVariable Integer id) {
        MenuVO menuVO = new MenuVO();
        SysMenu sysMenu = sysMenuService.selectById(id);
        if (sysMenu != null) {
            List list = sysMenuService.getParentOrgOfMenu(id);
            BeanUtils.copyProperties(sysMenu, menuVO);
            menuVO.setMenuList(list);
        }
        return menuVO;
    }

    /**
     * 新增菜单
     *
     * @param sysMenu 菜单信息
     * @return success/false
     */
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('sys_menu_add')")
    public R<Boolean> menu(@RequestBody SysMenu sysMenu) {
        return new R<>(sysMenuService.insert(sysMenu));
    }

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return success/false
     * TODO  级联删除下级节点
     */
    @DeleteMapping("/{id}")
//    @PreAuthorize("@pms.hasPermission('sys_menu_del')")
    public R<Boolean> menuDel(@PathVariable Integer id) {
        return new R<>(sysMenuService.deleteMenu(id));
    }


    @PutMapping
//    @PreAuthorize("@pms.hasPermission('sys_menu_edit')")
    public R<Boolean> menuUpdate(@RequestBody SysMenu sysMenu) {
        return new R<>(sysMenuService.updateMenuById(sysMenu));
    }

    /**
     * 　　* @Description: 分页查询菜单
     * 　　* @param
     * 　　* @return
     * 　　* <AUTHOR>
     * 　　* @date 2019/1/30 17:23
     */
    @GetMapping("/menuPage")
    public Page menuPage(@RequestParam Map<String, Object> params) {
        return sysMenuService.selectWithMenuVoPage(new Query(params));
    }

    /**
     * 　　* @Description: 根据编码查询父ID集合
     * 　　* @param
     * 　　* @return
     * 　　* <AUTHOR>
     * 　　* @date 2019/2/12 17:12
     */
    @GetMapping("/getParentOrgOfMenu")
    public List getParentOrgOfMenu(@RequestParam Map<String, Object> params) {
        int parentId = Integer.parseInt((String) params.get("parentId"));
        return sysMenuService.getParentOrgOfMenu(parentId);
    }

    /**
     * 根据类型查询菜单
     *
     * @param menuType
     * @return
     */
    @GetMapping("/getMenuByType")
    public List getMenuByType(@RequestParam(required = true) Integer menuType) {
        return sysMenuService.getMenuByType(menuType);
    }

    /**
     * 子应用类型新增菜单
     *
     * @param obj 菜单信息
     * @return success/false
     */
    @Transactional
    @PostMapping("/microMenuAdd")
    public R<Boolean> microMenuAdd(@RequestBody Map<String, Object> obj) {
        //获取子应用信息
        Integer microId = (Integer) obj.get("microId");
        if (microId == null) {
            return new R<>(Boolean.FALSE, "子应用为空！");
        }
        SysMicro sysMicro = sysMicroService.selectSysMicroById(microId);

        //从接口参数中提取目录ID和菜单ID列表
        List<List<Integer>> microMenuList = (List<List<Integer>>) obj.get("microMenuList");
        List<Integer> microMenuPids = new ArrayList<>();
        List<Integer> microMenuIds = new ArrayList<>();
        for (int i = 0; i < microMenuList.size(); i++) {
            List<Integer> microMenuIdGroup = microMenuList.get(i);
            microMenuPids.add(microMenuIdGroup.get(0));
            microMenuIds.add(microMenuIdGroup.get(microMenuIdGroup.size() - 1));
        }

        //查询子应用目录信息
        List<SysMicroMenu> directoryList = null;
        List<SysMenu> dirMenuList = new ArrayList<>();
        if (microMenuPids.size() > 0) {
            directoryList = sysMicroMenuService.selectList(new EntityWrapper<SysMicroMenu>().in("menu_id", microMenuPids));
            for (int i = 0; i < directoryList.size(); i++) {
                SysMicroMenu sysMicroMenu = directoryList.get(i);
                //构造子应用的目录，用于构建子应用界面的菜单，作为隐藏目录存在
                SysMenu sysMenu = new SysMenu();
                sysMenu.setName(sysMicroMenu.getName());
                sysMenu.setPermission(sysMicroMenu.getPermission());
                sysMenu.setPath(sysMicroMenu.getPath());
                sysMenu.setParentId(-1);
                sysMenu.setIcons(sysMicroMenu.getIcons());
                sysMenu.setComponent(sysMicroMenu.getComponent());
                sysMenu.setSort(sysMicroMenu.getSort());
                sysMenu.setType("0");
                sysMenu.setOutside(sysMicroMenu.getOutside());
                sysMenu.setMicroId(sysMicro.getMicroId());
                sysMenu.setMicroName(sysMicro.getMicroName());
                sysMenu.setMicroCode(sysMicro.getMicroCode());
                sysMenu.setMicroPath(sysMicro.getMicroPath());
                sysMenu.setMicroMenuParentPath(null);
                sysMenu.setMicroMenuId(sysMicroMenu.getMenuId());
                sysMenu.setMicroMenuParentId(sysMicroMenu.getParentId());
                dirMenuList.add(sysMenu);
            }
            //根据目录ID的集合排查之前是否已有数据存库，并返回menuId
            List<SysMenu> oldData = sysMenuService.selectList(new EntityWrapper<SysMenu>().setSqlSelect("menu_id, micro_menu_id").eq("del_flag", "1").in("micro_menu_id", microMenuPids));
            for (int i = 0; i < oldData.size(); i++) {
                for (int j = 0; j < dirMenuList.size(); j++) {
                    if (oldData.get(i).getMicroMenuId() == dirMenuList.get(j).getMicroMenuId()) {
                        dirMenuList.get(j).setMenuId(oldData.get(i).getMenuId());
                    }
                }
            }
            sysMenuService.insertOrUpdateBatch(dirMenuList);
        }

        //查询子应用菜单信息
        List<SysMicroMenu> menulist = null;
        List<SysMenu> childMenunList = new ArrayList<>();
        if (microMenuIds.size() > 0) {
            menulist = sysMicroMenuService.selectList(new EntityWrapper<SysMicroMenu>().in("menu_id", microMenuIds));
            for (int i = 0; i < menulist.size(); i++) {
                SysMicroMenu sysMicroMenu = menulist.get(i);
                //构造子应用的菜单
                SysMenu sysMenu = new SysMenu();
                sysMenu.setName(sysMicroMenu.getName());
                sysMenu.setPermission(sysMicroMenu.getPermission());
                sysMenu.setPath(sysMicroMenu.getPath());
                sysMenu.setIcons(sysMicroMenu.getIcons());
                sysMenu.setComponent(sysMicroMenu.getComponent());
                sysMenu.setSort(sysMicroMenu.getSort());
                sysMenu.setType("0");
                sysMenu.setOutside(sysMicroMenu.getOutside());
                sysMenu.setMicroId(sysMicro.getMicroId());
                sysMenu.setMicroName(sysMicro.getMicroName());
                sysMenu.setMicroCode(sysMicro.getMicroCode());
                sysMenu.setMicroPath(sysMicro.getMicroPath());
                sysMenu.setMicroMenuId(sysMicroMenu.getMenuId());
                sysMenu.setMicroMenuParentId(sysMicroMenu.getParentId());
                for (int j = 0; j < dirMenuList.size(); j++) {
                    if (dirMenuList.get(j).getMicroMenuId() == sysMicroMenu.getParentId()) {
                        sysMenu.setParentId(dirMenuList.get(j).getMenuId());
                        sysMenu.setMicroMenuParentPath(dirMenuList.get(j).getPath());
                    }
                }
                childMenunList.add(sysMenu);
            }
            //根据菜单ID的集合排查之前是否已有数据存库，并返回menuId
            List<SysMenu> oldData = sysMenuService.selectList(new EntityWrapper<SysMenu>().setSqlSelect("menu_id, micro_menu_id").eq("del_flag", "1").in("micro_menu_id", microMenuIds));
            for (int i = 0; i < oldData.size(); i++) {
                for (int j = 0; j < childMenunList.size(); j++) {
                    if (oldData.get(i).getMicroMenuId() == childMenunList.get(j).getMicroMenuId()) {
                        childMenunList.get(j).setMenuId(oldData.get(i).getMenuId());
                    }
                }
            }
            sysMenuService.insertOrUpdateBatch(childMenunList);
        }
        return new R<>(Boolean.TRUE);
    }

}
