package com.huazheng.tunny.admin.service;


import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.admin.api.entity.JobAndTrigger;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.common.core.util.Query;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface JobAndTriggerService  extends IService<JobAndTrigger> {
	/**
	 * 分页查询定时任务信息（含有触发器信息）
	 *
	 * @param query 查询条件
	 * @return
	 */
	Page getJobAndTriggerDetails(Query query);

	/**
	 * 根据任务类名查找任务信息
	 * @param jobClassName
	 * @return
	 */
	List<JobAndTrigger> getJobByClassName( Object jobClassName );
}
