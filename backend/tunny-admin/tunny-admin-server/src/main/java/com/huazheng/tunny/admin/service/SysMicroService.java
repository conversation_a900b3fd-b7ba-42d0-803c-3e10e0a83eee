package com.huazheng.tunny.admin.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.admin.api.entity.SysMicro;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 子应用 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2022-06-15 17:20:52
 */
public interface SysMicroService extends IService<SysMicro> {
    /**
     * 查询子应用信息
     *
     * @param microId 子应用ID
     * @return 子应用信息
     */
    public SysMicro selectSysMicroById(Integer microId);

    /**
     * 查询子应用列表
     *
     * @param sysMicro 子应用信息
     * @return 子应用集合
     */
    public List<SysMicro> selectSysMicroList(SysMicro sysMicro);


    /**
     * 分页模糊查询子应用列表
     * @return 子应用集合
     */
    public Page selectSysMicroListByLike(Query query);



    /**
     * 新增子应用
     *
     * @param sysMicro 子应用信息
     * @return 结果
     */
    public int insertSysMicro(SysMicro sysMicro);

    /**
     * 修改子应用
     *
     * @param sysMicro 子应用信息
     * @return 结果
     */
    public int updateSysMicro(SysMicro sysMicro);

    /**
     * 删除子应用
     *
     * @param microId 子应用ID
     * @return 结果
     */
    public int deleteSysMicroById(Integer microId);

    /**
     * 批量删除子应用
     *
     * @param microIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysMicroByIds(Integer[] microIds);

}

