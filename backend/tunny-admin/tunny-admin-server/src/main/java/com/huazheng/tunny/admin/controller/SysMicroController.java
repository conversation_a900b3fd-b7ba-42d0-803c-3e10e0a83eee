package com.huazheng.tunny.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.admin.api.entity.SysMicro;
import com.huazheng.tunny.admin.service.SysMicroService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.constant.CommonConstant;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.listener.EasyExcelListener;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 子应用
 *
 * <AUTHOR> code generator
 * @date 2022-06-15 17:20:52
 */
@Slf4j
@RestController
@RequestMapping("/micro")
public class SysMicroController {

    @Autowired
    private SysMicroService sysMicroService;

    /**
     * 分页
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //对象模糊查询
        return sysMicroService.selectSysMicroListByLike(new Query<>(params));
    }


    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/list")
    public List list(@RequestParam Map<String, Object> params) {
        SysMicro sysMicro =  BeanUtil.mapToBean(params, SysMicro.class,false);
        sysMicro .setDelFlag(CommonConstant.STATUS_NORMAL);
        sysMicro.setMicroState(1);
        return sysMicroService.selectSysMicroList(sysMicro);
    }

    /**
     * 信息
     *
     * @param microId
     * @return R
     */
    @GetMapping("/{microId}")
    public R info(@PathVariable("microId") Integer microId) {
        SysMicro sysMicro = sysMicroService.selectOne(new EntityWrapper<SysMicro>().eq("micro_id", microId).eq("del_flag", "1"));
        return new R<>(sysMicro);
    }

    /**
     * 保存
     *
     * @param sysMicro
     * @return R
     */
    @PostMapping("/add")
    public R save(@RequestBody SysMicro sysMicro) {
        sysMicro.setCreateBy(SecurityUtils.getUser());
        sysMicro.setUpdateBy(SecurityUtils.getUser());
        List<SysMicro> repeatData = sysMicroService.selectList(new EntityWrapper<SysMicro>().eq("micro_code", sysMicro.getMicroCode()).eq("del_flag", "1"));
        if(repeatData.size()>0){
            return new R<>(Boolean.FALSE, "英文名称已存在！");
        }
        List<SysMicro> repeatData1 = sysMicroService.selectList(new EntityWrapper<SysMicro>().eq("micro_path", sysMicro.getMicroPath()).eq("del_flag", "1"));
        if(repeatData1.size()>0){
            return new R<>(Boolean.FALSE, "路由前缀已存在！");
        }
        sysMicroService.insert(sysMicro);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param sysMicro
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody SysMicro sysMicro) {
        sysMicro.setUpdateBy(SecurityUtils.getUser());
        sysMicroService.updateById(sysMicro);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param microId
     * @return R
     */
    @GetMapping("/del")
    public R delete(@RequestParam Integer microId) {
        sysMicroService.updateForSet("del_flag = 0", new EntityWrapper<SysMicro>().eq("micro_id", microId));
        return new R<>(Boolean.TRUE);
    }

}
