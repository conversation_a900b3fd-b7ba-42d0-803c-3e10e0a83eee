package com.huazheng.tunny.admin.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.admin.api.dto.UserDTO;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.admin.api.entity.SysUserPost;
import com.huazheng.tunny.admin.api.vo.UserVO;
import com.huazheng.tunny.common.core.util.Query;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysUserMapper extends BaseMapper<SysUser> {
    /**
     * 通过用户名查询用户信息（含有角色信息）
     *
     * @param username 用户名
     * @return userVo
     */
    UserVO selectUserVoByUsername(String username);

    /**
     * 通过用户名查询用户信息（含有角色信息）
     *
     * @param username 用户名
     * @return userVo
     */
    UserVO selectSimpleUserVoByUsername(String username);

    /**
     * 分页查询用户信息（含角色）
     *
     * @param query    查询条件
     * @param username 用户名
     * @return list
     */
    List selectUserVoPage(Query query, @Param("username") Object username, @Param("deptIds") List deptIds, @Param("userSearch") Object userSearch);

    /**
     * 通过ID查询用户信息
     *
     * @param id 用户ID
     * @return userVo
     */
    UserVO selectUserVoById(Integer id);

    void insertUserPost(SysUserPost userPost);

    void deletePostByUser(Integer userId);

    SysUser selectAuthCode(@Param("email") String email, @Param("phone") String phone);

    void insertUserPosDummy(@Param("userId") Integer userId, @Param("list") List posDummys);

    void deleteUserPosDummy(@Param("userId") Integer userId);

    List selectUserByPost(List<Integer> list);

    List selectUserByRole(List<Integer> list);
}
