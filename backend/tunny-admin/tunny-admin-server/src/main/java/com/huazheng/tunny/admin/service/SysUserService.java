package com.huazheng.tunny.admin.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.admin.api.dto.UserDTO;
import com.huazheng.tunny.admin.api.dto.UserInfo;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.admin.api.entity.SysUserPost;
import com.huazheng.tunny.admin.api.vo.UserVO;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.tools.file.AttackmentDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017/10/31
 */
public interface SysUserService extends IService<SysUser> {
    /**
     * 查询用户信息
     *
     * @param type     类型
     * @param username 用户名
     * @return userInfo
     */
    UserInfo findUserInfo(String type, String username);

    /**
     * 分页查询用户信息（含有角色信息）
     *
     * @param query 查询条件
     * @return
     */
    Page selectWithRolePage(Query query);

    /**
     * 删除用户
     *
     * @param sysUser 用户
     * @return boolean
     */
    Boolean deleteUserById(SysUser sysUser);

    /**
     * 更新当前用户基本信息
     *
     * @param userDto  用户信息
     * @param username 用户名
     * @return Boolean
     */
    R<Boolean> updateUserInfo(UserDTO userDto, String username);

    /**
     * 更新指定用户信息
     *
     * @param userDto  用户信息
     * @param username 用户信息
     * @return
     */
    Boolean updateUser(UserDTO userDto, String username);

    /**
     * 通过ID查询用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    UserVO selectUserVoById(Integer id);

    void insertUserPost(SysUserPost userPost);

    R<Boolean> sendAuthCode(String params);

    R<Boolean> contrastAuthCode(String authCode, String encrypted);

    R<Boolean> updatePassword(String encrypted, String newpassword1, String authCode);

    void userPosDummy(Integer userId, String posDummys);

    AttackmentDto uploadSignName(MultipartFile[] files);
}
