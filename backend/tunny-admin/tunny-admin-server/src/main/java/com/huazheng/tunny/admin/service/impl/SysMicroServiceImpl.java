package com.huazheng.tunny.admin.service.impl;

import com.huazheng.tunny.admin.mapper.SysMicroMapper;
import com.huazheng.tunny.admin.api.entity.SysMicro;
import com.huazheng.tunny.admin.service.SysMicroService;
import com.huazheng.tunny.common.core.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("sysMicroService")
public class SysMicroServiceImpl extends ServiceImpl<SysMicroMapper, SysMicro> implements SysMicroService {

    @Autowired
    private SysMicroMapper sysMicroMapper;

    /**
     * 查询子应用信息
     *
     * @param microId 子应用ID
     * @return 子应用信息
     */
    @Override
    public SysMicro selectSysMicroById(Integer microId)
    {
        return sysMicroMapper.selectSysMicroById(microId);
    }

    /**
     * 查询子应用列表
     *
     * @param sysMicro 子应用信息
     * @return 子应用集合
     */
    @Override
    public List<SysMicro> selectSysMicroList(SysMicro sysMicro)
    {
        return sysMicroMapper.selectSysMicroList(sysMicro);
    }


    /**
     * 分页模糊查询子应用列表
     * @return 子应用集合
     */
    @Override
    public Page selectSysMicroListByLike(Query query)
    {
        SysMicro sysMicro =  BeanUtil.mapToBean(query.getCondition(), SysMicro.class,false);
        sysMicro .setDelFlag(CommonConstant.STATUS_NORMAL);
        query.setRecords(sysMicroMapper.selectSysMicroListByLike(query,sysMicro));
        return query;
    }

    /**
     * 新增子应用
     *
     * @param sysMicro 子应用信息
     * @return 结果
     */
    @Override
    public int insertSysMicro(SysMicro sysMicro)
    {
        return sysMicroMapper.insertSysMicro(sysMicro);
    }

    /**
     * 修改子应用
     *
     * @param sysMicro 子应用信息
     * @return 结果
     */
    @Override
    public int updateSysMicro(SysMicro sysMicro)
    {
        return sysMicroMapper.updateSysMicro(sysMicro);
    }


    /**
     * 删除子应用
     *
     * @param microId 子应用ID
     * @return 结果
     */
    public int deleteSysMicroById(Integer microId)
    {
        return sysMicroMapper.deleteSysMicroById( microId);
    };


    /**
     * 批量删除子应用对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSysMicroByIds(Integer[] microIds)
    {
        return sysMicroMapper.deleteSysMicroByIds( microIds);
    }

}
