package com.huazheng.tunny.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.admin.api.dto.UserDTO;
import com.huazheng.tunny.admin.api.dto.UserInfo;
import com.huazheng.tunny.admin.api.entity.SysRole;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.admin.api.entity.SysUserPost;
import com.huazheng.tunny.admin.api.entity.SysUserRole;
import com.huazheng.tunny.admin.api.vo.MenuVO;
import com.huazheng.tunny.admin.api.vo.UserVO;
import com.huazheng.tunny.admin.mapper.SysDeptMapper;
import com.huazheng.tunny.admin.mapper.SysUserMapper;
import com.huazheng.tunny.admin.service.SysMenuService;
import com.huazheng.tunny.admin.service.SysRoleService;
import com.huazheng.tunny.admin.service.SysUserRoleService;
import com.huazheng.tunny.admin.service.SysUserService;
import com.huazheng.tunny.common.core.constant.enums.EnumLoginType;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2017/10/31
 */
@Service
public class CacheServiceImpl{

}
