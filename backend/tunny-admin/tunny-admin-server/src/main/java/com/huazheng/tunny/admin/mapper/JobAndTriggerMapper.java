package com.huazheng.tunny.admin.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.admin.api.entity.JobAndTrigger;
import com.huazheng.tunny.admin.api.entity.SysPost;
import com.huazheng.tunny.admin.api.vo.PostVO;
import com.huazheng.tunny.common.core.util.Query;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 定时任务
 *
 * <AUTHOR> code generator
 * @date 2018-10-12 21:00:05
 */
@Mapper
public interface JobAndTriggerMapper extends BaseMapper<JobAndTrigger> {
    /**
     * 获取定时任务信息
     * @return
     * @param query
     * @param description
     */
     List<JobAndTrigger> getJobAndTriggerDetails(Query  query,@Param("description") Object description );

    /**
     * 根据类名查找任务信息
     * @return
     * @param jobClassName
     */
    List<JobAndTrigger> getJobByClassName(@Param("jobClassName") Object jobClassName );
}
