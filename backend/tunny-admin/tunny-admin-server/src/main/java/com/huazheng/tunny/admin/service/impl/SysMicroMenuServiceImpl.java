package com.huazheng.tunny.admin.service.impl;

import com.huazheng.tunny.admin.mapper.SysMicroMenuMapper;
import com.huazheng.tunny.admin.api.entity.SysMicroMenu;
import com.huazheng.tunny.admin.service.SysMicroMenuService;
import com.huazheng.tunny.common.core.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

@Service("sysMicroMenuService")
public class SysMicroMenuServiceImpl extends ServiceImpl<SysMicroMenuMapper, SysMicroMenu> implements SysMicroMenuService {

    @Autowired
    private SysMicroMenuMapper sysMicroMenuMapper;

    /**
     * 查询子应用菜单信息
     *
     * @param menuId 子应用菜单ID
     * @return 子应用菜单信息
     */
    @Override
    public SysMicroMenu selectSysMicroMenuById(Integer menuId) {
        return sysMicroMenuMapper.selectSysMicroMenuById(menuId);
    }

    /**
     * 查询子应用菜单列表
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 子应用菜单集合
     */
    @Override
    public List<SysMicroMenu> selectSysMicroMenuList(SysMicroMenu sysMicroMenu) {
        return sysMicroMenuMapper.selectSysMicroMenuList(sysMicroMenu);
    }


    /**
     * 分页模糊查询子应用菜单列表
     *
     * @return 子应用菜单集合
     */
    @Override
    public Page selectSysMicroMenuListByLike(Query query) {
        SysMicroMenu sysMicroMenu = BeanUtil.mapToBean(query.getCondition(), SysMicroMenu.class, false);
        sysMicroMenu .setDelFlag(CommonConstant.STATUS_NORMAL);
        query.setRecords(sysMicroMenuMapper.selectSysMicroMenuListByLike(query, sysMicroMenu));
        return query;
    }

    /**
     * 新增子应用菜单
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 结果
     */
    @Override
    public int insertSysMicroMenu(SysMicroMenu sysMicroMenu) {
        return sysMicroMenuMapper.insertSysMicroMenu(sysMicroMenu);
    }

    /**
     * 修改子应用菜单
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 结果
     */
    @Override
    public int updateSysMicroMenu(SysMicroMenu sysMicroMenu) {
        return sysMicroMenuMapper.updateSysMicroMenu(sysMicroMenu);
    }


    /**
     * 删除子应用菜单
     *
     * @param menuId 子应用菜单ID
     * @return 结果
     */
    public int deleteSysMicroMenuById(Integer menuId) {
        return sysMicroMenuMapper.deleteSysMicroMenuById(menuId);
    }

    ;


    /**
     * 批量删除子应用菜单对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSysMicroMenuByIds(Integer[] menuIds) {
        return sysMicroMenuMapper.deleteSysMicroMenuByIds(menuIds);
    }

    @Override
    public Page selectWithMicroMenuVoPage(Query query) {
        //获取点击的父编码
        Object parentId = query.getCondition().get("parentId");
        //子应用ID
        Object microId = query.getCondition().get("microId");
        //查询内容
        Object menuSearch = query.getCondition().get("menuSearch");
        List list = sysMicroMenuMapper.selectWithMicroMenuVoPage(query, Integer.parseInt((String) parentId), Integer.parseInt((String) microId), menuSearch);
        query.setRecords(list);
        return query;
    }

}
