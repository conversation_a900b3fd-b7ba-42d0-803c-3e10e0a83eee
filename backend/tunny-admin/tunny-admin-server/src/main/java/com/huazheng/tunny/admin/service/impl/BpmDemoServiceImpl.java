package com.huazheng.tunny.admin.service.impl;

import com.huazheng.tunny.admin.api.feign.RemoteBpmService;
import com.huazheng.tunny.admin.mapper.BpmDemoMapper;
import com.huazheng.tunny.admin.api.entity.BpmDemo;
import com.huazheng.tunny.admin.service.BpmDemoService;
import com.huazheng.tunny.common.core.constant.CommonConstant;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import cn.hutool.core.bean.BeanUtil;
import com.huazheng.tunny.common.core.util.Query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service("bpmDemoService")
public class BpmDemoServiceImpl extends ServiceImpl<BpmDemoMapper, BpmDemo> implements BpmDemoService {

    @Autowired
    private BpmDemoMapper bpmDemoMapper;
    @Autowired
    private RemoteBpmService remoteBpmService;

    /**
     * 查询信息
     *
     * @param id ID
     * @return 信息
     */
    @Override
    public BpmDemo selectBpmDemoById(Integer id) {
        return bpmDemoMapper.selectBpmDemoById(id);
    }

    /**
     * 查询列表
     *
     * @param bpmDemo 信息
     * @return 集合
     */
    @Override
    public List<BpmDemo> selectBpmDemoList(BpmDemo bpmDemo) {
        return bpmDemoMapper.selectBpmDemoList(bpmDemo);
    }


    /**
     * 分页模糊查询列表
     *
     * @return 集合
     */
    @Override
    public Page selectBpmDemoListByLike(Query query) {
        BpmDemo bpmDemo = BeanUtil.mapToBean(query.getCondition(), BpmDemo.class, false);
        SecruityUser secruityUser = SecurityUtils.getUserInfo();
        List bpmInstList = new ArrayList();
        if("my".equals(bpmDemo.getProcessFlag())){
            //我发起的（在途）
//            bpmInstList.addAll(remoteBpmService.processList(secruityUser.getEmpNo(), "myRequest", null, "HTSB"));
            //我发起的（已闭环）
//            bpmInstList.addAll(remoteBpmService.processList(secruityUser.getEmpNo(), "myCompleted", null, "HTSB"));
            bpmDemo.setCreateBy(secruityUser.getEmpNo());
        }
        if("pending".equals(bpmDemo.getProcessFlag())){
            bpmInstList.addAll(remoteBpmService.processList(secruityUser.getEmpNo(), "pending", null, "HTSB"));

            List<String> pendingProcIds = new ArrayList();
            for(int i=0;i<bpmInstList.size();i++){
                Map<String, Object> map = (Map<String, Object>) bpmInstList.get(i);
                pendingProcIds.add(String.valueOf(map.get("procId")));
            }

            bpmDemo.setPendingProcIds(pendingProcIds);
        }
        bpmDemo .setDelFlag(CommonConstant.STATUS_NORMAL);

        List<BpmDemo> bpmDemoList = bpmDemoMapper.selectBpmDemoListByLike(query, bpmDemo);
        for(int i=0;i<bpmDemoList.size();i++){
            for(int j=0;j<bpmInstList.size();j++){
                Map<String, Object> map = (Map<String, Object>) bpmInstList.get(j);
                if(map.get("procId").equals(bpmDemoList.get(i).getProcId())){
                    bpmDemoList.get(i).setTaskId(String.valueOf(map.get("taskId")));
                }
            }
        }
        query.setRecords(bpmDemoList);
        return query;
    }

    /**
     * 新增
     *
     * @param bpmDemo 信息
     * @return 结果
     */
    @Override
    public int insertBpmDemo(BpmDemo bpmDemo) {
        return bpmDemoMapper.insertBpmDemo(bpmDemo);
    }

    /**
     * 修改
     *
     * @param bpmDemo 信息
     * @return 结果
     */
    @Override
    public int updateBpmDemo(BpmDemo bpmDemo) {
        return bpmDemoMapper.updateBpmDemo(bpmDemo);
    }


    /**
     * 删除
     *
     * @param id ID
     * @return 结果
     */
    public int deleteBpmDemoById(Integer id) {
        return bpmDemoMapper.deleteBpmDemoById(id);
    }

    ;


    /**
     * 批量删除对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteBpmDemoByIds(Integer[] ids) {
        return bpmDemoMapper.deleteBpmDemoByIds(ids);
    }

}
