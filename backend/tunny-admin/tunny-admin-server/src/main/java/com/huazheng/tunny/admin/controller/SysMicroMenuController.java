package com.huazheng.tunny.admin.controller;

import com.huazheng.tunny.admin.api.dto.MenuTree;
import com.huazheng.tunny.admin.api.entity.SysMenu;
import com.huazheng.tunny.admin.api.entity.SysMicroMenu;
import com.huazheng.tunny.admin.api.vo.TreeUtil;
import com.huazheng.tunny.admin.service.SysMicroMenuService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.constant.CommonConstant;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.listener.EasyExcelListener;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 子应用菜单
 *
 * <AUTHOR> code generator
 * @date 2022-06-15 17:21:18
 */
@Slf4j
@RestController
@RequestMapping("/micro/menu")
public class SysMicroMenuController {

    @Autowired
    private SysMicroMenuService sysMicroMenuService;

    /**
     * 　　* @Description: 分页查询菜单
     * 　　* @param
     * 　　* @return
     * 　　* <AUTHOR>
     * 　　* @date 2019/1/30 17:23
     */
    @GetMapping("/menuPage")
    public Page menuPage(@RequestParam Map<String, Object> params) {
        return sysMicroMenuService.selectWithMicroMenuVoPage(new Query(params));
    }

    /**
     * 返回树形菜单集合
     *
     * @return 树形菜单
     */
    @GetMapping(value = "/allTree")
    public List<MenuTree> getTree(@RequestParam Integer microId) {
        SysMicroMenu condition = new SysMicroMenu();
        condition.setMicroId(microId);
        condition .setDelFlag(CommonConstant.STATUS_NORMAL);
        List<SysMicroMenu> sysMicroMenus = sysMicroMenuService.selectList(new EntityWrapper<SysMicroMenu>(condition));
        return bulidTree(sysMicroMenus, -1);
    }

    /**
     * 通过sysMenu创建树形节点
     *
     * @param menus
     * @param root
     * @return
     */
    public static List<MenuTree> bulidTree(List<SysMicroMenu> menus, int root) {
        List<MenuTree> trees = new ArrayList<MenuTree>();
        MenuTree node;
        for (SysMicroMenu menu : menus) {
            node = new MenuTree();
            node.setId(menu.getMenuId());
            node.setParentId(menu.getParentId());
            node.setName(menu.getName());
            node.setPath(menu.getPath());
            node.setCode(menu.getPermission());
            node.setLabel(menu.getName());
            node.setComponent(menu.getComponent());
            node.setIcons(menu.getIcons());
            node.setOutside(menu.getOutside());
            node.setRouterName(menu.getRouterName());
            node.setIsCache(menu.getIsCache());
            node.setIsHidden(menu.getIsHidden());
            trees.add(node);
        }
        return TreeUtil.bulid(trees, root);
    }

    /**
     * 信息
     *
     * @param menuId
     * @return R
     */
    @GetMapping("/{menuId}")
    public R info(@PathVariable("menuId") Integer menuId) {
        SysMicroMenu sysMicroMenu = sysMicroMenuService.selectOne(new EntityWrapper<SysMicroMenu>().eq("menu_id", menuId).eq("del_flag", "1"));
        return new R<>(sysMicroMenu);
    }

    /**
     * 保存
     *
     * @param sysMicroMenu
     * @return R
     */
    @PostMapping("/add")
    public R save(@RequestBody SysMicroMenu sysMicroMenu) {
        sysMicroMenu.setCreateBy(SecurityUtils.getUser());
        sysMicroMenu.setUpdateBy(SecurityUtils.getUser());
        sysMicroMenuService.insert(sysMicroMenu);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param sysMicroMenu
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody SysMicroMenu sysMicroMenu) {
        sysMicroMenu.setUpdateBy(SecurityUtils.getUser());
        sysMicroMenuService.updateById(sysMicroMenu);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param menuId
     * @return R
     */
    @GetMapping("/del")
    public R delete(@RequestParam Integer microId, @RequestParam Integer menuId) {
        sysMicroMenuService.updateForSet("del_flag = 0", new EntityWrapper<SysMicroMenu>().eq("micro_id", microId).eq("menu_id", menuId));
        return new R<>(Boolean.TRUE);
    }

}
