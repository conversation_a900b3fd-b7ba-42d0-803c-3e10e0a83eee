package com.huazheng.tunny.admin.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.admin.api.common.PageParams;
import com.huazheng.tunny.admin.api.entity.GatewayRoute;
import com.huazheng.tunny.admin.api.feign.RemoteGatewayService;
import com.huazheng.tunny.admin.api.util.StringUtils;
import com.huazheng.tunny.admin.api.dto.GatewayRouteDto;
import com.huazheng.tunny.admin.mapper.GatewayRouteMapper;
import com.huazheng.tunny.admin.service.GatewayRouteService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class GatewayRouteServiceImpl extends ServiceImpl<GatewayRouteMapper, GatewayRoute> implements GatewayRouteService {

    @Autowired
    private GatewayRouteMapper gatewayRouteMapper;
    @Autowired
    private RemoteGatewayService remoteGatewayService;

    /**
     * 分页查询
     *
     * @param pageParams
     * @return
     */
    @Override
    public Page<GatewayRoute> findListPage(PageParams pageParams) {
        GatewayRoute query = pageParams.mapToObject(GatewayRoute.class);
        EntityWrapper<GatewayRoute> entityWrapper = new EntityWrapper();
        if(StringUtils.isNotEmpty(query.getServiceId())){
            entityWrapper.eq("service_id", query.getServiceId());
        }
        entityWrapper.eq("del_flag", "1");

        return pageParams.setRecords(gatewayRouteMapper.selectPage(pageParams, entityWrapper));
    }

    @Override
    public Integer add(GatewayRouteDto gatewayRouteDto) {
        GatewayRoute gatewayRoute = new GatewayRoute();
        BeanUtils.copyProperties(gatewayRouteDto, gatewayRoute);
        gatewayRoute.setCreateDate(new Date());
        gatewayRoute.setCreatorId("");
        gatewayRoute = buildJSON(gatewayRoute);

        remoteGatewayService.saveRoute(gatewayRoute);
        return gatewayRouteMapper.insert(gatewayRoute);
    }

    @Override
    public Integer update(GatewayRouteDto gatewayRouteDto) {
        GatewayRoute gatewayRoute = new GatewayRoute();
        BeanUtils.copyProperties(gatewayRouteDto, gatewayRoute);
        gatewayRoute.setUpdateDate(new Date());
        gatewayRoute.setUpdateId("");
        gatewayRoute = buildJSON(gatewayRoute);

        remoteGatewayService.update(gatewayRoute);
        return gatewayRouteMapper.updateById(gatewayRoute);
    }

    /**
     * 构建路由配置的前缀、过滤器JSON数据
     * */
    private GatewayRoute buildJSON(GatewayRoute gatewayRoute){

        //构造路由前缀JSON，例：{"Path":"/basic/**"}
        Map<String, Object> predicates = new HashMap<>();
        predicates.put("Path", gatewayRoute.getPredicatesPath());
        gatewayRoute.setPredicates(JSON.toJSONString(predicates));

        //构造过滤器JSON，例：{"StripPrefix":"1"}
        Map<String, Object> filters = new HashMap<>();
        filters.put("StripPrefix", gatewayRoute.getStripPrefix());

        filters.put("RemoveRequestHeader", gatewayRoute.getRemoveRequestHeader());
        filters.put("SwaggerHeaderFilter", gatewayRoute.getSwaggerHeaderFilter());
        filters.put("PasswordDecoderFilter", gatewayRoute.getPasswordDecoderFilter());
        filters.put("ImageCodeGatewayFilter", gatewayRoute.getImageCodeGatewayFilter());
        filters.put("RequestRateLimiter", gatewayRoute.getRequestRateLimiter());
        filters.put("Hystrix", gatewayRoute.getHystrix());

        gatewayRoute.setFilters(JSON.toJSONString(filters));

        return gatewayRoute;
    }

    @Override
    public Integer delete(String id) {
        return gatewayRouteMapper.deleteById(Long.parseLong(id));
    }

    @Override
    public GatewayRoute find(String id) {
        GatewayRoute entity = new GatewayRoute();
        entity.setRouteId(Long.parseLong(id));
        return gatewayRouteMapper.selectOne(entity);
    }

}
