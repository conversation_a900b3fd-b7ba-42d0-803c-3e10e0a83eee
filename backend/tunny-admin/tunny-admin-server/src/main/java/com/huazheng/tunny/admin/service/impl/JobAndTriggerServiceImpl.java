package com.huazheng.tunny.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.admin.api.dto.UserDTO;
import com.huazheng.tunny.admin.api.dto.UserInfo;
import com.huazheng.tunny.admin.api.entity.JobAndTrigger;
import com.huazheng.tunny.admin.api.entity.SysRole;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.admin.api.entity.SysUserRole;
import com.huazheng.tunny.admin.api.vo.MenuVO;
import com.huazheng.tunny.admin.api.vo.UserVO;
import com.huazheng.tunny.admin.mapper.JobAndTriggerMapper;
import com.huazheng.tunny.admin.mapper.SysPostMapper;
import com.huazheng.tunny.admin.mapper.SysUserMapper;
import com.huazheng.tunny.admin.service.*;
import com.huazheng.tunny.common.core.constant.enums.EnumLoginType;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2017/10/31
 */
@Slf4j
@Service
@AllArgsConstructor
public class JobAndTriggerServiceImpl extends ServiceImpl<JobAndTriggerMapper, JobAndTrigger> implements JobAndTriggerService {
	private final JobAndTriggerMapper jobAndTriggerMapper;
	@Override
	public Page getJobAndTriggerDetails(Query query) {
		Object description = query.getCondition().get("description");
		List<JobAndTrigger> records=jobAndTriggerMapper.getJobAndTriggerDetails(query,description);
		query.setRecords(records);

		return query;
	}

	@Override
	public List<JobAndTrigger> getJobByClassName(Object jobClassName) {
		List<JobAndTrigger> records=jobAndTriggerMapper.getJobByClassName(jobClassName);
		return records;
	}


}
