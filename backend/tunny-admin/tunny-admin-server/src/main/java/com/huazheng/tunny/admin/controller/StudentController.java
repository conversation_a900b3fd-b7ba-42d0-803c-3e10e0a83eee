package com.huazheng.tunny.admin.controller;

//import cn.afterturn.easypoi.excel.ExcelImportUtil;
//import cn.afterturn.easypoi.excel.annotation.Excel;
//import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
//import cn.afterturn.easypoi.excel.entity.ImportParams;
//import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
//import cn.afterturn.easypoi.excel.imports.ExcelImportService;
//import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
//import cn.afterturn.easypoi.handler.inter.IExcelModel;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.admin.api.entity.Student;
import com.huazheng.tunny.admin.api.entity.StudentEntity;
import com.huazheng.tunny.admin.api.util.StudentSexConverter;
import com.huazheng.tunny.common.core.listener.EasyExcelListener;
import com.huazheng.tunny.admin.service.RedisService;
import com.huazheng.tunny.admin.service.StudentService;
//import com.huazheng.tunny.common.core.util.EasyPoiExcelExportUtil;
//import com.huazheng.tunny.common.core.util.ExcelUtil;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;


/**
 * 学生表
 *
 * <AUTHOR> code generator
 * @date 2018-10-08 09:58:04
 */
@RestController
@RequestMapping("/student")
@Api(value = "StudentController|一个用来测试swagger注解的控制器")
public class StudentController {
    @Autowired
    private StudentService studentService;
    @Autowired
    private RedisService redisService;


//    ExcelUtil util = new ExcelUtil();


    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        return studentService.selectPage(new Query<>(params), new EntityWrapper<>());
    }


    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        Student student = studentService.selectById(id);
        return new R<>(student);
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/getuser")
    public R getUser() {
        SecruityUser t = SecurityUtils.getUserInfo();
        return new R<>(t);
    }

    /**
     * 保存
     *
     * @param student
     * @return R
     */
    @PostMapping
    public R save(@RequestBody Student student) {
        studentService.insert(student);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param student
     * @return R
     */
    @PutMapping
    public R update(@RequestBody Student student) {
        studentService.updateById(student);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @DeleteMapping("/{id}")
    public R delete(@PathVariable Integer id) {
        studentService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }


    @GetMapping("/delObjs")
    public R delObjs(@RequestParam(value = "ids") String ids) {
        if (ids != null && !"".equals(ids)) {
            String[] split = ids.split(",");
            for (String s : split) {
                studentService.deleteById(Integer.valueOf(s));
            }
        }
        return new R<>(Boolean.TRUE);
    }

    //表头
    public static final String[] tableHeader1 = {"一票到底台账"};
    public static final String[] tableHeader2 = {"序列", "性别", "", "累计", "", "计划", "实际"};
    public static final String[] tableHeader3 = {"序列", "男", "女", "月累计", "年累计", "合计"};

    public static List<String[]> getStringDate() {
        List<String[]> cacheList = new ArrayList<String[]>();
        for (int j = 0; j < 50; j++) {
            String[] tb = new String[10];
            for (int i = 0; i < 7; i++) {
                tb[i] = "数据" + i;
            }
            cacheList.add(tb);
        }
        return cacheList;
    }

    public static List<String[]> getStudentDate() {
        List<String[]> cacheList = new ArrayList<String[]>();
        for (int j = 0; j < 50; j++) {
            String[] tb = new String[10];
            tb[0] = String.valueOf(j);
            tb[1] = "小明" + j;
            tb[2] = String.valueOf(j);
            tb[3] = "北京中路" + j + "号";
            tb[4] = "2019-03-02";
            tb[5] = "2019-03-02";
            cacheList.add(tb);
        }
        return cacheList;
    }

    //下载Excel
    @GetMapping("/downloadExcel")
    public void downloadExcel(HttpServletResponse res) throws IOException {
        /*//创建sheet名字(集合形式，同时创建多个sheet页)
        String[] sheetName = {"我的sheet1"};
        //创建Excel(创建的类型，并传入sheet集合)
        util.createWorkBook(ExcelUtil.EXCELVERSION.EXCEL_VERSION_2007, sheetName);
        //创建第一行信息
        List<String[]> list1 = new ArrayList<String[]>();
        list1.add(tableHeader1);
        //创建第二行信息
        List<String[]> list2 = new ArrayList<String[]>();
        list2.add(tableHeader2);
        //创建第三行信息
        List<String[]> list3 = new ArrayList<String[]>();
        list3.add(tableHeader3);


        //设置合并的单元格
        List<Integer[]> alist = new ArrayList<>();
        Integer[] a1 = {0, 0, 0, 6};//开始行,结束行,开始列,结束列
        alist.add(a1);
        //设置第二行单元格合并
        List<Integer[]> blist = new ArrayList<>();
        Integer[] b1 = {1, 2, 0, 0};//开始行,结束行,开始列,结束列
        Integer[] b11 = {1, 1, 1, 2};//开始行,结束行,开始列,结束列
        Integer[] b111 = {1, 1, 3, 4};//开始行,结束行,开始列,结束列
        blist.add(b1);
        blist.add(b11);
        blist.add(b111);

        //设置第三行合并
        List<Integer[]> clist = new ArrayList<>();
        Integer[] c1 = {2, 2, 5, 6};//开始行,结束行,开始列,结束列
        clist.add(c1);

        util.setValue(list1, util.getCellType4Merge(0, 12, alist), 0, 0);
        util.setValue(list2, util.getCellType4Merge(0, 8, blist), 0, 1);
        util.setValue(list3, util.getCellType4Merge(0, 6, clist), 0, 2);
        util.setValue(getStringDate(), util.getCellType4Number(), 0, 3);
        //设置批注
        util.addComment(10, 5, "这是批注信息。", "李明赫");
        //设置数据有效性 目前只支持2003
        //util.SetValidationList(0, 500, 2, 2, new String[]{"选择1", "选择2", "选择3"});
        //给单元格添加提示内容
        //util.setMessage(1, 1, 2, 2, "qq", "12589");
        //util.save2File(res, "E://a");
        util.exportExcel(res, "工资条模板");*/

        /* 2022-4-13 废弃，改用EasyPoi进行导入导出

        String[] student1 = {"学生表导出测试"};
        String[] student2 = {"序号", "姓名", "年龄", "地址", "创建时间", "更新时间"};
        //创建sheet名字(集合形式，同时创建多个sheet页)
        String[] sheetName = {"学生表"};
        //创建Excel(创建的类型，并传入sheet集合)
        util.createWorkBook(ExcelUtil.EXCELVERSION.EXCEL_VERSION_2007, sheetName);
        //创建第一行标题头
        List<String[]> list1 = new ArrayList<String[]>();
        list1.add(student1);
        //创建第二行标题头
        List<String[]> list2 = new ArrayList<String[]>();
        list2.add(student2);

        List<Integer[]> alist = new ArrayList<>();
        Integer[] a1 = {0, 0, 0, 6};//开始行,结束行,开始列,结束列
        alist.add(a1);
        List<Integer[]> blist = new ArrayList<>();

        util.setValue(list1, util.getCellType4Merge(0, 12, alist), 0, 0);
        util.setValue(list2, util.getCellType4Merge(0, 10, blist), 0, 1);
        util.setValue(getStudentDate(), util.getCellType4Number(), 0, 2);
        util.exportExcel(res, "学生");

         */

        /*
        实体类list注解形式导出
         */
//        List list = new ArrayList();
//        list.add(new StudentEntity("1", "一班", "张三", 1, new Date(), new Date()));
//        list.add(new StudentEntity("2", "二班", "李四", 2, new Date(), new Date()));
//        //支持换行
//        list.add(new StudentEntity("3", "二班", "王五", 1, new Date(), new Date()));
//        //title为null则不带大标题
//        Workbook workbook = EasyPoiExcelExportUtil.getWorkbook("计算机系\n学生考勤表", "学生", StudentEntity.class, list, null, null);

        /*
        单行数据写入
         */
//        Sheet sheet = workbook.getSheetAt(0);
//        Row row = sheet.createRow(sheet.getLastRowNum() + 1);
//        Cell cell_0 = row.createCell(0);
//        cell_0.setCellValue("人数合计");
//        CellStyle cellStyle_0 = workbook.createCellStyle();
//        //水平居中
//        cellStyle_0.setAlignment(HorizontalAlignment.RIGHT);
//        //垂直居中
//        cellStyle_0.setVerticalAlignment(VerticalAlignment.CENTER);
//        cell_0.setCellStyle(cellStyle_0);
//        //合并单元格
//        CellRangeAddress range_0 = new CellRangeAddress(sheet.getLastRowNum(), sheet.getLastRowNum(), 0, 3);
//        sheet.addMergedRegion(range_0);
//
//        Cell cell_1 = row.createCell(4);
//        cell_1.setCellValue("3");
//        CellStyle cellStyle_1 = workbook.createCellStyle();
//        //水平居中
//        cellStyle_1.setAlignment(HorizontalAlignment.CENTER);
//        //垂直居中
//        cellStyle_1.setVerticalAlignment(VerticalAlignment.CENTER);
//        //背景色
//        cellStyle_1.setFillForegroundColor(IndexedColors.RED.getIndex());
//        cellStyle_1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//        Font font = workbook.createFont();
//        //字号
//        font.setFontHeightInPoints((short) 10);
//        //字体
//        font.setFontName("黑体");
//        //加粗
//        font.setBold(true);
//        //颜色
//        font.setColor(IndexedColors.WHITE.getIndex());
//        cellStyle_1.setFont(font);
//        cell_1.setCellStyle(cellStyle_1);

        /*
        导出excel
         */
//        EasyPoiExcelExportUtil.exportExcel(workbook, "ExportExcel.xlsx", res);


        List list = new ArrayList();
        list.add(new StudentEntity("1", "一班", "张三", 1, new Date(), new Date()));
        list.add(new StudentEntity("2", "二班", "李四", 2, new Date(), new Date()));
        list.add(new StudentEntity("3", "二班", "王五", 1, new Date(), new Date()));
        // 根据用户传入字段，导出指定的列（被@ExcelIgnore注解忽略字段依然会被忽略导出）
        List<String> excludeColumnFiledNames = new ArrayList<>();
        excludeColumnFiledNames.add("classNum");
        excludeColumnFiledNames.add("name");
        excludeColumnFiledNames.add("sex");
        excludeColumnFiledNames.add("registrationDate");
        //sheet()可以指定表名称也可以指定表序号
        EasyExcel.write(res.getOutputStream(), StudentEntity.class)
                .includeColumnFiledNames(excludeColumnFiledNames)
                .sheet(1)
                .doWrite(list);
    }


    //下载Excel，模板化导出
    @GetMapping("/downloadExcelByModel")
    public void downloadExcelByModel(HttpServletResponse res) throws IOException{
        Map dataMap = new HashMap();
        dataMap.put("name", "计算机系");
        dataMap.put("total", "3");

        List studentList = new ArrayList();
        Map student_0 = new HashMap();
        student_0.put("classNum", "一班");
        student_0.put("name", "张三");
        student_0.put("sex", "男生");
        student_0.put("birthday", "2020-01-01");
        student_0.put("registrationDate", "2022-01-01");
        studentList.add(student_0);
        Map student_1 = new HashMap();
        student_1.put("classNum", "二班");
        student_1.put("name", "李四");
        student_1.put("sex", "女生");
        student_1.put("birthday", "2020-01-02");
        student_1.put("registrationDate", "2022-02-01");
        studentList.add(student_1);
        Map student_2 = new HashMap();
        student_2.put("classNum", "二班");
        student_2.put("name", "王五");
        student_2.put("sex", "男生");
        student_2.put("birthday", "2020-03-01");
        student_2.put("registrationDate", "2022-03-01");
        studentList.add(student_2);
//        dataMap.put("studentList", studentList);
//
//        Workbook workbook = EasyPoiExcelExportUtil.getWorkbook("META-INF/resources/ExcelModel.xlsx", dataMap, null);
        //导出excel
//        EasyPoiExcelExportUtil.exportExcel(workbook, "ExportExcelModel.xlsx", res);

        ExcelWriter excelWriter = EasyExcel.write(res.getOutputStream())
                .withTemplate(getClass().getResourceAsStream("/META-INF/resources/ExcelModel.xlsx"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        excelWriter.fill(dataMap, writeSheet);
        excelWriter.fill(new FillWrapper("studentList", studentList), writeSheet);
        excelWriter.finish();
    }

//    @Autowired
//    private StudentImportVerifyHandler studentImportVerifyHandler;

    /**
     * 上传文件
     *
     * @param file
     * @throws IOException
     */
    @PostMapping("/uploadExcel")
    public R uploadExcel(@RequestParam("file") MultipartFile file) throws Exception {
        /* 2022-4-13 废弃，改用EasyPoi进行导入导出
        InputStream in = null;
        List<List<Object>> listob = null;
        if (file.isEmpty()) {
            throw new RuntimeException("文件不存在！");
        }
        in = file.getInputStream();

        //获取出来这个sheet页面的全部信息
        //sheet 0
        listob = new ExcelUtil().getBankListByExcel(in, file.getOriginalFilename(), 0, 3);
        in.close();

        if (listob != null && listob.size() > 0) {
            for (List<Object> objects : listob) {
                Student student = new Student();
                student.setName(objects.get(0).toString());
                student.setAge(Integer.valueOf(objects.get(1).toString()));
                student.setAddress(objects.get(2).toString());
                student.setCreatetime(LocalDateTime(objects.get(3)));
                student.setUpdatetime(LocalDateTime(objects.get(4)));
                studentService.insert(student);
            }
        }
         */

        /* 2022-4-18 EasyPoi导入  不满足大数据效率，废弃
        ImportParams params = new ImportParams();
        // 设置标题列占几行，默认是0，可以不设置
        params.setTitleRows(1);
        // 设置字段名称占几行 即header
        params.setHeadRows(2);
        // 开启Excel校验
        params.setNeedVerfiy(true);
        //校验处理器（可选）
        params.setVerifyHandler(studentImportVerifyHandler);
        // 设置从第几张表格开始读取，这里0代表第一张表，默认从第一张表读取
        params.setStartSheetIndex(0);

        ExcelImportResult<StudentEntity> result = new ExcelImportService().importExcelByIs(file.getInputStream(), StudentEntity.class, params, true);
//        System.out.println("是否校验失败: " + result.isVerfiyFail());
//        System.out.println("校验失败的集合:" + JSONObject.toJSONString(result.getFailList()));
//        System.out.println("校验通过的集合:" + JSONObject.toJSONString(result.getList()));
        List msg_arr = new ArrayList();
        for (StudentEntity entity : result.getFailList()) {
            int line = entity.getRowNum() + 1;
            msg_arr.add("第" + line + "行的错误是：" + entity.getErrorMsg());
        }
        return new R<>();
                (result.getFailList() == null || result.getFailList().size() == 0)
                , String.join("\n", msg_arr)
                , result.getList()
        );
        */

        List list = new ArrayList();
        try {
            EasyExcel.read(file.getInputStream(), StudentEntity.class, new EasyExcelListener<StudentEntity>() {
                //数据处理逻辑，需要实现数据校验必须重写父级的invoke方法
                @Override
                public void invoke(StudentEntity entity, AnalysisContext analysisContext) {
                    //log.info("解析到一条数据:{}", JSON.toJSONString(excelItem));
                    String classNum = entity.getClassNum();
                    if (classNum.length()>10) {
                        throw new RuntimeException(String.format("第%s行错误，班级名称过长", analysisContext.readRowHolder().getRowIndex() + 1));
                    }
                    //每读取1000条数据保存一次
                    list.add(entity);
                    if (list.size() >= 1000) {
                        saveData(list);
                        list.clear();
                    }
                }

                /**
                 * 所有数据解析完成了就会来调用，确保最后遗留的数据也存储到数据库
                 */
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    saveData(list);
                    list.clear();
                }

                //数据批量保存逻辑
                @Override
                public void saveData(List<StudentEntity> infoList) {
                    list.addAll(infoList);
                }
            //headRowNumber()声明标题行占用行数
            }).headRowNumber(3).sheet().doRead();
        }catch (Exception e){
            e.printStackTrace();
            return new R<>(Boolean.FALSE, e.getMessage());
        }
        return new R<>(Boolean.TRUE, "", list);
    }

    public static LocalDateTime LocalDateTime(Object obj) throws ParseException {
        SimpleDateFormat sim = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = sim.parse(obj.toString());
        Instant instant = parse.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        return localDateTime;
    }
}
