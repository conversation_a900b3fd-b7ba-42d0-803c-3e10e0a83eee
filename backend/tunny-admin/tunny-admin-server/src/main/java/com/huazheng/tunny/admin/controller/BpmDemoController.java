package com.huazheng.tunny.admin.controller;

import com.huazheng.tunny.admin.api.entity.BpmDemo;
import com.huazheng.tunny.admin.service.BpmDemoService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;

import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.listener.EasyExcelListener;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 *
 * <AUTHOR> code generator
 * @date 2022-07-11 13:46:26
 */
@Slf4j
@RestController
@RequestMapping("/bpmdemo")
public class BpmDemoController {

    @Autowired
    private BpmDemoService bpmDemoService;

    /**
     *  列表
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  bpmDemoService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return bpmDemoService.selectBpmDemoListByLike(new Query<>(params));
    }

    /**
     * 信息
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        BpmDemo bpmDemo =bpmDemoService.selectById(id);
        return new R<>(bpmDemo);
    }

    /**
     * 保存
     * @param bpmDemo
     * @return R
     */
    @PostMapping
    public R save(@RequestBody BpmDemo bpmDemo) {
        bpmDemo.setCreateBy(SecurityUtils.getUserInfo().getEmpNo());
        bpmDemo.setCreateByName(SecurityUtils.getUserInfo().getRealName());
        bpmDemoService.insert(bpmDemo);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     * @param bpmDemo
     * @return R
     */
    @PostMapping("/update")
    public R update(@RequestBody BpmDemo bpmDemo) {
        bpmDemoService.updateById(bpmDemo);
        return new R<>(Boolean.TRUE);
    }



    /**
     * 删除
     * @param id
     * @return R
     */
    @GetMapping("/del/{id}")
    public R delete(@PathVariable  Integer id) {
        bpmDemoService.updateForSet("del_flag = 0", new EntityWrapper<BpmDemo>().eq("id", id));
        return new R<>(Boolean.TRUE);
    }

}
