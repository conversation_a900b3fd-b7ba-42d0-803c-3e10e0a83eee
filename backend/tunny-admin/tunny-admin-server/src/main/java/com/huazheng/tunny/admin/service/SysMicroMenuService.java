package com.huazheng.tunny.admin.service;

import com.baomidou.mybatisplus.service.IService;
import com.huazheng.tunny.admin.api.entity.SysMicroMenu;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 子应用菜单 服务接口层
 *
 * <AUTHOR> code generator
 * @date 2022-06-15 17:21:18
 */
public interface SysMicroMenuService extends IService<SysMicroMenu> {
    /**
     * 查询子应用菜单信息
     *
     * @param menuId 子应用菜单ID
     * @return 子应用菜单信息
     */
    public SysMicroMenu selectSysMicroMenuById(Integer menuId);

    /**
     * 查询子应用菜单列表
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 子应用菜单集合
     */
    public List<SysMicroMenu> selectSysMicroMenuList(SysMicroMenu sysMicroMenu);


    /**
     * 分页模糊查询子应用菜单列表
     *
     * @return 子应用菜单集合
     */
    public Page selectSysMicroMenuListByLike(Query query);


    /**
     * 新增子应用菜单
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 结果
     */
    public int insertSysMicroMenu(SysMicroMenu sysMicroMenu);

    /**
     * 修改子应用菜单
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 结果
     */
    public int updateSysMicroMenu(SysMicroMenu sysMicroMenu);

    /**
     * 删除子应用菜单
     *
     * @param menuId 子应用菜单ID
     * @return 结果
     */
    public int deleteSysMicroMenuById(Integer menuId);

    /**
     * 批量删除子应用菜单
     *
     * @param menuIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysMicroMenuByIds(Integer[] menuIds);

    Page selectWithMicroMenuVoPage(Query query);
}

