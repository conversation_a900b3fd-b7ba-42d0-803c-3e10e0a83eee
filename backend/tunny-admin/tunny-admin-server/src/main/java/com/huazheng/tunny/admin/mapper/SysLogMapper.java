package com.huazheng.tunny.admin.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.admin.api.entity.SysLog;
import com.huazheng.tunny.common.core.util.Query;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-11-20
 */
public interface SysLogMapper extends BaseMapper<SysLog> {
    List selectWithPage(Query<Object> query, @Param("type") Object type, @Param("startTime") String startTime, @Param("endTime") String  endTime);
}
