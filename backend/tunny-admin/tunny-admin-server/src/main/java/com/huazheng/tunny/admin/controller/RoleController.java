package com.huazheng.tunny.admin.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.admin.api.dto.DeptTree;
import com.huazheng.tunny.admin.api.dto.RoleDTO;
import com.huazheng.tunny.admin.api.dto.TreeNode;
import com.huazheng.tunny.admin.api.entity.SysDept;
import com.huazheng.tunny.admin.api.entity.SysRole;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.admin.api.vo.RoleVO;
import com.huazheng.tunny.admin.service.SysDeptService;
import com.huazheng.tunny.admin.service.SysRoleMenuService;
import com.huazheng.tunny.admin.service.SysRoleService;

import com.huazheng.tunny.common.core.constant.CommonConstant;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.log.enums.LogType;
import com.huazheng.tunny.common.log.util.EsLog;
import com.huazheng.tunny.common.security.DTO.SecruityUser;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/role")
@Slf4j
public class RoleController {
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysRoleMenuService sysRoleMenuService;
    @Autowired
    private SysDeptService sysDeptService;

    /**
     * 通过ID查询角色信息
     *
     * @param id ID
     * @return 角色信息
     */
    @GetMapping("/{id}")
    public SysRole role(@PathVariable Integer id) {
        return sysRoleService.selectById(id);
    }

    /**
     * 添加角色
     *
     * @param roleDto 角色信息
     * @return success、false
     */
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('sys_role_add')")
    public R<Boolean> role(@RequestBody RoleDTO roleDto) {
        return new R<>(sysRoleService.insertRole(roleDto));
    }

    /**
     * 修改角色
     *
     * @param roleDto 角色信息
     * @return success/false
     */
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('sys_role_edit')")
    public R<Boolean> roleUpdate(@RequestBody RoleDTO roleDto) {
        //插入log日志
        SecruityUser user = SecurityUtils.getUserInfo();
        log.info("es_begin:" + JSONUtil.toJsonStr(new EsLog(String.valueOf(user.getId()), user.getUserName(), user.getEmpNo(), user.getDeptId(), "", "/role/update", "PUT", JSONUtil.toJsonStr(roleDto), "", "", DateUtil.now(), "", LogType.TUNNY_OPERATE.toString(), "", "")) + "es_end!");
        return new R<>(sysRoleService.updateRoleById(roleDto));
    }

    @DeleteMapping("/{id}")
//    @PreAuthorize("@pms.hasPermission('sys_role_del')")
    public R<Boolean> roleDel(@PathVariable Integer id) {
        SysRole sysRole = sysRoleService.selectById(id);
        sysRole.setDelFlag(CommonConstant.STATUS_DEL);
        return new R<>(sysRoleService.updateById(sysRole));
    }

    /**
     * 分页查询角色信息
     *
     * @param params 分页对象
     * @return 分页对象
     */
    @GetMapping("/rolePage")
    public Page rolePage(@RequestParam Map<String, Object> params) {
        params.put(CommonConstant.DEL_FLAG, CommonConstant.STATUS_NORMAL);
        //return sysRoleService.selectPage(new Query<>(params), new EntityWrapper<>());
        return sysRoleService.selectRolePageByRoleName(new Query<>(params), new EntityWrapper<>());
    }

    /**
     * 更新角色菜单
     *
     * @param roleId  角色ID
     * @param menuIds 菜单ID拼成的字符串，每个id之间根据逗号分隔
     * @return success、false
     */
    @PutMapping("/roleMenuUpd")
//    @PreAuthorize("@pms.hasPermission('sys_role_perm')")
    public R<Boolean> roleMenuUpd(Integer roleId, @RequestParam(value = "menuIds", required = false) String menuIds) {
        SysRole sysRole = sysRoleService.selectById(roleId);
        return new R<>(sysRoleMenuService.insertRoleMenus(sysRole.getRoleCode(), roleId, menuIds));
    }

    /**
     * 　　* @Description: 查询所有角色
     * 　　* @param
     * 　　* @return
     * 　　* <AUTHOR>
     * 　　* @date 2019/1/28 11:17
     */
    @GetMapping("/roleListAll")
    public List<SysRole> roleListAll() {
        return sysRoleService.selectList(new EntityWrapper<>());
    }


    /**
     * 查询角色数据权限
     *
     * @param roleId
     * @return
     */
    @GetMapping("/selectRoledataPower")
    public RoleVO selectRoledataPower(@RequestParam(required = true) Integer roleId) {

        RoleVO roleVO = new RoleVO();//返回的ov包装类
        SysRole sysRole = sysRoleService.selectById(roleId);
        if (sysRole != null) {
            BeanUtils.copyProperties(sysRole, roleVO);
            Integer dsType = sysRole.getDsType();//数据权限类型0-全部 1-自定义 2：本级及子级 3：本级
            String dsScope = sysRole.getDsScope();//数据类型为自定义的数据集合
            //查询部门树
            SysDept condition = new SysDept();
            condition.setDelFlag(CommonConstant.STATUS_NORMAL);
            List<DeptTree> deptTrees = sysDeptService.selectListTree(new EntityWrapper<>(condition));
            //处理部门树的默认选中值
            if (deptTrees != null && deptTrees.size() > 0 && dsScope != null && !"".equals(dsScope)) {
                List<Integer> deptList = new ArrayList<>();
                //将字符串分割为数组
                String[] split = dsScope.split(",");
                if (split != null && split.length > 0) {
                    for (String s : split) {
                        deptList.add(Integer.valueOf(s));
                    }
                    //两次for循环处理数结构选中值
                    deptTrees = deptSelected(deptTrees, deptList);
                }
            }
            roleVO.setDeptTrees(deptTrees);
        }
        return roleVO;
    }

    //树结构选中值处理
    public <T extends TreeNode> List<T> deptSelected(List<T> trees, List<Integer> menuList) {
        for (TreeNode menuTree : trees) {
            for (Integer integer : menuList) {
                if (menuTree.getId() == integer) {
                    menuTree.setChecked(true);
                }
            }
            List<TreeNode> children = menuTree.getChildren();
            if (children != null && children.size() > 0) {
                for (TreeNode treeNode : children) {
                    deptSelected(children, menuList);
                }
            }
        }
        return trees;
    }


    /**
     * 更新角色数据权限
     *
     * @param roleId
     * @param dsType
     * @param dsScope
     * @return
     */
    @GetMapping("/updateRoledataPower")
    public R<Boolean> updateRoledataPower(@RequestParam(required = true) Integer roleId,
                                          @RequestParam(required = true) Integer dsType,
                                          @RequestParam(required = false) String dsScope) {
        SysRole sysRole = new SysRole();
        sysRole.setRoleId(roleId);
        sysRole.setDsType(dsType);
        if (dsType != 1) {
            dsScope = "";

        }
        sysRole.setDsScope(dsScope);
        boolean b = sysRoleService.updateById(sysRole);
        return new R<>(b);
    }

    /**
     * 根据角色查询用户
     *
     * @param roles
     * @return
     */
    @PostMapping("/selectUserByRole")
    public List<SysUser> selectUserByRole(@RequestParam List<Integer> roles) {
        List list = new ArrayList();
        if (roles != null && roles.size() > 0) {
            list = sysRoleService.selectUserByRole(roles);
        }
        return list;
    }
}
