package com.huazheng.tunny.admin.mapper;

import com.huazheng.tunny.admin.api.entity.SysMicro;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 * 子应用  mapper层
 *
 * <AUTHOR> code generator
 * @date 2022-06-15 17:20:52
 */
public interface SysMicroMapper extends BaseMapper<SysMicro> {
    /**
     * 查询子应用信息
     *
     * @param microId 子应用ID
     * @return 子应用信息
     */
    public SysMicro selectSysMicroById(Integer microId);

    /**
     * 查询子应用列表
     *
     * @param sysMicro 子应用信息
     * @return 子应用集合
     */
    public List<SysMicro> selectSysMicroList(SysMicro sysMicro);

    /**
     * 模糊查询子应用列表
     *
     * @param sysMicro 子应用信息
     * @return 子应用集合
     */
    public List<SysMicro> selectSysMicroListByLike(SysMicro sysMicro);


    /**
     * 分页模糊查询子应用列表
     *
     * @param sysMicro 子应用信息
     * @return 子应用集合
     */
    public List<SysMicro> selectSysMicroListByLike(Query query, SysMicro sysMicro);


    /**
     * 新增子应用
     *
     * @param sysMicro 子应用信息
     * @return 结果
     */
    public int insertSysMicro(SysMicro sysMicro);

    /**
     * 修改子应用
     *
     * @param sysMicro 子应用信息
     * @return 结果
     */
    public int updateSysMicro(SysMicro sysMicro);

    /**
     * 删除子应用
     *
     * @param microId 子应用ID
     * @return 结果
     */
    public int deleteSysMicroById(Integer microId);

    /**
     * 批量删除子应用
     *
     * @param microIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysMicroByIds(Integer[] microIds);


}
