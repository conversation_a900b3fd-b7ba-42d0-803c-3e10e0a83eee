package com.huazheng.tunny.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.aliyuncs.CommonResponse;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.admin.api.dto.UserDTO;
import com.huazheng.tunny.admin.api.dto.UserInfo;
import com.huazheng.tunny.admin.api.entity.SysRole;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.admin.api.entity.SysUserPost;
import com.huazheng.tunny.admin.api.entity.SysUserRole;
import com.huazheng.tunny.admin.api.vo.MenuVO;
import com.huazheng.tunny.admin.api.vo.UserVO;
import com.huazheng.tunny.admin.mapper.SysDeptMapper;
import com.huazheng.tunny.admin.mapper.SysUserMapper;
import com.huazheng.tunny.admin.service.*;
import com.huazheng.tunny.common.core.constant.enums.EnumLoginType;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.data.enums.DataScopeTypeEnum;
import com.huazheng.tunny.common.security.util.SecurityUtils;
import com.huazheng.tunny.tools.file.AttackmentDto;
import com.huazheng.tunny.tools.file.FastDfsUtil;
import com.huazheng.tunny.tools.number.RandomUtil;
import com.huazheng.tunny.tools.sms.SmsUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2017/10/31
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();
    private final SysMenuService sysMenuService;
    private final SysUserMapper sysUserMapper;
    private final SysDeptMapper sysDeptMapper;
    private final SysRoleService sysRoleService;
    private final SysUserRoleService sysUserRoleService;
    //懒得写mapper了直接用 hutool db
    private final DataSource dataSource;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 验证码校验限制次数
    private static final Integer REDIS_CODE_CHECKE_NUMBER = 5;

    /**
     * 通过用户名查用户的全部信息
     *
     * @param username 用户名
     * @return
     */
    @Override
  //  @Cacheable(value = "user_details", key = "#username", unless="#result == null")
    public UserInfo findUserInfo(String type, String username) {
        SysUser condition = new SysUser();
        if (EnumLoginType.PWD.getType().equals(type)) {
            condition.setUsername(username);
        } else if (EnumLoginType.WECHAT.getType().equals(type)) {
            condition.setWxOpenid(username);
        } else if (EnumLoginType.MOBILE.getType().equals(type)) {

            condition.setPhone(username);
        }  else {
            condition.setQqOpenid(username);
        }
        SysUser sysUser = this.selectOne(new EntityWrapper<>(condition));
        if (sysUser == null) {
            return null;
        }

        UserInfo userInfo = new UserInfo();
        userInfo.setSysUser(sysUser);
        //设置角色列表
        List<SysRole> roleList = sysRoleService.findRolesByUserId(sysUser.getUserId());
        List<String> roleCodes = new ArrayList<>();
        //角色对应的数据权限信息
        List<Integer> dsTypeCodes = new ArrayList<>();
        if (CollUtil.isNotEmpty(roleList)) {
            roleList.forEach((sysRole )->{
                roleCodes.add(sysRole.getRoleCode());

                dsTypeCodes.add(sysRole.getDsType());
            });
        }
        userInfo.setRoles(ArrayUtil.toArray(roleCodes, String.class));

        //取dsType最小值
        userInfo.setDsType(ArrayUtil.min(ArrayUtil.toArray(dsTypeCodes, Integer.class)));

        //对于绑定多个角色的用户，ds_scop 取 哪个的数值？
        //获取ds_scop数据  自定义与本级及子级
        try {
            List<Integer>  deptids= initDeptIds(sysUser,convertListToString(roleCodes));


            userInfo.setDataScope( ArrayUtil.join(deptids.toArray(),"_"));
        } catch (SQLException e) {
            e.printStackTrace();
        }

        //设置权限列表（menu.permission）
        Set<MenuVO> menuVoSet = new HashSet<>();
        for (String role : roleCodes) {
            List<MenuVO> menuVos = sysMenuService.findMenuByRoleCode(role, null);
            menuVoSet.addAll(menuVos);
        }
        Set<String> permissions = new HashSet<>();
        for (MenuVO menuVo : menuVoSet) {
            if (StringUtils.isNotEmpty(menuVo.getPermission())) {
                String permission = menuVo.getPermission();
                permissions.add(permission);
            }
        }
        userInfo.setPermissions(ArrayUtil.toArray(permissions, String.class));
        return userInfo;
    }
    //根据dsTypes获取部门list
    public  List<Integer>   initDeptIds(   SysUser sysUser,String rolecodes) throws SQLException {

        List<Integer> deptIds=new ArrayList<>();
        String roleQuery = "SELECT * FROM sys_role where role_code IN (" + rolecodes + ")";
        //若多角色对应多权限信息则取最小权限信息
        Entity query = Db.use(dataSource)
                .query(roleQuery)
                .stream().min(Comparator.comparingInt(o -> o.getInt("ds_type"))).get();

        Integer dsType = query.getInt("ds_type");

        // 自定义
        if (DataScopeTypeEnum.CUSTOM.getType() == dsType) {
            //获取自定义的部门List 清单
            String dsScope = query.getStr("ds_scope");
            deptIds.addAll(Arrays.stream(dsScope.split(",")).map(Integer::parseInt)
                    .collect(Collectors.toList()));
        }
        // 查询本级及其下级
        if (DataScopeTypeEnum.OWN_CHILD_LEVEL.getType() == dsType) {
            String ids = Db.use(dataSource).queryString("select getDeptChildList( ? );", sysUser.getDeptId()).replace("^,", "");
            String[] idsarray = ids.split(",");
            Integer[] inIds = (Integer[]) ConvertUtils.convert(idsarray, Integer.class);
            List<Integer> deptIdList = Arrays.asList(inIds);
            deptIds.addAll(deptIdList);
        }
        // 只查询本级
        if (DataScopeTypeEnum.OWN_LEVEL.getType() == dsType) {
            deptIds.add(sysUser.getDeptId());
        }
        return deptIds;
    }

    @Override
    public Page selectWithRolePage(Query query) {
        List list = new ArrayList<>();
        Object username = query.getCondition().get("username");
        Object deptId = query.getCondition().get("deptId");
        Object userSearch = query.getCondition().get("userSearch");
        //先根据父ID查询出所有子ID

        String deptIds = sysDeptMapper.getChildrenOrgOfDept(Integer.parseInt((String) deptId));
        if (deptIds != null && !"".equals(deptIds)) {
            List<Integer> ids = new ArrayList<>();
            String[] split = deptIds.split(",");
            if (split != null && split.length > 0) {
                for (String s : split) {
                    if (s != null && !"".equals(s)) {
                        ids.add(Integer.valueOf(s));
                    }
                }
            }
            //根据子ID查询出所有的菜单
            list = sysUserMapper.selectUserVoPage(query, username, ids, userSearch);
        }
        query.setRecords(list);
        return query;
    }

    /**
     * 通过ID查询用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @Override
    public UserVO selectUserVoById(Integer id) {
        List<Integer> deptList = new ArrayList<>();
        UserVO userVO = sysUserMapper.selectUserVoById(id);
        if (userVO.getDeptId() != null) {
            deptList = sysDeptMapper.getParentOrgOfDept(userVO.getDeptId());
        }
        userVO.setDeptList(deptList);
        //查询用户信息时移除密码
        userVO.setPassword(null);
        return userVO;
    }

    @Override
    public void insertUserPost(SysUserPost userPost) {
        sysUserMapper.insertUserPost(userPost);
    }


    /**
     * 删除用户
     *
     * @param sysUser 用户
     * @return Boolean
     */
    @Override
    @CacheEvict(value = "user_details", key = "#sysUser.username")
    public Boolean deleteUserById(SysUser sysUser) {
        sysUserRoleService.deleteByUserId(sysUser.getUserId());
        this.deleteById(sysUser.getUserId());
        return Boolean.TRUE;
    }

    @Override
    @CacheEvict(value = "user_details", key = "#username")
    public R<Boolean> updateUserInfo(UserDTO userDto, String username) {
        UserVO userVO = sysUserMapper.selectSimpleUserVoByUsername(username);
        SysUser sysUser = new SysUser();
        if (StrUtil.isNotBlank(userDto.getPassword())
                && StrUtil.isNotBlank(userDto.getNewpassword1())) {
            //if (ENCODER.matches(userDto.getPassword(), userVO.getPassword())) {
            //sysUser.setPassword(ENCODER.encode(userDto.getNewpassword1()));
            if (userVO.getPassword().toLowerCase(Locale.ENGLISH).equals(SecureUtil.md5((String) userDto.getPassword()))) {
                sysUser.setPassword(SecureUtil.md5(userDto.getNewpassword1()));
                //更新密码过期时间
                sysUser.setPasswordEndtime(LocalDateTime.now().plus(30, ChronoUnit.DAYS));
            } else {
                log.warn("原密码错误，修改密码失败:{}", username);
                return new R<>(Boolean.FALSE, "原密码错误，修改失败");
            }
        }
        sysUser.setPhone(userDto.getPhone());
        sysUser.setUserId(userVO.getUserId());
        sysUser.setAvatar(userDto.getAvatar());
        return new R<>(this.updateById(sysUser));
    }


    @Override
    public R<Boolean> sendAuthCode(String params) {
        Boolean b = Boolean.TRUE;
        String msg = "验证码发送成功";
        String email = null;
        String phone = null;

        //验证账号是否存在
        if (params != null && !"".equals(params)) {
            //判断是邮箱还是手机
            if (params.indexOf("@") != -1) {
                //邮箱
                email = params;
            } else {
                //手机
                phone = params;
            }
            //根据认证账号查询是否有效
            SysUser user = sysUserMapper.selectAuthCode(email, phone);
            if (user != null) {
                //随机生成6位数密码(逗号后附加校验次数)
                String verifyCode = String.valueOf(RandomUtil.secureRandom().nextInt(899999) + 100000);

                //将验证码存入redis
                RedisSerializer<String> stringSerializer = new StringRedisSerializer();
                redisTemplate.setValueSerializer(stringSerializer);
                redisTemplate.opsForValue().set(params, verifyCode + ":0", 5, TimeUnit.MINUTES);

                //发送验证（手机或者邮箱）
                if (params.indexOf("@") != -1) {
                    //邮箱

                } else {
                    //手机
//                    b = SmsSendUtil.SmsSend(params, "您的验证码是：" + verifyCode + "(验证码有效时间5分钟)【产城创】");
                    String smsParam = "{\"code\":\"" + verifyCode + "\"}";
                    CommonResponse commonResponse = SmsUtil.sendSms(phone, smsParam, "华正云平台", "SMS_238461834");
                    if (200 == commonResponse.getHttpStatus()) {
                        b = Boolean.TRUE;
                    }
                }
                msg = "验证码发送成功";
            }
        }
        return new R<>(b, msg);
    }

    @Override
    public R<Boolean> contrastAuthCode(String authCode, String encrypted) {
        Boolean b = Boolean.FALSE;
        String msg = "验证码不正确";
        //将验证码存入redis
        Object redisObj = redisTemplate.opsForValue().get(encrypted);
        if (redisObj != null) {
            String redisCode = redisObj.toString();
            String[] redisCodeDetails = redisCode.split(":");
            //验证码校验次数超过5次则认定为失效
            if(Integer.valueOf(redisCodeDetails[1]) <= REDIS_CODE_CHECKE_NUMBER) {
                if (redisCodeDetails[0].equals(authCode)) {
                    b = Boolean.TRUE;
                    msg = "验证成功";
                } else {
                    redisTemplate.opsForValue().set(encrypted, redisCodeDetails[0] + ":" + (Integer.valueOf(redisCodeDetails[1]) + 1), 5, TimeUnit.MINUTES);
                }
            }
        }
        return new R<>(b, msg);
    }

    @Override
    public R<Boolean> updatePassword(String encrypted, String newpassword1, String authCode) {
        Boolean b = Boolean.FALSE;
        String msg = "修改失败";

        //验证码二次校验
        Object redisObj = redisTemplate.opsForValue().get(encrypted);
        if (redisObj != null) {
            String redisCode = redisObj.toString();
            String[] redisCodeDetails = redisCode.split(":");
            // 取到值后，立即销毁redis中的验证码，防止被爆破
            redisTemplate.delete(encrypted);
            // 验证码已校验超过5次（防止失效验证码） 或 前置页面输入的验证码与实际验证码做二次匹配未通过（防止绕过验证码校验直接调接口修改密码）
            if(Integer.valueOf(redisCodeDetails[1]) > REDIS_CODE_CHECKE_NUMBER || !redisCodeDetails[0].equals(authCode)) {
                return new R<>(b, msg);
            }
        }else {
            return new R<>(b, msg);
        }

        String email = null;
        String phone = null;
        SysUser sysUser = new SysUser();

        //根据手机号或者邮箱查询出账号详情
        if (encrypted != null && !"".equals(encrypted)) {
            if (encrypted.indexOf("@") != -1) {
                email = encrypted;
            } else {
                phone = encrypted;
            }

            //根据认证账号查询是否有效
            SysUser user = sysUserMapper.selectAuthCode(email, phone);
            if (user != null) {
                sysUser.setUserId(user.getUserId());
                sysUser.setPassword(SecureUtil.md5(newpassword1));
                this.updateById(sysUser);
                b = Boolean.TRUE;
                msg = "修改成功";
            }
        }
        return new R<>(b, msg);
    }

    @Override
    public void userPosDummy(Integer userId, String posDummys) {
        sysUserMapper.deleteUserPosDummy(userId);
        if (userId != null && posDummys != null && !"".equals(posDummys)) {
            List list = new ArrayList();
            String[] split = posDummys.split(",");
            for (String s : split) {
                list.add(Integer.valueOf(s));
            }
            sysUserMapper.insertUserPosDummy(userId, list);
        }
    }

    @Override
    public AttackmentDto uploadSignName(MultipartFile[] files) {
        AttackmentDto dto = new AttackmentDto();
        dto.setCode(500);
        dto.setMsg("电子签名上传异常");
        List<AttackmentDto> dtos = FastDfsUtil.webUpload(files, "");
        if (dtos != null && dtos.size() > 0) {
            dto = dtos.get(0);
        }
        return dto;
    }


    @Override
    @CacheEvict(value = "user_details", key = "#username")
    @Transactional
    public Boolean updateUser(UserDTO userDto, String username) {
        SysUser sysUser = new SysUser();
        BeanUtils.copyProperties(userDto, sysUser);
        sysUser.setUpdateTime(LocalDateTime.now());
        this.updateById(sysUser);

        SysUserRole condition = new SysUserRole();
        condition.setUserId(userDto.getUserId());
        sysUserRoleService.delete(new EntityWrapper<>(condition));
        List<Integer> roles = userDto.getRoles();
        if (roles != null && roles.size() > 0) {
            for (Integer roleId : roles) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(sysUser.getUserId());
                userRole.setRoleId(roleId);
                userRole.insert();
            }
        }
        sysUserMapper.deletePostByUser(userDto.getUserId());
        List<Integer> posts = userDto.getPosts();
        if (posts != null && posts.size() > 0) {
            for (Integer postId : posts) {
                SysUserPost userPost = new SysUserPost();
                userPost.setUserId(sysUser.getUserId());
                userPost.setPsotId(postId);
                this.insertUserPost(userPost);
            }
        }
        return Boolean.TRUE;
    }
    /**
     * 将List<String>集合 转化为String
     */
    public String convertListToString(List<String> strlist) {
        StringBuffer sb = new StringBuffer();
        if (CollectionUtils.isNotEmpty(strlist)) {
            for (int i = 0; i < strlist.size(); i++) {
                if (i == 0) {
                    sb.append("'").append(strlist.get(i)).append("'");
                } else {
                    sb.append(",").append("'").append(strlist.get(i)).append("'");
                }
            }
        }
        return sb.toString();
    }
    /**
     * 将List<String>集合 转化为String
     */
    public String convertIntListToString(List<Integer> strlist) {
        StringBuffer sb = new StringBuffer();
        if (CollectionUtils.isNotEmpty(strlist)) {
            for (int i = 0; i < strlist.size(); i++) {
                if (i == 0) {
                    sb.append("'").append(strlist.get(i)).append("'");
                } else {
                    sb.append(",").append("'").append(strlist.get(i)).append("'");
                }
            }
        }
        return sb.toString();
    }
}
