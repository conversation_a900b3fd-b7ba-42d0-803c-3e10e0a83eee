package com.huazheng.tunny.admin.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.admin.mapper.SysUserRoleMapper;
import com.huazheng.tunny.admin.service.SysUserRoleService;
import com.huazheng.tunny.admin.api.entity.SysUserRole;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements SysUserRoleService {

	/**
	 * 根据用户Id删除该用户的角色关系
	 *
	 * @param userId 用户ID
	 * @return boolean
	 * <AUTHOR>
	 * @date 2017年12月7日 16:31:38
	 */
	@Override
	public Boolean deleteByUserId(Integer userId) {
		return baseMapper.deleteByUserId(userId);
	}
}
