package com.huazheng.tunny.admin.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.admin.api.entity.SysDictIndex;
import com.huazheng.tunny.admin.service.SysDictIndexService;
import com.huazheng.tunny.common.core.constant.CommonConstant;
import com.huazheng.tunny.common.core.util.Query;
import com.huazheng.tunny.common.core.util.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 字典
 *
 * <AUTHOR> code generator
 * @date 2019-03-08 10:41:04
 */
@RestController
@RequestMapping("/sysdictindex")
public class SysDictIndexController {
    @Autowired
    private SysDictIndexService sysDictIndexService;


    /**
     * 列表
     *
     * @param params
     * @return
     */
    @GetMapping("/page")
    public Page page(@RequestParam Map<String, Object> params) {
        //数据库字段值完整查询
        // return  sysDictIndexService.selectPage(new Query<>(params), new EntityWrapper<>());
        //对象模糊查询
        return sysDictIndexService.selectSysDictIndexListByLike(new Query<>(params));
    }


    /**
     * 信息
     *
     * @param id
     * @return R
     */
    @GetMapping("/{id}")
    public R info(@PathVariable("id") Integer id) {
        SysDictIndex sysDictIndex = sysDictIndexService.selectById(id);
        return new R<>(sysDictIndex);
    }

    /**
     * 保存
     *
     * @param sysDictIndex
     * @return R
     */
    @PostMapping
    public R save(@RequestBody SysDictIndex sysDictIndex) {
        sysDictIndex .setDelFlag(CommonConstant.STATUS_NORMAL);
        sysDictIndexService.insert(sysDictIndex);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 修改
     *
     * @param sysDictIndex
     * @return R
     */
    @PutMapping
    public R update(@RequestBody SysDictIndex sysDictIndex) {
        sysDictIndexService.updateById(sysDictIndex);
        return new R<>(Boolean.TRUE);
    }


    /**
     * 删除
     *
     * @param id
     * @return R
     */
    @DeleteMapping("/{id}")
    public R delete(@PathVariable Integer id) {
        sysDictIndexService.deleteById(id);
        return new R<>(Boolean.TRUE);
    }

    /**
     * 根据分类ID查询字典列表
     *
     * @param typeId
     * @return
     */
    @GetMapping("/selectDictIndex")
    public List selectDictIndex(@RequestParam(required = true, value = "typeId") Integer typeId) {
        List list = sysDictIndexService.selectDictIndex(typeId);
        return list;
    }
}
