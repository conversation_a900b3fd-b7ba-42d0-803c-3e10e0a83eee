package com.huazheng.tunny.admin.service.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.huazheng.tunny.admin.api.entity.SysLog;
import com.huazheng.tunny.admin.mapper.SysLogMapper;
import com.huazheng.tunny.admin.service.SysLogService;
import com.huazheng.tunny.common.core.constant.CommonConstant;
import com.huazheng.tunny.common.core.util.Query;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-11-20
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements SysLogService {
    private final SysLogMapper sysLogMapper;

    @Override
    public Boolean updateByLogId(Long id) {

        SysLog sysLog = new SysLog();
        sysLog.setId(id);
        sysLog.setDelFlag(CommonConstant.STATUS_DEL);
        sysLog.setUpdateTime(LocalDateTime.now());
        return updateById(sysLog);
    }

    @Override
    public Page selectWithPage(Query<Object> query) throws Exception {
        String startTime = null;
        String endTime = null;
        Object type = query.getCondition().get("type");
        Object start = query.getCondition().get("startTime");
        if (start != null) {
            startTime = start.toString();
        }
        Object end = query.getCondition().get("endTime");
        if (end != null) {
            endTime = end.toString();
        }
        List list = sysLogMapper.selectWithPage(query, type, startTime, endTime);
        query.setRecords(list);
        return query;
    }
}
