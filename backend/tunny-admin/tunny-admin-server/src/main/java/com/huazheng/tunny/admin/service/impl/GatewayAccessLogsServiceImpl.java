package com.huazheng.tunny.admin.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.admin.api.common.PageParams;
import com.huazheng.tunny.admin.api.entity.GatewayAccessLogs;
import com.huazheng.tunny.admin.api.util.StringUtils;
import com.huazheng.tunny.admin.mapper.GatewayLogsMapper;
import com.huazheng.tunny.admin.service.GatewayAccessLogsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;

/**
 *
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class GatewayAccessLogsServiceImpl implements GatewayAccessLogsService {

    @Autowired
    private GatewayLogsMapper gatewayLogsMapper;

    /**
     * 分页查询
     *
     * @param pageParams
     * @return
     */
    @Override
    public Page<GatewayAccessLogs> findListPage(PageParams pageParams) {
        GatewayAccessLogs query = pageParams.mapToObject(GatewayAccessLogs.class);
        EntityWrapper<GatewayAccessLogs> entityWrapper = new EntityWrapper();

        entityWrapper.notIn("path", Arrays.asList(new String[]{"/auth/oauth/token"}));
        if(StringUtils.isNotEmpty(query.getPath())){
            entityWrapper.like("path", query.getPath());
        }
        if(StringUtils.isNotEmpty(query.getIp())){
            entityWrapper.eq("ip", query.getIp());
        }
        if(StringUtils.isNotEmpty(query.getServiceId())) {
            entityWrapper.eq("service_id", query.getServiceId());
        }
        entityWrapper.orderBy("request_time", false);

        return pageParams.setRecords(gatewayLogsMapper.selectPage(pageParams, entityWrapper));
    }

    /**
     * 分页查询
     *
     * @param pageParams
     * @return
     */
    @Override
    public Page<GatewayAccessLogs> findLoginListPage(PageParams pageParams) {
        GatewayAccessLogs query = pageParams.mapToObject(GatewayAccessLogs.class);
        EntityWrapper<GatewayAccessLogs> entityWrapper = new EntityWrapper();

        entityWrapper.in("path", Arrays.asList(new String[]{"/auth/oauth/token"}));

        if(StringUtils.isNotEmpty(query.getIp())){
            entityWrapper.eq("ip", query.getIp());
        }
        if(StringUtils.isNotEmpty(query.getParams())) {
            entityWrapper.like("params", "\"username\":[\"" + query.getParams() + "\"]");
        }
        entityWrapper.orderBy("request_time", false);

        return pageParams.setRecords(gatewayLogsMapper.selectPage(pageParams, entityWrapper));
    }
}
