package com.huazheng.tunny.admin.mapper;

import com.huazheng.tunny.admin.api.entity.SysMicroMenu;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 子应用菜单  mapper层
 *
 * <AUTHOR> code generator
 * @date 2022-06-15 17:21:18
 */
public interface SysMicroMenuMapper extends BaseMapper<SysMicroMenu> {
    /**
     * 查询子应用菜单信息
     *
     * @param menuId 子应用菜单ID
     * @return 子应用菜单信息
     */
    public SysMicroMenu selectSysMicroMenuById(Integer menuId);

    /**
     * 查询子应用菜单列表
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 子应用菜单集合
     */
    public List<SysMicroMenu> selectSysMicroMenuList(SysMicroMenu sysMicroMenu);

    /**
     * 模糊查询子应用菜单列表
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 子应用菜单集合
     */
    public List<SysMicroMenu> selectSysMicroMenuListByLike(SysMicroMenu sysMicroMenu);


    /**
     * 分页模糊查询子应用菜单列表
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 子应用菜单集合
     */
    public List<SysMicroMenu> selectSysMicroMenuListByLike(Query query, SysMicroMenu sysMicroMenu);


    /**
     * 新增子应用菜单
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 结果
     */
    public int insertSysMicroMenu(SysMicroMenu sysMicroMenu);

    /**
     * 修改子应用菜单
     *
     * @param sysMicroMenu 子应用菜单信息
     * @return 结果
     */
    public int updateSysMicroMenu(SysMicroMenu sysMicroMenu);

    /**
     * 删除子应用菜单
     *
     * @param menuId 子应用菜单ID
     * @return 结果
     */
    public int deleteSysMicroMenuById(Integer menuId);

    /**
     * 批量删除子应用菜单
     *
     * @param menuIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysMicroMenuByIds(Integer[] menuIds);


    List selectWithMicroMenuVoPage(Query query, @Param("parentId") int parseInt, @Param("microId") int microId, @Param("dataSearch") Object dataSearch);
}
