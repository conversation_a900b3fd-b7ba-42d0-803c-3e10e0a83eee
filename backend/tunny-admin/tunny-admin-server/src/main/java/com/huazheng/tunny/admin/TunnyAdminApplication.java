package com.huazheng.tunny.admin;
import com.huazheng.tunny.common.swagger.annotation.EnableTunnySwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 用户统一管理系统
 */
@EnableTunnySwagger2
@EnableFeignClients
@SpringCloudApplication
@ServletComponentScan
@ComponentScan({"com.huazheng.tunny.admin"})
public class TunnyAdminApplication extends SpringBootServletInitializer {
	public static void main(String[] args) {
		SpringApplication.run(TunnyAdminApplication.class, args);
	}
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(TunnyAdminApplication.class);
	}
}
