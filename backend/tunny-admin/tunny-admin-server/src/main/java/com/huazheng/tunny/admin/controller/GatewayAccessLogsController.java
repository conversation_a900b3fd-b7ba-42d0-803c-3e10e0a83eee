package com.huazheng.tunny.admin.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.tunny.admin.api.common.PageParams;
import com.huazheng.tunny.admin.api.common.ResultBody;
import com.huazheng.tunny.admin.api.entity.GatewayAccessLogs;
import com.huazheng.tunny.admin.service.GatewayAccessLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 网关智能路由
 *
 * @description:
 */
@Api(tags = "网关访问日志")
@RestController
public class GatewayAccessLogsController {

    @Autowired
    private GatewayAccessLogsService gatewayAccessLogsService;

    /**
     * 获取分页列表
     *
     * @return
     */
    @ApiOperation(value = "获取访问日志分页列表", notes = "获取访问日志分页列表")
    @GetMapping("/gateway/access/logs")
    public ResultBody<Page<GatewayAccessLogs>> getAccessLogListPage(@RequestParam(required = false) Map map) {
        return ResultBody.ok().data(gatewayAccessLogsService.findListPage(new PageParams(map)));
    }

    /**
     * 获取分页列表
     *
     * @return
     */
    @ApiOperation(value = "获取登录日志分页列表", notes = "获取登录日志分页列表")
    @GetMapping("/gateway/login/logs")
    public ResultBody<Page<GatewayAccessLogs>> getLoginLogListPage(@RequestParam(required = false) Map map) {
        return ResultBody.ok().data(gatewayAccessLogsService.findLoginListPage(new PageParams(map)));
    }

}
