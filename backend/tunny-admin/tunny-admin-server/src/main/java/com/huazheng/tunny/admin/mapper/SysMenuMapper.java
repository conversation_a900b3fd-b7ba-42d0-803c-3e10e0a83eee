package com.huazheng.tunny.admin.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.admin.api.entity.SysMenu;
import com.huazheng.tunny.admin.api.vo.MenuVO;
import com.huazheng.tunny.common.core.util.Query;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * <p>
 * 菜单权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysMenuMapper extends BaseMapper<SysMenu> {

	/**
	 * 通过角色编号查询菜单
	 *
	 * @param role 角色编号
	 * @return
	 */
	List<MenuVO> findMenuByRoleCode(String role, String microCode);

	/**
	 * 通过角色ID查询权限
	 *
	 * @param roleIds Ids
	 * @return
	 */
	List<String> findPermissionsByRoleIds(String roleIds);

	List selectWithMenuVoPage(Query query, @Param("parentId") int parseInt, @Param("dataSearch") Object dataSearch);

	List<Integer> getChildrenOrgOfMenu(int parseInt);

	List<Integer> getParentOrgOfMenu(int parentId);

    List getMenuByType(Integer menuType);
}
