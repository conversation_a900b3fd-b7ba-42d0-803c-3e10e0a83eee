package com.huazheng.tunny.admin.mapper;

import com.huazheng.tunny.admin.api.entity.BpmDemo;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.common.core.util.Query;
import java.util.List;
/**
 *   mapper层
 *
 * <AUTHOR> code generator
 * @date 2022-07-11 13:46:26
 */
public interface BpmDemoMapper extends BaseMapper<BpmDemo> {
    /**
     * 查询信息
     *
     * @param id ID
     * @return 信息
     */
    public BpmDemo selectBpmDemoById(Integer id);

    /**
     * 查询列表
     *
     * @param bpmDemo 信息
     * @return 集合
     */
    public List<BpmDemo> selectBpmDemoList(BpmDemo bpmDemo);

    /**
     * 模糊查询列表
     *
     * @param bpmDemo 信息
     * @return 集合
     */
    public List<BpmDemo> selectBpmDemoListByLike(BpmDemo bpmDemo);


    /**
     * 分页模糊查询列表
     *
     * @param bpmDemo 信息
     * @return 集合
     */
    public List<BpmDemo> selectBpmDemoListByLike(Query query, BpmDemo bpmDemo);


    /**
     * 新增
     *
     * @param bpmDemo 信息
     * @return 结果
     */
    public int insertBpmDemo(BpmDemo bpmDemo);

    /**
     * 修改
     *
     * @param bpmDemo 信息
     * @return 结果
     */
    public int updateBpmDemo(BpmDemo bpmDemo);

    /**
     * 删除
     *
     * @param id ID
     * @return 结果
     */
    public int deleteBpmDemoById(Integer id);

    /**
     * 批量删除
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBpmDemoByIds(Integer[] ids);



}
