package com.huazheng.tunny.admin.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.huazheng.tunny.admin.api.entity.SysDictIndex;
import com.huazheng.tunny.common.core.util.Query;

import java.util.List;

/**
 *   mapper层
 *
 * <AUTHOR> code generator
 * @date 2019-03-08 10:41:04
 */
public interface SysDictIndexMapper extends BaseMapper<SysDictIndex> {
    /**
     * 查询信息
     *
     * @param id ID
     * @return 信息
     */
    public SysDictIndex selectSysDictIndexById(Integer id);

    /**
     * 查询列表
     *
     * @param sysDictIndex 信息
     * @return 集合
     */
    public List<SysDictIndex> selectSysDictIndexList(SysDictIndex sysDictIndex);

    /**
     * 模糊查询列表
     *
     * @param sysDictIndex 信息
     * @return 集合
     */
    public List<SysDictIndex> selectSysDictIndexListByLike(SysDictIndex sysDictIndex);


    /**
     * 分页模糊查询列表
     *
     * @param sysDictIndex 信息
     * @return 集合
     */
    public List<SysDictIndex> selectSysDictIndexListByLike(Query query, SysDictIndex sysDictIndex);


    /**
     * 新增
     *
     * @param sysDictIndex 信息
     * @return 结果
     */
    public int insertSysDictIndex(SysDictIndex sysDictIndex);

    /**
     * 修改
     *
     * @param sysDictIndex 信息
     * @return 结果
     */
    public int updateSysDictIndex(SysDictIndex sysDictIndex);

    /**
     * 删除
     *
     * @param id ID
     * @return 结果
     */
    public int deleteSysDictIndexById(Integer id);

    /**
     * 批量删除
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSysDictIndexByIds(String[] ids);

}
