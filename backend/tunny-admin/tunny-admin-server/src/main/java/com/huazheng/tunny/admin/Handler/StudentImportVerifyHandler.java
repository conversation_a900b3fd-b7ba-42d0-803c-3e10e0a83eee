//package com.huazheng.tunny.admin.Handler;
//
//import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
//import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
//import com.huazheng.tunny.admin.api.entity.StudentEntity;
//import org.springframework.stereotype.Component;
//
//import java.util.Arrays;
//import java.util.StringJoiner;
//
///**
// * EasyPoi自定义导入校验处理器
// * */
//@Component
//public class StudentImportVerifyHandler implements IExcelVerifyHandler<StudentEntity> {
//
//    @Override
//    public ExcelVerifyHandlerResult verifyHandler(StudentEntity inputEntity) {
//        StringJoiner joiner = new StringJoiner(",");
//
//        if("零班".equals(inputEntity.getClassNum())){
//            joiner.add("班级名称不存在零班");
//        }
//
//        if (joiner.length() != 0) {
//            return new ExcelVerifyHandlerResult(false, joiner.toString());
//        }
//        return new ExcelVerifyHandlerResult(true);
//    }
//}
