package com.huazheng.tunny.admin.api.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
*/
@TableName("gateway_route")
@Data
public class GatewayRoute implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ID_WORKER)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long routeId;

    private String routeName;

    private String serviceId;

    private String uri;

    private String stripPrefix;

    private String predicatesPath;

    private String predicates;

    private String filters;

    private String routeOrder;

    private String creatorId;

    private Date createDate;

    private String updateId;

    private Date updateDate;

    private String remarks;

    private String delFlag;

    private String removeRequestHeader;
    private String swaggerHeaderFilter;
    private String passwordDecoderFilter;
    private String imageCodeGatewayFilter;
    private String requestRateLimiter;
    private String hystrix;
}
