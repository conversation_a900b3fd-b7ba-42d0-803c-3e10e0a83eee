package com.huazheng.tunny.admin.api.feign.fallback;

import com.huazheng.tunny.admin.api.entity.GatewayRoute;
import com.huazheng.tunny.admin.api.feign.RemoteGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RemoteGatewayServiceFallbackImpl implements RemoteGatewayService {

	@Override
	public String refresh() {
		log.error("网关刷新feign调用失败");
		return null;
	}

	@Override
	public void saveRoute(GatewayRoute gatewayRoute) {
		log.error("网关新增删除feign调用失败");
	}

	@Override
	public void update(GatewayRoute gatewayRoute) {
		log.error("网关配置更新feign调用失败");
	}

	@Override
	public void deleteRoute(GatewayRoute gatewayRoute) {
		log.error("网关配置删除feign调用失败");
	}
}
