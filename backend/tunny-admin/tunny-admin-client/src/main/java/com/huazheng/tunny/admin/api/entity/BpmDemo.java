package com.huazheng.tunny.admin.api.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR> code generator
 * @date 2022-07-11 13:46:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bpm_demo")
public class BpmDemo extends Model<BpmDemo> {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 合同名称
     */
    private String formName;
    /**
     * 合同内容
     */
    private String formContent;
    /**
     * 合同金额
     */
    private String contractAmount;
    /**
     * 流程实例id
     */
    private String procId;
    /**
     * 流程状态
     */
    private String bpmStatus;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建人姓名
     */
    private String createByName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    /**
     * 0-删除，1-正常
     */
    private String delFlag;

    /**
     * 查询标识
     * */
    //非数据库字段
    @TableField(exist = false)
    private String processFlag;

    /**
     * 查询标识
     * */
    //非数据库字段
    @TableField(exist = false)
    private List pendingProcIds;

    /**
     * 流程任务ID
     * */
    //非数据库字段
    @TableField(exist = false)
    private String taskId;

    /**
     * 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
