package com.huazheng.tunny.admin.api.util;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class StudentSexConverter implements Converter<Integer> {

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    //转换为
    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String stringValue = cellData.getStringValue();
        if (stringValue == null) {
            throw new RuntimeException("学生性别填写为空");
        }
        switch (stringValue){
            case "男生":
                return 1;
            case "女生":
                return 2;
            default:
                return 0;
        }
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer obj, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        CellData cellData = new CellData();
        switch (obj){
            case 1:
                return new WriteCellData("男生");
            case 2:
                return new WriteCellData("女生");
            default:
                return new WriteCellData("外星人");
        }
    }
}
