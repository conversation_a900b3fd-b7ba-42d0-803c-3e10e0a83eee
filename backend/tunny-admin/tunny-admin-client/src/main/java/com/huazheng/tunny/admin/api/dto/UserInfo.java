package com.huazheng.tunny.admin.api.dto;

import com.huazheng.tunny.admin.api.entity.SysUser;

import java.io.Serializable;

/**
 * <p>
 * commit('SET_ROLES', data)
 * commit('SET_NAME', data)
 * commit('SET_AVATAR', data)
 * commit('SET_INTRODUCTION', data)
 * commit('SET_PERMISSIONS', data)
 */
public class UserInfo implements Serializable {
	/**
	 * 用户基本信息
	 */
	private SysUser sysUser;
	/**
	 * 权限标识集合
	 */
	private String[] permissions;

	/**
	 * 角色集合
	 */
	private String[] roles;

	/**
	 * 数据权限类别集合--取最小数值-最大权限
	 * @return
	 */
	private Integer dsType;

	/**
	 * 获取自定义或本级及子级的部门清单
	 * @return
	 */
	private String dataScope;

	public String getDataScope() {
		return dataScope;
	}

	public void setDataScope(String dataScope) {
		this.dataScope = dataScope;
	}

	public Integer getDsType() {
		return dsType;
	}

	public void setDsType(Integer dsType) {
		this.dsType = dsType;
	}

	public SysUser getSysUser() {
		return sysUser;
	}

	public void setSysUser(SysUser sysUser) {
		this.sysUser = sysUser;
	}

	public String[] getPermissions() {
		return permissions;
	}

	public void setPermissions(String[] permissions) {
		this.permissions = permissions;
	}

	public String[] getRoles() {
		return roles;
	}

	public void setRoles(String[] roles) {
		this.roles = roles;
	}
}
