package com.huazheng.tunny.admin.api.dto;

import com.huazheng.tunny.admin.api.entity.SysUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
public class
UserDTO extends SysUser {
	/**
	 * 角色ID
	 */
	private List<Integer> roles;
	private List<Integer> posts;
	/**
	 * 新密码
	 */
	private String newpassword1;

	/**
	 * 密保工具(邮箱或者手机号)
	 */
	private String encrypted;
}
