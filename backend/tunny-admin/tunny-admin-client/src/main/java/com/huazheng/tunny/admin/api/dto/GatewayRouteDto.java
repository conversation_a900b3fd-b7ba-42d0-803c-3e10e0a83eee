package com.huazheng.tunny.admin.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatewayRouteDto {

    private Long routeId;

    private String routeName;

    private String serviceId;

    private String uri;

    private String stripPrefix;

    private String predicatesPath;

    private String delFlag;

    private String removeRequestHeader;
    private String swaggerHeaderFilter;
    private String passwordDecoderFilter;
    private String imageCodeGatewayFilter;
    private String requestRateLimiter;
    private String hystrix;
}
