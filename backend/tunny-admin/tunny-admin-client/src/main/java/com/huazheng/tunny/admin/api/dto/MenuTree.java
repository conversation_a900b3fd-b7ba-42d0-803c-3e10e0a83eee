package com.huazheng.tunny.admin.api.dto;

import com.huazheng.tunny.admin.api.vo.MenuVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 菜单树
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MenuTree extends TreeNode {
	private String icons;
	private String name;
	private boolean spread = false;
	private String path;
	private String component;
	private String authority;
	private String redirect;
	private String code;
	private String type;
	private String label;
	private Integer sort;
	private Integer outside;

	private Integer microId;
	private String microName;
	private String microCode;
	private String microPath;
	private String microMenuParentPath;
	private Integer microMenuId;
	private Integer microMenuParentId;
	/**
	 * 路由名称
	 */
	private String routerName;
	/**
	 * 是否缓存(0否1是)
	 */
	private Integer isCache;
	/**
	 * 是否隐藏(0否1是)
	 */
	private Integer isHidden;

	public MenuTree() {
	}

	public MenuTree(int id, String name, int parentId) {
		this.id = id;
		this.parentId = parentId;
		this.name = name;
		this.label = name;
	}

	public MenuTree(int id, String name, MenuTree parent) {
		this.id = id;
		this.parentId = parent.getId();
		this.name = name;
		this.label = name;
	}

	public MenuTree(MenuVO menuVo) {
		this.id = menuVo.getMenuId();
		this.parentId = menuVo.getParentId();
		this.icons = menuVo.getIcons();
		this.name = menuVo.getName();
		this.path = menuVo.getPath();
		this.component = menuVo.getComponent();
		this.type = menuVo.getType();
		this.label = menuVo.getName();
		this.sort = menuVo.getSort();
		this.outside = menuVo.getOutside();
		this.microId = menuVo.getMicroId();
		this.microCode = menuVo.getMicroCode();
		this.microPath = menuVo.getMicroPath();
		this.microMenuParentPath = menuVo.getMicroMenuParentPath();
		this.routerName = menuVo.getRouterName();
		this.isCache = menuVo.getIsCache();
		this.isHidden = menuVo.getIsHidden();
	}
}
