package com.huazheng.tunny.admin.api.feign;

import com.huazheng.tunny.admin.api.entity.GatewayRoute;
import com.huazheng.tunny.admin.api.feign.fallback.RemoteGatewayServiceFallbackImpl;
import feign.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "tunny-gateway", fallback = RemoteGatewayServiceFallbackImpl.class)
public interface RemoteGatewayService {

    /**
     * 刷新路由配置
     */
    @GetMapping("/route/refresh")
    public String refresh();

    /**
     * 新增指定路由配置
     */
    @PostMapping("/route/saveRoute")
    public void saveRoute(@RequestBody GatewayRoute gatewayRoute);

    /**
     * 修改指定路由配置
     */
    @PostMapping("/route/update")
    public void update(@RequestBody GatewayRoute gatewayRoute);

    /**
     * 删除指定路由配置
     */
    @PostMapping("/route/deleteRoute")
    public void deleteRoute(@RequestBody GatewayRoute gatewayRoute);
}
