
#!/bin/bash
# 一、定义jar的全名
jar_array=(tunny-basic-server.jar tunny-basic-server1.jar tunny-basic-server2.jar)

# 二、定义jar包部署的路径 
jar_path=(/web/webapps/basic/webapps /web/webapps/basic/webapps /web/webapps/basic/webapps)

# 三、定义jvm参数
JAVA_OPTS="-server -Xms400m -Xmx400m -Xmn300m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m -Xverify:none -XX:+DisableExplicitGC -Djava.awt.headless=true "


# 四、循环jar数组，并执行ps -ef | grep 查看进程是否存在，若不存在，进入jar包路径并启动


while true 
do

	echo "---------------------正在检测中---------------------------------------"

	for(( i=0;i<${#jar_array[@]};i++)) 
	do 

	echo "当前检测项目是：${jar_array[i]}"

	project=`ps -ef | grep ${jar_array[i]} |grep -v grep|wc -l`

	if [ $project -eq 0 ]

	then
	echo "${jar_array[i]} project is closed and will be starting!"

	cd ${jar_path[i]}

	nohup java $JAVA_OPTS -jar ${jar_array[i]} 1>/dev/null 2>"${jar_path[i]}/catalina.out" &

	else

	echo "${jar_array[i]} project is ok!"

	fi

	done
    echo "---------------------检测结束，等待下一轮，等待中------------------------ "
	sleep 60 #每60秒检查一轮

done
