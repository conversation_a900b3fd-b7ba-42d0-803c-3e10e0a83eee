{"info": {"_postman_id": "c0934d95-6c60-4a94-8391-7a6a755eb084", "name": "<PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "获取token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Basic dGVzdDp0ZXN0"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "admin", "type": "text"}, {"key": "password", "value": "123456", "type": "text"}, {"key": "scope", "value": "server", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}]}, "url": {"raw": "http://************:4001/auth/oauth/token", "protocol": "http", "host": ["10", "138", "22", "15"], "port": "4001", "path": ["auth", "o<PERSON>h", "token"]}}, "response": []}, {"name": "刷新token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Basic dGVzdDp0ZXN0"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "refresh_token", "value": "1f7a8bc8-a922-44fb-bf24-7e0992b4db8d", "type": "text"}, {"key": "grant_type", "value": "refresh_token", "type": "text"}]}, "url": {"raw": "http://************:4001/auth/oauth/token", "protocol": "http", "host": ["10", "138", "22", "15"], "port": "4001", "path": ["auth", "o<PERSON>h", "token"]}}, "response": []}, {"name": "获取token 普通客户端", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Basic dGVzdDp0ZXN0"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "admin", "type": "text"}, {"key": "password", "value": "123456", "type": "text"}, {"key": "scope", "value": "server", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}]}, "url": {"raw": "http://************:4001/auth/oauth/token", "protocol": "http", "host": ["10", "138", "22", "15"], "port": "4001", "path": ["auth", "o<PERSON>h", "token"]}}, "response": []}]}