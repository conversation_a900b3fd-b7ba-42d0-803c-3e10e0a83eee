#!/bin/bash
#details:Start the jar file in the upper directory
#Parameter introduction:
# $1:jar filename (with suffix)
# Note: The jar file must be located in the directory above the startup.sh directory.

#Startup parameter
JAVA_OPTS="-server -Xms512m -Xmx521m -Xmn300m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xverify:none -XX:+DisableExplicitGC -Djava.awt.headless=true"

jar_name=$1
this_dir="$( cd "$( dirname "$0"  )" && pwd )"
parent_dir=`dirname "${this_dir}"`
log_dir="${parent_dir}/logs"
log_file="${log_dir}/catalina.out"
jar_file="${parent_dir}/webapps/${jar_name}"

#Interrupt execution when the number of parameters is <1 or the parameter is null
if [ $# -lt 1 ] || [ -z $1 ]; then
	echo -e "\033[31m Please enter the name of the jar package to be deployed! \033[0m"
	exit 1
fi

#The log folder does not exist, it is created
if [ ! -d "${log_dir}" ]; then
	mkdir "${log_dir}"
fi

#The jar file exists in the parent directory.
if [ -f "${jar_file}" ]; then
	#Start the jar package; redirect the standard error output to the file, and drop the standard output
	java $JAVA_OPTS -jar ${jar_file} 1>/dev/null 2>"${log_file}" &
	exit 0
else
	echo -e "\033[31m${jar_file}file does not exist!\033[0m"
	exit 1
fi
