#!/bin/bash
#details:Start the jar file in the upper directory
#Parameter introduction:
# $1:jar filename (with suffix)
# Note: The jar file must be located in the directory above the startup.sh directory.

#Startup parameter

# 主机的skywalking目录，如果脚本判断该目录不存在，则会以不带监控的方式启动服务
skywalking_home=/usr/local/skywalking7
# skywalking服务地址，本地或指定的主机
skywalking_server=10.133.85.71:11800

if [ $# == 1 ];then
 echo "INFO:Only 1 args,now starting service without skywalking."
 jar_name=$1
 JAVA_OPTS="-server -Xms512m -Xmx512m -Xmn300m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -Xverify:none -XX:+DisableExplicitGC -Djava.awt.headless=true "
else
 service_name=$1
 jar_name=$2

 agent_path=$skywalking_home/agents/agent_$service_name
 if [ ! -d $skywalking_home ];then
  echo "INFO:$skywalking_home not exists,if you need to monitor,please ensure $skywalking_home exists and check config,now starting service without skywalking."
  JAVA_OPTS="-server -Xms512m -Xmx512m -Xmn300m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -Xverify:none -XX:+DisableExplicitGC -Djava.awt.headless=true "
 else
  if [ ! -d $agent_path ];then
    mkdir -p $agent_path
    cp -r $skywalking_home/agent/* $agent_path/
    echo "INFO:$agent_path created complete."
    sed -i "s/SW_AGENT_NAME:.*\}/SW_AGENT_NAME:$service_name\}/g"  $agent_path/config/agent.config
    sed -i "s/SW_AGENT_COLLECTOR_BACKEND_SERVICES:.*\}/SW_AGENT_COLLECTOR_BACKEND_SERVICES:$skywalking_server\}/g"  $agent_path/config/agent.config
    echo "INFO:agent.config configured complete."
  else
    echo "INFO:$agent_path exists."
  fi
  JAVA_OPTS="-server -Xms512m -Xmx512m -Xmn300m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -Xverify:none -XX:+DisableExplicitGC -Djava.awt.headless=true   -javaagent:${agent_path}/skywalking-agent.jar"
 fi

fi


this_dir="$( cd "$( dirname "$0"  )" && pwd )"
parent_dir=`dirname "${this_dir}"`
log_dir="${parent_dir}/logs"
log_file="${log_dir}/catalina.out"
jar_file="${parent_dir}/webapps/${jar_name}"

#Interrupt execution when the number of parameters is <1 or the parameter is null
if [ $# -lt 1 ] || [ -z $1 ]; then
	echo -e "\033[31m Please enter the name of the jar package to be deployed! \033[0m"
	exit 1
fi

#The log folder does not exist, it is created
if [ ! -d "${log_dir}" ]; then
	mkdir "${log_dir}"
fi

#The jar file exists in the parent directory.
if [ -f "${jar_file}" ]; then
	#Start the jar package; redirect the standard error output to the file, and drop the standard output
	java $JAVA_OPTS -jar ${jar_file} 1>/dev/null 2>"${log_file}" &
        exit 0
else
	echo -e "\033[31m${jar_file}file does not exist!\033[0m"
	exit 1
fi
