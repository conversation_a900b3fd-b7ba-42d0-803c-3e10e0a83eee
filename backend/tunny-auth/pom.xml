<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>tunny</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>tunny-auth</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>

	<name>tunny-auth</name>
	<description>tunny 认证授权中心，基于 spring security oAuth2</description>

	<dependencies>

		<!--系统管理模块 api、model 模块-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-admin-client</artifactId>
			<version>1.3.2</version>
		</dependency>
		<!--spring security 、oauth、jwt依赖-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-security</artifactId>
			<exclusions>
				<!--旧版本 redis操作有问题-->
				<exclusion>
					<artifactId>spring-security-oauth2</artifactId>
					<groupId>org.springframework.security.oauth</groupId>
				</exclusion>
				<!--为安全考虑移除actuator监控-->
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-actuator</artifactId>
				</exclusion>
			</exclusions>

		</dependency>
		<dependency>
			<groupId>org.springframework.security.oauth</groupId>
			<artifactId>spring-security-oauth2</artifactId>
			<version>${security.oauth.version}</version>
		</dependency>
		<!--数据库-->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>2.3.1</version>
		</dependency>
		<!--web 模块-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<!--排除tomcat依赖-->
				<exclusion>
					<artifactId>spring-boot-starter-tomcat</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
			<version>2.0.8.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>2.9.9</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
		<finalName>${project.artifactId}-${maven.build.timestamp}</finalName>
	</build>
	<properties>
		<!--自定义默认的编码格式-->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!--自定义默认的时间格式 年-月-日-时-分-->
		<maven.build.timestamp.format>
			yyyy-MM-dd_HH_mm
		</maven.build.timestamp.format>
	</properties>

</project>
