package com.huazheng.tunny.auth.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Lenovo on 2018/9/14.
 */
@RestController
//配置如下注解，当Nacos的配置发生变更时，动态更新
@RefreshScope
public class InfoController {

    @Value("${spring.application.name}")
    private String applicationName;
    @RequestMapping("/info")
    public  String info(){
        return "Tunny标准开发脚手架，当前服务是："+applicationName;
    }

}
