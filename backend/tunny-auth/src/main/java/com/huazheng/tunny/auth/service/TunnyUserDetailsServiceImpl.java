package com.huazheng.tunny.auth.service;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.huazheng.tunny.admin.api.dto.UserInfo;
import com.huazheng.tunny.admin.api.entity.SysUser;
import com.huazheng.tunny.admin.api.feign.RemoteUserService;
import com.huazheng.tunny.common.core.constant.CommonConstant;
import com.huazheng.tunny.common.core.constant.SecurityConstants;
import com.huazheng.tunny.common.core.util.R;
import com.huazheng.tunny.common.core.util.WebUtils;
import com.huazheng.tunny.common.security.DTO.TunnyUser;
import com.huazheng.tunny.common.security.util.TunnyUserDetailsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * 用户详细信息
 *
 */
@Slf4j
@Service
@AllArgsConstructor
public class TunnyUserDetailsServiceImpl implements TunnyUserDetailsService {
	private final RemoteUserService remoteUserService;

	/**
	 * 用户密码登录
	 *
	 * @param username 用户名
	 * @return
	 * @throws UsernameNotFoundException
	 */
	@Override
	public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {


		R<UserInfo> result = remoteUserService.info(username, SecurityConstants.FROM_IN);

		return getUserDetails(result);
	}
	/**
	 * 根据手机号 登录
	 *
	 * @param mobile TYPE@CODE
	 * @return UserDetails
	 * @throws UsernameNotFoundException
	 */
	@Override
	public UserDetails loadUserByMobile(String mobile) throws UsernameNotFoundException {
		return getUserDetails(remoteUserService.mobileinfo(mobile, SecurityConstants.FROM_IN));
	}

	/**
	 * 根据社交登录code 登录
	 *
	 * @param inStr TYPE@CODE
	 * @return UserDetails
	 * @throws UsernameNotFoundException
	 */
	@Override
	public UserDetails loadUserBySocial(String inStr) throws UsernameNotFoundException {
		return getUserDetails(remoteUserService.social(inStr));
	}

	/**
	 * 构建userdetails
	 *
	 * @param result 用户信息
	 * @return
	 */
	private UserDetails getUserDetails(R<UserInfo> result) {
		//todo 处理用户禁用状态和密码过期策略
		if (result == null || result.getData() == null) {
			throw new UsernameNotFoundException("用户不存在");
		}else if ("0".equals(result.getData().getSysUser().getUserFlag())){
			throw new UserLockedException("user is already locked");
		}


		UserInfo info = result.getData();
		Set<String> dbAuthsSet = new HashSet<>();
		if (ArrayUtil.isNotEmpty(info.getRoles())) {
			// 获取角色
			Arrays.stream(info.getRoles()).forEach(role -> dbAuthsSet.add(SecurityConstants.ROLE + role));
			// 获取资源
			dbAuthsSet.addAll(Arrays.asList(info.getPermissions()));


		}
		SysUser user = info.getSysUser();
		//将用户ID填入 资源
		dbAuthsSet.add("userId:"+ user.getUserId());
		dbAuthsSet.add("userName:"+ user.getUsername());
		dbAuthsSet.add("userRealName:"+ user.getUserRealname());
		dbAuthsSet.add("userDeptId:"+ user.getDeptId());
		dbAuthsSet.add("userEmpNo:"+ user.getEmpno());
		//塞入权限信息
		dbAuthsSet.add("dsType:"+info.getDsType());
		//塞入部门List   ,这么搞不行，好像自动根据, 默认为多个授权项目了
		dbAuthsSet.add("dataScope:"+info.getDataScope());

		Collection<? extends GrantedAuthority> authorities
			= AuthorityUtils.createAuthorityList(dbAuthsSet.toArray(new String[0]));
		boolean enabled = StrUtil.equals(user.getDelFlag(), CommonConstant.STATUS_NORMAL);
		// 构造security用户
//		return new User(info.getSysUser().getUsername(), SecurityConstants.MD5+ user.getPassword().toLowerCase(), enabled,
//				true, true, true, authorities);

		return new TunnyUser(user.getUserId(), user.getDeptId(), user.getUsername(), SecurityConstants.MD5 + user.getPassword().toLowerCase(), enabled,
				true, true, true, authorities);
	}

	/**
	 * 用户锁定自定义异常
	 */
	private static class UserLockedException extends OAuth2Exception {

		public UserLockedException(String msg) {
			super(msg);
		}

		@Override
		public String getOAuth2ErrorCode() {
			return "user_locked";
		}

		@Override
		public int getHttpErrorCode() {
			return 415;  // Unsupported Media Type
		}
	}
}
