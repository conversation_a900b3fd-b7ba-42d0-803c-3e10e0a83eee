<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>tunny-auth</contextName>
    <!--输出文件路径-->
    <property name="log.path" value="../logs/tunny-auth.log"/>
    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!--logback默认过滤器，若开启则error以下的日志级别都不会输出-->
        <!-- <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
             <level>ERROR</level>
         </filter>-->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!--输出到文件-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}</file>
        <!--日志滚动策略，一天切割一次-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>../logs/logback.%d{yyyy-MM-dd}.log</fileNamePattern>

            <!--只保留最近15天的日志-->
            <maxHistory>15</maxHistory>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志输出编码格式-->
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>

    <!-- 测试环境+开发环境. 多个使用逗号隔开. -->
    <springProfile name="test,dev">
      <logger name="com.huazheng" level="debug" additivity="true" >
           <!--   <appender-ref ref="console"/>-->
      </logger>
    </springProfile>
    <!-- 生产环境可设置成info 或error -->
    <springProfile name="prod">
        <root level="info">
            <appender-ref ref="console"/>
            <appender-ref ref="file"/>
        </root>
        <!-- <logger name="com.huazheng" level="ERROR" additivity="false" >
             <appender-ref ref="file"/>
         </logger>-->
    </springProfile>

</configuration>