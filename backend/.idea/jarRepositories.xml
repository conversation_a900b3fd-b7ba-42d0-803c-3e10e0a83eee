<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="aliyun" />
      <option name="name" value="aliyun" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus" />
      <option name="name" value="tunny_group" />
      <option name="url" value="http://58.56.128.186:30037/repository/tunny_group/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-milestones" />
      <option name="name" value="Spring Milestones" />
      <option name="url" value="https://repo.spring.io/libs-milestone" />
    </remote-repository>
  </component>
</project>