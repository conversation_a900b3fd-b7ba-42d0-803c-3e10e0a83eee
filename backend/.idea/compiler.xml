<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="tunny-basic-server" />
        <module name="tunny-gateway" />
        <module name="tunny-admin-client" />
        <module name="tunny-bpm-server" />
        <module name="tunny-common-data" />
        <module name="tunny-common-tools" />
        <module name="tunny-common-mq" />
        <module name="tunny-admin-server" />
        <module name="tunny-common-core" />
        <module name="tunny-bpm-client" />
        <module name="tunny-api-server" />
        <module name="tunny-common-log" />
        <module name="tunny-common-api" />
        <module name="tunny-auth" />
        <module name="tunny-fastdfs-client" />
        <module name="tunny-report" />
        <module name="tunny-monitor" />
        <module name="tunny-common-swagger" />
        <module name="tunny-msgcenter" />
        <module name="tunny-common-security" />
        <module name="tunny-basic-client" />
        <module name="tunny-api-client" />
        <module name="tunny-fastdfs-server" />
        <module name="tunny-codegen" />
        <module name="tunny-common-starter" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="tunny-admin-client" options="-parameters" />
      <module name="tunny-admin-server" options="-parameters" />
      <module name="tunny-api-client" options="-parameters" />
      <module name="tunny-api-server" options="-parameters" />
      <module name="tunny-auth" options="-parameters" />
      <module name="tunny-basic-client" options="-parameters" />
      <module name="tunny-basic-server" options="-parameters" />
      <module name="tunny-bpm-client" options="-parameters" />
      <module name="tunny-bpm-server" options="-parameters" />
      <module name="tunny-codegen" options="-parameters" />
      <module name="tunny-common-api" options="-parameters" />
      <module name="tunny-common-core" options="-parameters" />
      <module name="tunny-common-data" options="-parameters" />
      <module name="tunny-common-log" options="-parameters" />
      <module name="tunny-common-mq" options="-parameters" />
      <module name="tunny-common-security" options="-parameters" />
      <module name="tunny-common-starter" options="-parameters" />
      <module name="tunny-common-swagger" options="-parameters" />
      <module name="tunny-common-tools" options="-parameters" />
      <module name="tunny-fastdfs-client" options="-parameters" />
      <module name="tunny-fastdfs-server" options="-parameters" />
      <module name="tunny-gateway" options="-parameters" />
      <module name="tunny-monitor" options="-parameters" />
      <module name="tunny-msgcenter" options="-parameters" />
      <module name="tunny-report" options="-parameters" />
    </option>
  </component>
</project>