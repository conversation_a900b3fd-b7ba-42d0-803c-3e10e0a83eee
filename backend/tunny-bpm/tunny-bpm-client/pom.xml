<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>tunny-bpm</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>tunny-bpm-client</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>
	<name>tunny-bpm-client</name>
	<description>tunny bpm公共api模块</description>


	<dependencies>
		<!--core 工具类-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-security</artifactId>
			<version>1.3.2</version>
		</dependency>

		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-data</artifactId>
			<version>1.3.2</version>
		</dependency>
		<!--feign接口依赖-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
    </dependencies>
</project>
