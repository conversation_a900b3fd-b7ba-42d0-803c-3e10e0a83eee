package com.huazheng.bpm.api.feign.fallback;

import com.huazheng.bpm.api.entity.SysPost;
import com.huazheng.bpm.api.dto.UserInfo;
import com.huazheng.bpm.api.entity.SysRole;
import com.huazheng.bpm.api.entity.SysUser;
import com.huazheng.bpm.api.feign.RemoteUserService;
import com.huazheng.bpm.api.vo.UserVO;
import com.huazheng.tunny.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class RemoteUserServiceFallbackImpl implements RemoteUserService {
	/**
	 * 通过用户名查询用户、角色信息
	 *
	 * @param username 用户名
	 * @param from     内外标志
	 * @return R
	 */
	@Override
	public R<UserInfo> info(String username, String from) {
		log.error("feign 查询用户信息失败:{}", username);
		return null;
	}

	/**
	 * 通过社交账号查询用户、角色信息
	 *
	 * @param inStr appid@code
	 * @return
	 */
	@Override
	public R<UserInfo> social(String inStr) {
		log.error("feign 查询用户信息失败:{}", inStr);
		return null;
	}

	@Override
	public List selectUserList(String searchUser) {
		return new ArrayList();
	}

	@Override
	public List<SysPost> postList() {
		return new ArrayList<>();
	}

	@Override
	public List<SysRole> roleListAll() {
		return new ArrayList<>();
	}

	@Override
	public List<SysUser> selectUserByRole(List<Integer> roles) {
		return new ArrayList<>();
	}
	@Override
	public List<SysUser> selectuserbyrolelistandprojectuuid(String role, String projectuuid) {
		return new ArrayList<>();
	}
	@Override
	public List<SysRole> selectrolebyempno(String empno) {
		return new ArrayList<>();
	}

	@Override
	public List<SysUser> selectUserByPost(String posts) {
		return new ArrayList<>();
	}

	@Override
	public UserVO user(Integer id) {
		return null;
	}

	@Override
	public SysUser getUserByCode(String code) {
		return null;
	}

	/**
	 * 查询当前登录用户信息
	 *
	 * @return R
	 */
	@Override
	public R<UserInfo> info() {
		log.error("feign 查询当前登录用户信息失败:{}");
		return null;
	}
}
