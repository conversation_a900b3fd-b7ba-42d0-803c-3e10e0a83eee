package com.huazheng.bpm.api.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(value = "tunny-bpm")
public interface RemoteBpmService {

    /**
     * 流程发起
     *
     * defKey	流程定义Key	String	必要
     * empNo	当前操作人工号	String	必要
     * empName	当前操作人姓名	String	必要
     * gateWay	跳转条件设置	（JSON字符串,格式{"条件名": "值","条件名": "值"}）	非必要
     * nodeUsers	后续节点办理人	（JSON字符串，格式{"节点ID": ["员工号","员工号"],"节点ID": ["员工号","员工号"]}）	非必要
     * formInfo	业务系统表单信息	（JSON字符串，格式
     *          {
     *              "formKey": "表单主键",
     *              "formType": "表单类型",
     *              "formName": "表单名称",
     *              "formSource": "表单系统来源",
     *              "projectuuid": "项目UUID"}
     *         ）	非必要
     */
    @PostMapping("/platform/startProcess")
    public Map<String, Object> startProcess(@RequestParam("defKey")String defKey,
                                            @RequestParam("empNo")String empNo,
                                            @RequestParam("empName")String empName,
                                            @RequestParam("nodeUsers")String nodeUsers,
                                            @RequestParam("gateWay")String gateWay,
                                            @RequestParam("formInfo")String formInfo);

    /**
     * 审批通过
     *
     * procId	流程ID	String	必要
     * opinion	审批意见	String	必要
     * taskId	任务ID	String	必要
     * empNo	当前操作人工号	String	必要
     * empName	当前操作人姓名	String	必要
     * gateWay	跳转条件设置	（JSON字符串,格式{"条件名": "值","条件名": "值"}）	非必要
     * nodeUser	后续节点办理人	（JSON字符串，格式{"节点ID": ["员工号","员工号"],"节点ID": ["员工号","员工号"]}）	非必要
     */
    @PostMapping("/platform/agree")
    public Map<String, Object> agree(@RequestParam Map<String, String> vapMaps);

    /**
     * 审批不通过
     *
     * procId	流程ID	String	必要
     * opinion	审批意见	String	必要
     * taskId	任务ID	String	必要
     * empNo	当前操作人工号	String	必要
     * empName	当前操作人姓名	String	必要
     * gateWay	跳转条件设置	（JSON字符串,格式{"条件名": "值","条件名": "值"}）	非必要
     * nodeUser	后续节点办理人	（JSON字符串，格式{"节点ID": ["员工号","员工号"],"节点ID": ["员工号","员工号"]}）	非必要
     */
    @PostMapping("/platform/oppose")
    public Map<String, Object> oppose(@RequestParam Map<String, String> vapMaps);

    /**
     * 审批驳回
     *
     * procId	流程ID	String	必要
     * destination	驳回节点ID	String	必要
     * backHandMode	是否重走流程	String	必要
     * opinion	审批意见	String	必要
     * taskId	任务ID	String	必要
     * empNo	当前操作人工号	String	必要
     * empName	当前操作人姓名	String	必要
     */
    @GetMapping("/platform/reject")
    public Map<String, Object> reject(@RequestParam Map<String, String> vapMaps);

    /**
     * 弃审
     *
     * procId	流程ID	String	必要
     * actionName	操作类型	String	必要(固定填revoke)
     * opinion	审批意见	String	必要
     * taskId	任务ID	String	必要
     * empNo	当前操作人工号	String	必要
     * empName	当前操作人姓名	String	必要
     */
    @GetMapping("/platform/complete")
    public Map<String, Object> complete(@RequestParam Map<String, String> vapMaps);

    /**
     * 查询可驳回节点
     *
     * taskId	任务ID	String	必要
     */
    @GetMapping("/platform/toReject")
    public List toReject(@RequestParam String taskId);

    /**
     * 待办任务
     *
     * empNo	当前操作人工号	String	必要
     * startTime	根据待办节点开始时间查询-开始时间	String	非必要
     * endTime	根据待办节点开始时间查询-结束时间	String	非必要
     * defKey	流程定义Key	String	非必要
     */
    @GetMapping("/platform/pendingJson")
    public List pendingJson(@RequestParam("empNo") String empNo,
                            @RequestParam("startTime") String startTime,
                            @RequestParam("endTime") String endTime,
                            @RequestParam("defKey") String defKey);

    /**
     * 流程相关数据，包含待办、已办理、已完结
     *
     * empNo	当前操作人工号	String	必要
     * flag	状态	String	非必要
     * approveType	审批结果	String	非必要
     * defKey	流程定义Key	String	非必要
     */
    @GetMapping("/platform/processList")
    public List processList(@RequestParam("empNo")String empNo,
                            @RequestParam("flag")String flag,
                            @RequestParam("approveType")String approveType,
                            @RequestParam("defKey")String defKey);

    /**
     * 审批历史
     *
     * procId	流程ID	String	必要
     * allApproval	是否查询全部历史(默认不查发起时跳过节点)	Boolean	非必要
     */
    @GetMapping("/platform/flowHistory")
    public List<Map<String, Object>> flowHistory(@RequestParam String procId);

    /**
     * 根据流程实例ID获取流程定义里所有任务节点及节点审批状态
     *
     * procId	流程实例ID	String	必要
     */
    @GetMapping("/platform/fullNodeInfo")
    public String fullNodeInfo(@RequestParam String procId);

    /**
     * 流程任务转办
     */
    @GetMapping("/platform/transfer")
    public Map<String, Object> transfer(@RequestParam("taskId")String taskId,
                                        @RequestParam("taskName")String taskName,
                                        @RequestParam("procId")String procId,
                                        @RequestParam("subject")String subject,
                                        @RequestParam("empNo")String empNo,
                                        @RequestParam("tranUsers")String tranUsers);

    /**
     * 获取流程图节点状态信息
     */
    @GetMapping("/platform/flowProcess")
    List<Map<String,Object>> flowProcess(@RequestParam String procId);

    /**
     * 流程挂起
     */
    @GetMapping("/platform/suspendProcess")
    public void suspendProcess(@RequestParam String taskId);

    /**
     * 流程恢复
     */
    @GetMapping("/platform/recoverProcess")
    public void recoverProcess(@RequestParam String taskId);

    /**
     * 流程实例-批量挂起
     */
    @GetMapping("/platform/batchSuspendProcess")
    public void batchSuspendProcess(@RequestParam String taskIds);

    /**
     * 流程实例-批量恢复
     */
    @GetMapping("/platform/batchRecoverProcess")
    public void batchRecoverProcess(@RequestParam String taskIds);

    /**
     * 获取流程任务下一级审批节点信息
     */
    @GetMapping("/platform/getNextTaskNode")
    public String getNextTaskNode(@RequestParam("procId")String procId, @RequestParam("gateWay")String gateWay);

    /**
     * 获取流程图第一个任务节点
     */
    @GetMapping("/platform/getFirstNodeInfo")
    public String getFirstNodeInfo(@RequestParam("defKey")String defKey, @RequestParam("gateWay")String gateWay);

    /**
     * 强制终止流程
     *
     * @return*/
    @GetMapping("/platform/doEndProcess")
    public Map<String, Object> doEndProcess(@RequestParam("taskId") String taskId, @RequestParam("empNo") String empNo, @RequestParam("endReason") String endReason);
}
