package com.huazheng.bpm.api.feign;

import com.huazheng.bpm.api.entity.SysPost;
import com.huazheng.bpm.api.dto.UserInfo;
import com.huazheng.bpm.api.entity.SysRole;
import com.huazheng.bpm.api.entity.SysUser;
import com.huazheng.bpm.api.vo.UserVO;
import com.huazheng.tunny.common.core.constant.ServiceNameConstant;
import com.huazheng.tunny.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = ServiceNameConstant.UMPS_SERVICE)
//@FeignClient(value = "tunny-upms")
//@FeignClient(value = "tunny-admin")
//@FeignClient(value = "tunny-upms", url = "http://10.133.85.73:4005/admin")
public interface RemoteUserService {
    /**
     * 通过用户名查询用户、角色信息
     *
     * @param username 用户名
     * @param from     调用标志
     * @return R
     */
    @GetMapping("/user/info/{username}")
    R<UserInfo> info(@PathVariable("username") String username
            , @RequestHeader("from") String from);


    /**
     * 通过社交账号查询用户、角色信息
     *
     * @param inStr appid@code
     * @return
     */
    @GetMapping("/social/info/{inStr}")
    R<UserInfo> social(@PathVariable("inStr") String inStr);

    /**
     * 获取用户列表
     *
     * @param
     * @return
     */
    @PostMapping("/user/selectUserList")
    List<SysUser> selectUserList(@RequestParam("searchUser") String searchUser);

    /**
     * 获取岗位列表
     *
     * @param
     * @return
     */
    @GetMapping("/syspost/postList")
    List<SysPost> postList();

    /**
     * 获取角色列表
     *
     * @param
     * @return
     */
    @GetMapping("/role/roleListAll")
    List<SysRole> roleListAll();

    /**
     * 根据角色获取用户列表
     *
     * @param
     * @return
     */
    @PostMapping("/role/selectUserByRole")
    List<SysUser> selectUserByRole(@RequestParam("roles") List<Integer> roles);

    /**
     * 根据角色获取用户列表（允许按项目UUID进行过滤）
     *
     * @param role 例："1,4,5"
     * @param projectuuid 例："C3734540-6868-4469-B420-583EC86AFF8C"
     * @return
     */
    @GetMapping("/outInterface/selectuserbyrolelistandprojectuuid")
    List<SysUser> selectuserbyrolelistandprojectuuid(@RequestParam("role") String role, @RequestParam("projectuuid") String projectuuid);

    /**
     * 根据用户角色获取角色列表
     *
     * @param empno
     * @return
     */
    @GetMapping("/outInterface/selectrolebyempno")
    List<SysRole> selectrolebyempno(@RequestParam("empno") String empno);
    /**
     * 根据岗位获取用户列表
     *
     * @param
     * @return
     */
    @PostMapping("/syspost/postList")
    List<SysUser> selectUserByPost(@RequestParam("posts") String posts);
    /**
     * 根据用户ID获取用户
     *
     * @param
     * @return
     */
    @GetMapping("/user")
    UserVO user(@RequestParam("id") Integer id);
    /**
     * 根据用户ID获取用户
     *
     * @param
     * @return
     */
    @GetMapping("/user/getUserByCode")
    SysUser getUserByCode(@RequestParam("code") String code);

    /**
     * 查询当前登录用户信息
     *
     * @return R
     */
    @GetMapping("/user/info")
    R<UserInfo> info();
}
