package com.huazheng.bpm.api.feign.fallback;

import com.huazheng.bpm.api.feign.RemoteBpmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class RemoteBpmServiceFallbackImpl implements RemoteBpmService {

	@Override
	public List<Map<String, Object>> flowHistory(String procId) {
		return new ArrayList<>();
	}

	@Override
	public Map<String, Object> startProcess(String defKey, String empNo, String empName, String nodeUsers, String gateWay, String formInfo) {
		return new HashMap<>();
	}

	@Override
	public Map<String, Object> agree(Map<String, String> vapMaps) {
		return new HashMap<>();
	}

	@Override
	public Map<String, Object> oppose(Map<String, String> vapMaps) {
		return new HashMap<>();
	}

	@Override
	public Map<String, Object> reject(Map<String, String> vapMaps) {
		return new HashMap<>();
	}

	@Override
	public Map<String, Object> complete(Map<String, String> vapMaps) {
		return new HashMap<>();
	}

	@Override
	public List toReject(String taskId) {
		return new ArrayList();
	}

	@Override
	public List pendingJson(String empNo, String startTime, String endTime, String defKey) {
		return new ArrayList();
	}

	@Override
	public List processList(String empNo, String flag, String approveType, String defKey) {
		return new ArrayList();
	}

	@Override
	public Map<String, Object> transfer(String taskId, String taskName, String procId, String subject, String empNo, String tranUsers) {
		return new HashMap<>();
	}

	@Override
	public List<Map<String, Object>> flowProcess(String procId) {
		return new ArrayList<>();
	}

	@Override
	public void suspendProcess(String taskId) {
		//
	}

	@Override
	public void recoverProcess(String taskId) {
		//
	}

	@Override
	public void batchSuspendProcess(String taskIds) {
		//
	}

	@Override
	public void batchRecoverProcess(String taskIds) {
		//
	}

	@Override
	public String getNextTaskNode(String procId, String gateWay) {
		return "";
	}

	@Override
	public String getFirstNodeInfo(String defKey, String gateWay) {
		return "";
	}

	@Override
	public Map<String, Object> doEndProcess(String taskId, String empNo, String endReason) {
		return null;
	}

	@Override
	public String fullNodeInfo(String procId) {
		return "";
	}
}
