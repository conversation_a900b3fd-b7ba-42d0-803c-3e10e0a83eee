package com.huazheng.bpm.controller;

import com.huazheng.bpm.service.DiagramService;
import com.huazheng.bpm.util.web.RequestUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;


/**
 * 流程定义 控制类
 *
 * <pre>
 *
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：simon cai
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-08-30 9:37:25
 * </pre>
 */
@Controller
@RequestMapping("/platform/bpmn/image/")
public class BpmImageController extends GenericController {

	@Resource
	private DiagramService diagramService;

	/**
	 * 生成流程图
	 *
	 * @param request
	 * @param reponse
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("gen")
	public void gen(HttpServletRequest request, HttpServletResponse reponse) throws Exception {

		String defId = RequestUtil.getString(request, "defId","");
		String bpmnInstId =  RequestUtil.getString(request, "bpmnInstId","");
		String taskId =  RequestUtil.getString(request, "taskId","") ;

		InputStream is = diagramService.genImage(defId,bpmnInstId,taskId);

		this.reInputStream(is, reponse, "image/png");
	}

}
