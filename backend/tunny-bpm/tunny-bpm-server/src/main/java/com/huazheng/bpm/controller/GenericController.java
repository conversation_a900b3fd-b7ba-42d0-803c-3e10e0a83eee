package com.huazheng.bpm.controller;

import com.huazheng.bpm.domain.mybatis.DefaultFieldSort;
import com.huazheng.bpm.domain.mybatis.DefaultPage;
import com.huazheng.bpm.util.base.Direction;
import com.huazheng.bpm.util.base.FieldLogic;
import com.huazheng.bpm.util.base.FieldSort;
import com.huazheng.bpm.util.base.QueryFilter;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.db.DefaultQueryFilter;
import com.huazheng.bpm.util.page.Page;
import com.huazheng.bpm.util.string.StringUtil;
import com.huazheng.bpm.util.web.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.context.MessageSource;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.support.ByteArrayMultipartFileEditor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;


/**
 * 通用控制器。
 *
 * <pre>
 * 构建组：ibps-base-web
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2015-10-12-下午10:21:40
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class GenericController {

	protected Logger logger = LoggerFactory.getLogger(this.getClass());
	/**
	 * 返回成功的JSON字符串
	 */
	public final String SUCCESS = "{success:true}";
	/**
	 * 返回失败字符串
	 */
	public final String FAILURE = "{success:false}";

	/**
	 * 保存操作的信息
	 */
	private MessageSourceAccessor messages;

	public static final String MESSAGES_KEY = "successMessages";

	public static final String ERRORS = "errors";

	/**
	 * 通过Request的URL对应转成对应的JSP文件展示
	 *
	 * @return
	 * @throws Exception
	 */
	public ModelAndView getAutoView() throws Exception {
		ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = requestAttributes.getRequest();
		// 处理RequestURI
		logger.debug("request URI:" + request.getRequestURI());
		String jspPath = RequestViewUtil.getViewUrl(request);
		logger.debug("request Url JSP:" + jspPath);
		return new ModelAndView(jspPath);

	}

	/**
	 * 通过Request的URL对应转成对应的JSP文件展示
	 *
	 * @return
	 * @throws Exception
	 */
	public ModelAndView getAutoView(String view) throws Exception {
		if(StringUtil.isEmpty(view))
			return getAutoView();
		return new ModelAndView(view);
	}

	@Autowired
	public void setMessages(MessageSource messageSource) {
		messages = new MessageSourceAccessor(messageSource);
	}

	private String resultMessageToString(ResultMessage resultMessage) {
		if (resultMessage == null) {
			resultMessage = new ResultMessage(ResultMessage.ERROR, "未设置返回参数");
		}
		return resultMessage.toString();
	}

	/**
	 * 返回出错或成功的信息。
	 *
	 * @param writer
	 * @param resultMsg
	 * @param successFail
	 */
	protected void writeResultMessage(PrintWriter writer, String resultMsg, int successFail) {
		ResultMessage resultMessage = new ResultMessage(successFail, resultMsg);
		writeResultMessage(writer, resultMessage);
	}

	/**
	 * 返回出错或成功的信息。
	 *
	 * @param writer
	 * @param resultMsg
	 * @param successFail
	 */
	protected void writeResultMessage(PrintWriter writer, String resultMsg, String cause, int successFail) {
		ResultMessage resultMessage = new ResultMessage(successFail, resultMsg, cause);
		writeResultMessage(writer, resultMessage);
	}

	/**
	 * 返回出错或成功的信息。
	 *
	 * @param writer
	 * @param resultMessage
	 */
	protected void writeResultMessage(PrintWriter writer, ResultMessage resultMessage) {
		writer.print(resultMessageToString(resultMessage));
	}

	/**
	 * 保存错误消息（errors）
	 *
	 * @param request
	 * @param error
	 */
	public void saveError(HttpServletRequest request, String msg) {
		saveMessage(request, ERRORS, msg);
	}

	/**
	 * 保存消息（successMessages）
	 *
	 * @param request
	 * @param msg
	 */
	public void saveMessage(HttpServletRequest request, String msg) {
		saveMessage(request, MESSAGES_KEY, msg);
	}

	/**
	 *
	 * @param request
	 * @param key
	 * @param msg
	 */
	@SuppressWarnings("unchecked")
	public void saveMessage(HttpServletRequest request, String key, String msg) {
		List<Object> messages = (List<Object>) request.getSession().getAttribute(key);
		if (messages == null) {
			messages = new ArrayList<Object>();
		}
		messages.add(msg);
		request.getSession().setAttribute(key, messages);
	}

	/**
	 * 根据key和local取得键值。
	 *
	 * @param msgKey
	 * @param locale
	 * @return
	 */
	public String getText(String msgKey, Locale locale) {
		return messages.getMessage(msgKey, locale);
	}

	/**
	 * 根据键值，参数，local取得键值。
	 *
	 * @param msgKey
	 * @param arg
	 * @param locale
	 * @return
	 */
	public String getText(String msgKey, String arg, Locale locale) {
		return getText(msgKey, new Object[] { arg }, locale);
	}

	/**
	 * 根据键值，参数数组，local取得键值。
	 *
	 * @param msgKey
	 * @param args
	 * @param locale
	 * @return
	 */
	public String getText(String msgKey, Object[] args, Locale locale) {
		return messages.getMessage(msgKey, args, locale);
	}

	/**
	 * 根据键值参数取得键值。
	 *
	 * @param msgKey
	 * @param args
	 * @return
	 */
	public String getText(String msgKey, Object... args) {
		return messages.getMessage(msgKey, args, ContextUtil.getLocale());
	}

	/**
	 * 根据键值取得键值。
	 *
	 * @param msgKey
	 * @return
	 */
	public String getText(String msgKey) {
		return messages.getMessage(msgKey, ContextUtil.getLocale());
	}

	/**
	 * 取得资源键值
	 *
	 * @param msgKey
	 * @param arg
	 * @param request
	 * @return
	 */
	protected String getText(String msgKey, String arg, HttpServletRequest request) {
		Locale locale = ContextUtil.getLocale();
		return getText(msgKey, arg, locale);
	}

	/**
	 * 取得资源键值
	 *
	 * @param msgKey
	 * @param args
	 * @param request
	 * @return
	 */
	protected String getText(String msgKey, Object[] args, HttpServletRequest request) {
		Locale locale = ContextUtil.getLocale();
		return getText(msgKey, args, locale);
	}

	/**
	 * 返回构建的QueryFilter
	 *
	 * @param request
	 *            页面的请求
	 * @param page
	 *            分页数据
	 * @return QueryFilter
	 */
	protected QueryFilter getQuerFilter(HttpServletRequest request, Page page) {
		DefaultQueryFilter queryFilter = new DefaultQueryFilter();
		try {
			String defaultWhere = request.getParameter("defaultWhere");
			if (StringUtil.isNotEmpty(defaultWhere))
				queryFilter.addParamsFilter("defaultWhere", defaultWhere);

			// 设置查询字段
			FieldLogic queryFieldLogic = QueryUtil.getFieldLogic(request);
			queryFilter.setFieldLogic(queryFieldLogic);
			// 设置分页
			if (BeanUtils.isNotEmpty(page))
				queryFilter.setPage(page);
			else
				queryFilter.setPage(null);

			// 设置排序字段
			queryFilter.setFieldSortList(getQueryFilterSort(request));

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return queryFilter;
	}

	/**
	 * 返回构建的QueryFilter,带or查询
	 *
	 * @param request
	 *            页面的请求
	 * @param page
	 *            分页数据
	 * @return QueryFilter
	 */
	protected QueryFilter getQuerFilterByOr(HttpServletRequest request, Page page) {
		DefaultQueryFilter queryFilter = new DefaultQueryFilter();
		try {
			String defaultWhere = request.getParameter("defaultWhere");
			if (StringUtil.isNotEmpty(defaultWhere))
				queryFilter.addParamsFilter("defaultWhere", defaultWhere);

			// 设置查询字段
			FieldLogic queryFieldLogic = QueryUtil.getFieldLogicByOr(request);
			queryFilter.setFieldLogic(queryFieldLogic);
			// 设置分页
			if (BeanUtils.isNotEmpty(page))
				queryFilter.setPage(page);

			// 设置排序字段
			queryFilter.setFieldSortList(getQueryFilterSort(request));

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}

		return queryFilter;
	}

	/**
	 * 返回构建的QueryFilter(and查询)
	 *
	 * @param request
	 *            页面的请求
	 * @return QueryFilter
	 * @exception @since
	 *                1.0.0
	 */
	protected QueryFilter getQuerFilter(HttpServletRequest request) {
		return getQuerFilter(request, getQueryFilterPage(request));
	}

	/**
	 * 返回构建的QueryFilter(or查询)
	 *
	 * @param request
	 *            页面的请求
	 * @return QueryFilter
	 */
	protected QueryFilter getQuerFilterByOr(HttpServletRequest request) {
		return getQuerFilterByOr(request, getQueryFilterPage(request));
	}

	/**
	 * 排序的字段
	 *
	 * @param request
	 * @return
	 */
	private List<FieldSort> getQueryFilterSort(HttpServletRequest request) {
		String sort = RequestUtil.getString(request, "sidx");
		String order = RequestUtil.getString(request, "sord");
		if (StringUtil.isNotEmpty(sort) && StringUtil.isNotEmpty(order)) {
			List<FieldSort> fieldSorts = new ArrayList<FieldSort>();
			fieldSorts.add(new DefaultFieldSort(sort, Direction.fromString(order)));
			return fieldSorts;
		}
		return null;
	}

	/**
	 * 查询的字段
	 *
	 * @param request
	 * @return
	 */
	public Page getQueryFilterPage(HttpServletRequest request) {
		Integer pageSize = RequestUtil.getInt(request, "page", 0);
		// 页大小
		Integer rows = RequestUtil.getInt(request, "rows", 0);
		DefaultPage page = new DefaultPage();
		if (pageSize > 0 && rows > 0) {
			page.setPage(pageSize);
			page.setLimit(rows);
			return page;
		}
		return page;
	}

	/**
	 * Set up a custom property editor for converting form inputs to real
	 * objects
	 *
	 * @param request
	 *            the current request
	 * @param binder
	 *            the data binder
	 */
	@InitBinder
	protected void initBinder(HttpServletRequest request, ServletRequestDataBinder binder) {
		// logger.debug("init binder ....");
		binder.registerCustomEditor(Integer.class, null, new CustomNumberEditor(Integer.class, null, true));
		binder.registerCustomEditor(Long.class, null, new CustomNumberEditor(Long.class, null, true));
		binder.registerCustomEditor(byte[].class, new ByteArrayMultipartFileEditor());
		binder.registerCustomEditor(Date.class, null, new CustomDateEditor(true));
		binder.registerCustomEditor(Timestamp.class, null, new CustomDateEditor(true));
	}

	/**
	 * 返回字节流
	 * @param is
	 * @param reponse
	 */
	public void reInputStream(InputStream is, HttpServletResponse reponse, String type) throws Exception {
		if (is == null)
			return;
		reponse.setContentType(type);
		OutputStream out = null;
		try {
			out = reponse.getOutputStream();
			byte[] bs = new byte[1024];
			int n = 0;
			while ((n = is.read(bs)) != -1) {
				out.write(bs, 0, n);
			}
			out.flush();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			if (is != null)
				is.close();
			if (out != null)
				out.close();
		}
	}

}
