package com.huazheng.bpm.activiti.def.graph.draw.util;

/**
 * 函数的帮助类。
 *
 * <pre>
 * 构建组：ibps-bpmn-activiti
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月29日-下午3:12:13
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class MathUtil {

	/**
	 * 替换Math 的四舍五入
	 *
	 * @param v
	 * @return
	 */
	public static int round(double v) {
		return (int) Math.round(v);
	}

	/**
	 * 替换Math 的四舍五入
	 *
	 * @param v
	 * @return
	 */
	public static int round(float v) {
		return Math.round(v);
	}
}
