package com.huazheng.bpm.activiti.def.graph.draw.entity;

/**
 * 流程图形坐标点。
 *
 * <pre>
 * 构建组：ibps-bpmn-activiti
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月29日-下午3:16:01
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class Point {
	private float x=0;
	private float y=0;

	public Point(float x,float y)
	{
		this.x=x;
		this.y=y;
	}

	public float getX() {
		return x;
	}
	/**
	 * x坐标。
	 * @param x
	 */
	public void setX(float x) {
		this.x = x;
	}
	public float getY() {
		return y;
	}
	/**
	 * y坐标。
	 * @param y
	 */
	public void setY(float y) {
		this.y = y;
	}
}
