package com.huazheng.bpm.activiti.def.impl;


import com.huazheng.bpm.activiti.def.BpmDefUtil;
import com.huazheng.bpm.service.DefTransform;

/**
 *
 * <pre>
 * 构建组：ibps-bpmn-act
 * 作者：simon cai
 * 邮箱：<EMAIL>
 * 日期：2016年12月8日-下午4:12:09
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class WebDefTransform implements DefTransform {

	@Override
	public String convert(String id, String name, String designXml) {

		return BpmDefUtil.transBpmDef(id, name, designXml);
	}

}
