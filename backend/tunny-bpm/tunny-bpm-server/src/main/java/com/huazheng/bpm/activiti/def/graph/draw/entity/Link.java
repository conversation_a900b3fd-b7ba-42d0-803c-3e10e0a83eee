package com.huazheng.bpm.activiti.def.graph.draw.entity;

import java.util.List;

/**
 * 程序设计器 连接 实体类，表示两个节点之间的连接。
 *
 * <pre>
 * 构建组：ibps-bpmn-activiti
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月29日-下午3:15:25
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class Link {

	//	 连接类型
	//	 "straight". Defines a straight link made of one line segment
	//	 "free". Defines a polyline line. The points defining the polyline can be specified through the intermediatePoints property.
	//	 "orthogonal". Defines a link that automatically computes its shape made of orthogonal segments.
	//	 "oblique". Defines a link that automatically computes its shape with straight segments at start and end of the link and an oblique segment in the middle
	private ShapeType shapeType;
	//开始节点
	private Shape startNode;
	//结束节点
	private Shape endNode;
	//开始端口
	private Port startPort;
	//结束端口
	private Port endPort;

	private Point fallbackStartPoint;

	private Point fallbackEndPoint;
	//中间点
	private List<Point> intermediatePoints;

	public ShapeType getShapeType() {
		return shapeType;
	}
	public void setShapeType(ShapeType shapeType) {
		this.shapeType = shapeType;
	}
	public Shape getStartNode() {
		return startNode;
	}
	public void setStartNode(Shape startNode) {
		this.startNode = startNode;
	}
	public Shape getEndNode() {
		return endNode;
	}
	public void setEndNode(Shape endNode) {
		this.endNode = endNode;
	}
	public Port getStartPort() {
		return startPort;
	}
	public void setStartPort(Port startPort) {
		this.startPort = startPort;
	}
	public Port getEndPort() {
		return endPort;
	}
	public void setEndPort(Port endPort) {
		this.endPort = endPort;
	}
	public Point getFallbackStartPoint() {
		return fallbackStartPoint;
	}
	public void setFallbackStartPoint(Point fallbackStartPoint) {
		this.fallbackStartPoint = fallbackStartPoint;
	}
	public Point getFallbackEndPoint() {
		return fallbackEndPoint;
	}
	public void setFallbackEndPoint(Point fallbackEndPoint) {
		this.fallbackEndPoint = fallbackEndPoint;
	}
	public List<Point> getIntermediatePoints() {
		return intermediatePoints;
	}
	public void setIntermediatePoints(List<Point> intermediatePoints) {
		this.intermediatePoints = intermediatePoints;
	}
}
