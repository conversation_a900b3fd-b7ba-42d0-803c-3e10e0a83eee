package com.huazheng.bpm.controller;

import com.alibaba.fastjson.JSON;
import com.huazheng.bpm.dao.bpm.BpmTaskAssignDao;
import com.huazheng.bpm.dao.bpm.BpmTaskAssignQueryDao;
import com.huazheng.bpm.dao.bpm.BpmTaskDao;
import com.huazheng.bpm.dao.bpm.BpmTaskQueryDao;
import com.huazheng.bpm.entity.bpm.BpmTaskAssignPo;
import com.huazheng.bpm.entity.bpm.BpmTaskPo;
import com.huazheng.bpm.service.BpmTaskAssignService;
import com.huazheng.bpm.service.BpmTaskService;
import com.huazheng.bpm.util.base.UUIDUtil;
import com.huazheng.bpm.util.web.RequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.*;

@Component
//@ConfigurationProperties(prefix = "demo")
//@PropertySource(value = "classpath:application.properties")
@Controller
@RequestMapping("/platform/bpmn/instance/bpmTaskAssign/")
public class BpmTaskAssignController {
    @Resource
    private BpmTaskQueryDao bpmTaskQueryDao;
    @Resource
    private BpmTaskDao bpmTaskDao;
    @Resource
    private BpmTaskAssignDao bpmTaskAssignDao;
    @Resource
    private BpmTaskAssignQueryDao bpmTaskAssignQueryDao;

//    @Autowired
    private BpmTaskAssignService bpmTaskAssignService;
//    @Value("${demo.cron}")
//    private String cron;
   //@Scheduled(cron = "")
    @Scheduled(cron = "0/1 * * * * ?")
    public void sendSmsTask() {
//        System.out.println(cron);
        //bpmTaskAssignService.sendSmsTask();
    }


    /**
     * 强制批量变更在途流程当前审批人
     * */
    @RequestMapping("changeAssignPage")
    @ResponseBody
    public String changeAssignPage(HttpServletRequest request, HttpServletResponse response) {
        try {
            String procIds = RequestUtil.getString(request, "procIds");
            String nodeUsers = RequestUtil.getString(request, "nodeUsers");
            List<String> procIdList = Arrays.asList(procIds.split(","));
            List<String> nodeUserList = Arrays.asList(nodeUsers.split(","));

            List<BpmTaskPo> bpmTaskPos = bpmTaskQueryDao.findByInst(procIdList);
            if(procIdList.size()==0){
                return new Exception("无流程号！").toString();
            }
            if(procIdList.size()!=bpmTaskPos.size()){
                return new Exception("存在多条待审任务流程，请检查是否提交了当前待审为会签节点的流程！").toString();
            }
            List<BpmTaskAssignPo> bpmTaskAssignPos = new ArrayList<>();
            for(BpmTaskPo bpmTaskPo : bpmTaskPos){
                for(String nodeUser : nodeUserList){
                    BpmTaskAssignPo bpmTaskAssignPo = new BpmTaskAssignPo();
                    bpmTaskAssignPo.setId(
                            String.valueOf((1 + new Random().nextDouble()) * Math.pow(10, 6)).substring(1, 7)
                            + String.valueOf((1 + new Random().nextDouble()) * Math.pow(10, 6)).substring(1, 7)
                            + String.valueOf((1 + new Random().nextDouble()) * Math.pow(10, 6)).substring(1, 7)
                    );
                    bpmTaskAssignPo.setTaskId(bpmTaskPo.getTaskId());
                    bpmTaskAssignPo.setType("employee");
                    bpmTaskAssignPo.setExecutor(nodeUser);
                    bpmTaskAssignPo.setProcInstId(bpmTaskPo.getProcInstId());
                    bpmTaskAssignPos.add(bpmTaskAssignPo);
                }
            }
            List<Map<String, String>> bpmTaskPos_new = new ArrayList<>();
            for(String nodeUser : nodeUserList) {
                Map<String, String> qualfieds = new HashMap<>();
                qualfieds.put("id", nodeUser);
                qualfieds.put("type", "employee");
                bpmTaskPos_new.add(qualfieds);
            }
            bpmTaskDao.updateBatchForQualfieds(procIdList, JSON.toJSONString(bpmTaskPos_new));
            //清理原审批人
            bpmTaskAssignDao.delByInst(procIdList);
            bpmTaskAssignDao.createBatch(bpmTaskAssignPos);
            return "1";
        }catch (Exception e){
            return e.toString();
        }

    }
}
