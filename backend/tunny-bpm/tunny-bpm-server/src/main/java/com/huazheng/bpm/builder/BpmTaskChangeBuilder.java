package com.huazheng.bpm.builder;

import com.huazheng.bpm.entity.bpm.BpmTaskChangePo;
import com.huazheng.bpm.entity.bpm.BpmTaskPo;
import com.huazheng.bpm.model.base.User;
import com.huazheng.bpm.service.IPartyEntityService;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.JacksonUtil;
import com.huazheng.bpm.util.web.ContextUtil;

import java.util.Date;
import java.util.List;


/**
 * 流程任务变更候选人对象构建工具
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年3月31日-上午9:14:55
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmTaskChangeBuilder {

	/**
	 * 构建流程任务变更候选人对象展示属性
	 *
	 * @param bpmAgentList
	 */
	public static void build(List<BpmTaskChangePo> bpmTaskChangeList){
		if(BeanUtils.isEmpty(bpmTaskChangeList)){
			return;
		}

		for(BpmTaskChangePo po : bpmTaskChangeList){
			build(po);
		}
	}

	/**
	 * 构建流程任务变更候选人对象展示属性
	 *
	 * @param bpmAgent
	 */
	public static void build(BpmTaskChangePo bpmTaskChange){
		if(BeanUtils.isEmpty(bpmTaskChange)){
			return;
		}

		IPartyEntityService entityService = AppUtil.getBean(IPartyEntityService.class);
		String entity = null;

		entity = entityService.getByIdJson(bpmTaskChange.getOwnerId());
		if(JacksonUtil.isJsonObject(entity)){
			bpmTaskChange.setOwnerName(JacksonUtil.getString(entity, "name"));
		}

		if(BeanUtils.isNotEmpty(bpmTaskChange.getExecutorId())){
			entity = entityService.getByIdJson(bpmTaskChange.getExecutorId());
			if(JacksonUtil.isJsonObject(entity)){
				bpmTaskChange.setExecutorName(JacksonUtil.getString(entity, "name"));
			}
		}
	}

	/**
	 * 构建流程任务变更候选人对象展示属性
	 *
	 * @param bpmAgent
	 */
	public static BpmTaskChangePo build(BpmTaskPo bpmTask, String changeType, String comment){
		if(BeanUtils.isEmpty(bpmTask)){
			return null;
		}

		BpmTaskChangePo bpmTaskChange = new BpmTaskChangePo();

		User cuser = ContextUtil.getCurrentUser();
		bpmTaskChange.setOwnerId(cuser.getUserId());
		bpmTaskChange.setOwnerName(cuser.getFullname());
		bpmTaskChange.setChangeType(changeType);
		bpmTaskChange.setStatus(BpmTaskChangePo.CHANGE_STATUS_RUNNING);
		bpmTaskChange.setTaskId(bpmTask.getId());
		bpmTaskChange.setTaskName(bpmTask.getName());
		bpmTaskChange.setTaskSubject(bpmTask.getSubject());
		bpmTaskChange.setNodeId(bpmTask.getNodeId());
		bpmTaskChange.setProcInstId(bpmTask.getProcInstId());
		bpmTaskChange.setCreateTime(new Date());
		bpmTaskChange.setComment(comment);

		return bpmTaskChange;
	}
}
