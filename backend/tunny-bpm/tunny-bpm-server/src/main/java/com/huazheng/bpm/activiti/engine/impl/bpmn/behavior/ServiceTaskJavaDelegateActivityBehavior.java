/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.huazheng.bpm.activiti.engine.impl.bpmn.behavior;

import com.huazheng.bpm.activiti.engine.delegate.DelegateExecution;
import com.huazheng.bpm.activiti.engine.delegate.ExecutionListener;
import com.huazheng.bpm.activiti.engine.delegate.JavaDelegate;
import com.huazheng.bpm.activiti.engine.impl.context.Context;
import com.huazheng.bpm.activiti.engine.impl.delegate.JavaDelegateInvocation;
import com.huazheng.bpm.activiti.engine.impl.pvm.delegate.ActivityBehavior;
import com.huazheng.bpm.activiti.engine.impl.pvm.delegate.ActivityExecution;


/**
 * <AUTHOR>
 */
public class ServiceTaskJavaDelegateActivityBehavior extends TaskActivityBehavior implements ActivityBehavior, ExecutionListener {

  protected JavaDelegate javaDelegate;

  protected ServiceTaskJavaDelegateActivityBehavior() {
  }

  public ServiceTaskJavaDelegateActivityBehavior(JavaDelegate javaDelegate) {
    this.javaDelegate = javaDelegate;
  }

  public void execute(ActivityExecution execution) throws Exception {
    execute((DelegateExecution) execution);
    leave(execution);
  }

  public void notify(DelegateExecution execution) throws Exception {
    execute(execution);
  }

  public void execute(DelegateExecution execution) throws Exception {
    Context.getProcessEngineConfiguration()
      .getDelegateInterceptor()
      .handleInvocation(new JavaDelegateInvocation(javaDelegate, execution));
  }
}
