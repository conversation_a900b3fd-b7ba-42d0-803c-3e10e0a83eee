package com.huazheng.bpm.builder;


import com.huazheng.bpm.domain.right.RightsType;
import com.huazheng.bpm.entity.rights.RightsContext;
import com.huazheng.bpm.service.RightsStrategy;
import com.huazheng.bpm.service.RightsStrategyFactory;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.string.StringUtil;

import java.util.List;

public class RightsSqlBuilder {
	private static final String UNION = " union ";

	public static String buildSqls(List<RightsType> rightTypes, RightsContext rightsContext){
		StringBuilder sb = new StringBuilder();
		int i = 0;
		String _sql = "";
		for(RightsType rightType:rightTypes){
			RightsStrategy rightStrategy = RightsStrategyFactory.get(rightType);
			if(BeanUtils.isEmpty(rightStrategy)) continue;

			_sql = rightStrategy.buildSql(rightsContext);
			if(StringUtil.isEmpty(_sql)) continue;
			if(i>0) sb.append(UNION);

			sb.append(_sql);
			i++;
		}
		return sb.toString();
	}
}
