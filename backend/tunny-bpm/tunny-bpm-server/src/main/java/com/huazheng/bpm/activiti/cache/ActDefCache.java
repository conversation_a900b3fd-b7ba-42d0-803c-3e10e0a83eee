package com.huazheng.bpm.activiti.cache;

import com.huazheng.bpm.util.base.CacheKey;
import com.huazheng.bpm.util.base.CacheKeyGenerator;
import com.huazheng.bpm.util.base.CacheUtil;
import com.huazheng.bpm.util.base.ICache;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.activiti.engine.impl.persistence.deploy.DeploymentCache;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 流程引擎的自定义缓存接口，可以配置到activiti的配置文件中。
 * 支持集群部署，如果集群部署的情况可以使用到memcached缓存。
 * <pre>
 * &lt;property name="processDefinitionCache">
 *	&lt;bean class="com.lc.core.bpm.cache.ActDefCache">&lt;/bean>
 * &lt;/property>
 * 构建组：ibps-bpmn-activiti
 * 作者：simon cai
 * 邮箱:<EMAIL>
 * 日期:2013-12-20-上午10:26:09
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("activitiDefCache")
public class ActDefCache implements DeploymentCache<ProcessDefinitionEntity> {
	@Resource
	private ICache<ProcessDefinitionEntity> iCache;
	@Resource
	private CacheKeyGenerator cacheKeyGenerator;

	/**
	 * 清除线程变量缓存，这个在每次请求前进行调用。
	 */
	public static void clearLocal(){
		ActDefCache cache= AppUtil.getBean(ActDefCache.class);
		cache.clearProcessCache();
	}

	/**
	 * 根据流程定义ID清除缓存，这个在流程定义xml发生变更时进行调用。
	 * @param actDefId
	 */
	public static void clearByDefId(String actDefId){
		ActDefCache cache= AppUtil.getBean(ActDefCache.class);
		cache.clearProcessDefinitionEntity(actDefId);
		cache.clearProcessCache();
	}

	private ThreadLocal<Map<String,ProcessDefinitionEntity>> bpmDefCacheLocal = new ThreadLocal<Map<String,ProcessDefinitionEntity>>();

	 private void clearProcessDefinitionEntity(String defId){
		 remove(defId);
		 bpmDefCacheLocal.remove();
	 }

	 private  void clearProcessCache(){
		 bpmDefCacheLocal.remove();
	 }

	 private  void setThreadLocalDef(ProcessDefinitionEntity processEnt){
		  if(bpmDefCacheLocal.get()==null){
			  Map<String, ProcessDefinitionEntity> map=new HashMap<String, ProcessDefinitionEntity>();
			  map.put(processEnt.getId(),processEnt);
			  bpmDefCacheLocal.set(map);
		  }
		  else{
			  Map<String, ProcessDefinitionEntity> map=bpmDefCacheLocal.get();
			  map.put(processEnt.getId(),processEnt);
		  }
	 }

	 private ProcessDefinitionEntity getThreadLocalDef(String processDefinitionId){
		  if(bpmDefCacheLocal.get()==null){
			  return null;
		  }
		  Map<String, ProcessDefinitionEntity> map=bpmDefCacheLocal.get();
		  if(!map.containsKey(processDefinitionId)){
			  return null;
		  }
		  //LOG.info("get definition from local thread:"+processDefinitionId);
		  return map.get(processDefinitionId);
	 }

	@Override
	public ProcessDefinitionEntity get(String id) {
		CacheKey cacheKey = cacheKeyGenerator.generate(id);
		ProcessDefinitionEntity ent=(ProcessDefinitionEntity)iCache.getByKey(cacheKey.getDefKey());
		if(ent==null) return null;
		//克隆流程定义。
		ProcessDefinitionEntity cloneEnt = (ProcessDefinitionEntity)BeanUtils.cloneObject(ent);
		ProcessDefinitionEntity p=getThreadLocalDef(id);
		if(p==null){
			setThreadLocalDef(cloneEnt);
		}
		p=getThreadLocalDef(id);

		return p;
	}

	@Override
	public void add(String id, ProcessDefinitionEntity object) {
		CacheKey cacheKey = cacheKeyGenerator.generate(id);
		int cacheExpire = CacheUtil.getExpire();
		iCache.add(cacheKey.getDefKey(), object, cacheExpire);
	}

	@Override
	public void remove(String id) {
		CacheKey cacheKey = cacheKeyGenerator.generate(id);
		iCache.delByKey(cacheKey.getDefKey());
	}

	@Override
	public void clear() {
		iCache.clearAll();
	}

}
