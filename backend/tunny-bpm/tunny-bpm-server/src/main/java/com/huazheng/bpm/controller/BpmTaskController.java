package com.huazheng.bpm.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.huazheng.bpm.builder.BpmTaskBuilder;
import com.huazheng.bpm.domain.bpm.BpmTask;
import com.huazheng.bpm.entity.bpm.*;
import com.huazheng.bpm.entity.constant.BpmConstants;
import com.huazheng.bpm.entity.constant.NodeType;
import com.huazheng.bpm.entity.constant.ProcInstStatus;
import com.huazheng.bpm.entity.constant.TaskActionType;
import com.huazheng.bpm.entity.inst.IBpmProcInst;
import com.huazheng.bpm.entity.org.PartyEntityPo;
import com.huazheng.bpm.model.base.*;
import com.huazheng.bpm.model.def.BpmDefLayout;
import com.huazheng.bpm.model.define.*;
import com.huazheng.bpm.model.form.*;
import com.huazheng.bpm.model.image.BpmProcessStatusColor;
import com.huazheng.bpm.model.node.IBpmNodeDefine;
import com.huazheng.bpm.model.node.ProcBoDefine;
import com.huazheng.bpm.model.node.SignNodeDefine;
import com.huazheng.bpm.model.task.IBpmTaskApproval;
import com.huazheng.bpm.plugin.DataObjectHandler;
import com.huazheng.bpm.repository.*;
import com.huazheng.bpm.service.*;
import com.huazheng.bpm.service.form.IFormRightsService;
import com.huazheng.bpm.util.base.*;
import com.huazheng.bpm.util.cmd.ActionCmd;
import com.huazheng.bpm.util.cmd.IbpsTaskFinishCmd;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.JacksonUtil;
import com.huazheng.bpm.util.core.StringPool;
import com.huazheng.bpm.util.json.JsonUtil;
import com.huazheng.bpm.util.json.PageJson;
import com.huazheng.bpm.util.page.PageList;
import com.huazheng.bpm.util.string.StringUtil;
import com.huazheng.bpm.util.web.ContextUtil;
import com.huazheng.bpm.util.web.RequestUtil;
import com.huazheng.bpm.util.web.ResultMessage;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 流程任务控制类
 *
 *<pre>
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：huangchunyan
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-06 09:26:18
 *</pre>
 */
@Controller
@RequestMapping("/platform/bpmn/bpmTask/")
public class BpmTaskController extends GenericController{
	@Resource
	private BpmApprovalService bpmApprovalService;

	/**
	 * 获取流程实例中指定节点的审批意见
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("nodeApproval")
	@ResponseBody
	public Object nodeApproval(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String instId = RequestUtil.getString(request, "instId");
		String nodeId = RequestUtil.getString(request, "nodeId");

		Map<String, Object> data = bpmApprovalService.setApprovalerName(bpmApprovalService.getNodeApprovalMap(instId, nodeId));
		data.put("instId", instId);
		data.put("nodeId", nodeId);

		int bpmNodeUserShowCount = Integer.valueOf(AppUtil.getProperty("bpm.node.user.show.count", "5"));
		data.put("bpmNodeUserShowCount", bpmNodeUserShowCount);

		return data;
	}

	/**
	 * 获取流程实例中指定节点的审批意见
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping("nodeExecutor")
	public ModelAndView nodeExecutor(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String instId = RequestUtil.getString(request, "instId");
		String nodeId = RequestUtil.getString(request, "nodeId");
		String approvalId = RequestUtil.getString(request, "approvalId", "");

		ModelAndView mv = new ModelAndView("/platform/bpmn/instance/bpmInstNodeExecutor.jsp");

		Map<String, Object> data = bpmApprovalService.getNodeApprovalMap(instId, nodeId);
		if(StringUtil.isNotEmpty(approvalId)){
			List<IBpmTaskApproval> approvalList = (List<IBpmTaskApproval>) data.get("data");
			List<QualifiedExecutor> qeList = null;
			for(IBpmTaskApproval approval : approvalList){
				if(approval.getId().equals(approvalId)){
					qeList = approval.getQualifiedExecutor();
				}
			}
			mv.addObject("data", qeList);
			mv.addObject("hasApproval", true);
		}else{
			mv.addObject("hasApproval", false);
			mv.addObject("data", data.get("data"));
		}

		return mv;
	}



}
