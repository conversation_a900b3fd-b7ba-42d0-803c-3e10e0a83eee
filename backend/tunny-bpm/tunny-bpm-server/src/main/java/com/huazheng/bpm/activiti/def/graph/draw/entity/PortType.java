package com.huazheng.bpm.activiti.def.graph.draw.entity;

/**
 * 程序设计器 端口类型。
 *
 * <pre>
 * 构建组：ibps-bpmn-activiti
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月29日-下午3:16:38
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum PortType {
	POSITION("position"),
	NODE_PART_REFERENCE("nodePartReference"),
	AUTOMATIC_SIDE("automaticSide");

	private String text;

	PortType(String text) {
		this.text = text;
	}

	public String getText() {
		return this.text;
	}

	@Override
	public String toString() {
		return this.text;
	}

	public static PortType fromString(String text) {
		if (text != null) {
			for (PortType type : PortType.values()) {
				if (text.equalsIgnoreCase(type.text)) {
					return type;
				}
			}
		}
		return null;
	}
}
