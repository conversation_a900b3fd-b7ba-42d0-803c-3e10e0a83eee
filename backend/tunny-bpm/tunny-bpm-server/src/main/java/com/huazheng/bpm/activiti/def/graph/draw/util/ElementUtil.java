package com.huazheng.bpm.activiti.def.graph.draw.util;

import org.dom4j.Element;

public class ElementUtil {

	/**
	 *
	 * 获取xml文档元素的Boolean类型的属性。
	 *
	 * @param element
	 *            xml文档元素
	 * @param name
	 *            xml文档元素的属性
	 * @param defaultValue
	 * @return
	 */
	public static Boolean getBoolean(Element element, String name, Boolean defaultValue) {
		String val = element.attributeValue(name);
		if (val != null) {
			return val.equalsIgnoreCase("true");
		} else {
			return defaultValue;
		}
	}

	/**
	 * 获取xml文档元素的Boolean类型的属性。
	 *
	 * @param element
	 *            xml文档元素
	 * @param name
	 *            xml文档元素的属性名
	 * @return
	 */
	public static Boolean getBoolean(Element element, String name) {
		return getBoolean(element, name, null);
	}

	/**
	 *
	 * 返回 Float 值
	 *
	 * @param element
	 * @param name
	 * @return
	 */
	public static Float getFloat(Element element, String name) {
		return getFloat(element, name, 0f);
	}

	/**
	 * 返回 Float 值
	 *
	 * @param element
	 * @param name
	 * @param defaultValue
	 * @return
	 */
	public static Float getFloat(Element element, String name, Float defaultValue) {
		String val = element.attributeValue(name);
		if (val != null) {
			return Float.parseFloat(val);
		} else {
			return defaultValue;
		}
	}

	/**
	 *
	 * 返回 Float 值
	 *
	 * @param element
	 * @param name
	 * @return
	 */
	public static Double getDouble(Element element, String name) {
		return getDouble(element, name, 0d);
	}

	/**
	 * 返回 Double 值
	 *
	 * @param element
	 * @param name
	 * @param defaultValue
	 * @return
	 */
	public static Double getDouble(Element element, String name, Double defaultValue) {
		String val = element.attributeValue(name);
		if (val != null) {
			return Double.parseDouble(val);
		} else {
			return defaultValue;
		}
	}

}
