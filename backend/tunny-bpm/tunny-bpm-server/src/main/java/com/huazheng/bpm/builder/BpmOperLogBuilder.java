/**
 * 描述：流程操作日志构建工具
 * 包名：com.huazheng.bpm.biz.bpmn.builder
 * 文件名：BpmOperLogBuilder.java
 * 作者：eddy
 * 日期：2017年4月21日-下午12:53:50
 * 版权：广州流辰信息技术有限公司版权所有
 *
 */
package com.huazheng.bpm.builder;

import com.huazheng.bpm.entity.bpm.BpmOperLogPo;
import com.huazheng.bpm.entity.constant.BpmOperTypeEnum;
import com.huazheng.bpm.entity.inst.IBpmProcInst;
import com.huazheng.bpm.model.base.User;
import com.huazheng.bpm.model.define.IBpmDefine;
import com.huazheng.bpm.model.task.IBpmTask;
import com.huazheng.bpm.repository.BpmTaskRepository;
import com.huazheng.bpm.service.BpmDefineService;
import com.huazheng.bpm.service.IPartyUserService;
import com.huazheng.bpm.util.cmd.TaskFinishCmd;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.StringPool;
import com.huazheng.bpm.util.db.UniqueIdUtil;
import com.huazheng.bpm.util.web.ContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * 流程操作日志构建工具
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年4月21日-下午12:53:50
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmOperLogBuilder {

	private static final Logger logger = LoggerFactory.getLogger(BpmOperLogBuilder.class);

	/**
	 * 构建流程操作日志
	 *
	 * @param task
	 */
	public static void build(List<BpmOperLogPo> bpmOperLogList){
		if(BeanUtils.isEmpty(bpmOperLogList)){
			return;
		}

		for(BpmOperLogPo bpmOperLogPo : bpmOperLogList){
			build(bpmOperLogPo);
		}
	}

	/**
	 * 构建流程操作日志
	 *
	 * @param task
	 */
	public static void build(BpmOperLogPo bpmOperLogPo){
		if(BeanUtils.isEmpty(bpmOperLogPo)){
			return;
		}

		BpmOperTypeEnum operType = BpmOperTypeEnum.fromKey(bpmOperLogPo.getOperType());
		bpmOperLogPo.setOperTypeName(operType.getValue());

		IPartyUserService partyUserService = AppUtil.getBean(IPartyUserService.class);
		User user = null;
				//DefaultPartyUserPo.fromJsonString2(partyUserService.getByIdJson(bpmOperLogPo.getCreateBy()));
		if(BeanUtils.isEmpty(user)){
			logger.warn("用户不存在或已被删除！");
			bpmOperLogPo.setCreator(bpmOperLogPo.getCreateBy());
		}else{
			bpmOperLogPo.setCreator(user.getFullname());
		}

		BpmDefineService bpmDefineService = AppUtil.getBean(BpmDefineService.class);
		IBpmDefine bpmDefine = bpmDefineService.getBpmDefinitionByDefId(bpmOperLogPo.getProcDefId());
		if(BeanUtils.isEmpty(bpmDefine)){
			logger.warn("流程定义不存在或已被删除！{}-{}-{}", bpmOperLogPo.getOperTypeName(), bpmOperLogPo.getProcDefId(), bpmOperLogPo.getProcDefKey());
			bpmOperLogPo.setProcDefName(bpmOperLogPo.getProcDefId());
		}else{
			bpmOperLogPo.setProcDefName(bpmDefine.getName());
		}
	}

	/**
	 * 构建流程操作日志-流程启动、结束
	 *
	 * @param task
	 */
	public static BpmOperLogPo build(IBpmProcInst instance, BpmOperTypeEnum operType, String curUser){
		if(BeanUtils.isEmpty(instance)){
			return null;
		}

		BpmOperLogPo po = new BpmOperLogPo();
		po.setId(UniqueIdUtil.getId());
		po.setProcInstSubject(instance.getSubject());
		po.setProcInstId(instance.getId());
		po.setProcDefId(instance.getProcDefId());
		po.setProcDefKey(instance.getProcDefKey());
		po.setOption(operType.getValue());
		po.setOperType(operType.getKey());
		po.setNodeId(null);
		po.setTaskId(null);
		po.setInterpose(StringPool.N);
		po.setCreateTime(new Date());
		po.setCreateBy(curUser);
		po.setContent(operType.getValue());

		return po;
	}

	/**
	 * 构建流程操作日志-任务审批
	 *
	 * @param task
	 */
	public static BpmOperLogPo build(IBpmTask bpmTask, TaskFinishCmd cmd){
		if(BeanUtils.isEmpty(bpmTask)){
			return null;
		}

		BpmOperTypeEnum operType = BpmOperTypeEnum.fromKey(cmd.getActionName());
		String interpose = StringPool.N;
		if(cmd.isInterpose()){
			interpose = StringPool.Y;
		}

		BpmOperLogPo po = new BpmOperLogPo();
		po.setId(UniqueIdUtil.getId());
		po.setProcInstSubject(bpmTask.getSubject());
		po.setProcInstId(bpmTask.getProcInstId());
		po.setProcDefId(bpmTask.getProcDefId());
		po.setProcDefKey(bpmTask.getProcDefKey());
		po.setOption(cmd.getApprovalOpinion());
		po.setOperType(operType.getKey());
		po.setNodeId(bpmTask.getNodeId());
		po.setTaskId(bpmTask.getId());
		po.setInterpose(interpose);
		po.setCreateTime(new Date());
		po.setCreateBy(cmd.getCurUser());
		po.setContent(operType.getValue());

		return po;
	}

	/**
	 * 构建流程操作日志-流程图、审批历史、转办、撤销
	 *
	 * @param task
	 */
	public static BpmOperLogPo build(IBpmTask bpmTask, BpmOperTypeEnum operType, String curUser){
		if(BeanUtils.isEmpty(bpmTask)){
			return null;
		}

		String interpose = StringPool.N;

		BpmOperLogPo po = new BpmOperLogPo();
		po.setId(UniqueIdUtil.getId());
		po.setProcInstSubject(bpmTask.getSubject());
		po.setProcInstId(bpmTask.getProcInstId());
		po.setProcDefId(bpmTask.getProcDefId());
		po.setProcDefKey(bpmTask.getProcDefKey());
		po.setOption(operType.getValue());
		po.setOperType(operType.getKey());
		po.setNodeId(bpmTask.getNodeId());
		po.setTaskId(bpmTask.getId());
		po.setInterpose(interpose);
		po.setCreateTime(new Date());
		po.setCreateBy(curUser);
		po.setContent(operType.getValue());

		return po;
	}

	/**
	 * 构建流程操作日志-终止流程、补签
	 *
	 * @param task
	 */
	public static BpmOperLogPo build(IBpmTask bpmTask, BpmOperTypeEnum operType, String cause, String curUser){
		if(BeanUtils.isEmpty(bpmTask)){
			return null;
		}

		String interpose = StringPool.N;
		BpmTaskRepository bpmTaskRepository = AppUtil.getBean(BpmTaskRepository.class);
		if(!bpmTaskRepository.isCandidate(bpmTask.getId(), ContextUtil.getCurrentUserId())){
			interpose = StringPool.Y;
		}

		BpmOperLogPo po = new BpmOperLogPo();
		po.setId(UniqueIdUtil.getId());
		po.setProcInstSubject(bpmTask.getSubject());
		po.setProcInstId(bpmTask.getProcInstId());
		po.setProcDefId(bpmTask.getProcDefId());
		po.setProcDefKey(bpmTask.getProcDefKey());
		po.setOption(cause);
		po.setOperType(operType.getKey());
		po.setNodeId(bpmTask.getNodeId());
		po.setTaskId(bpmTask.getId());
		po.setInterpose(interpose);
		po.setCreateTime(new Date());
		po.setCreateBy(curUser);
		po.setContent(operType.getValue());

		return po;
	}

}
