package com.huazheng.bpm.activiti.def.service.impl;

import com.huazheng.bpm.activiti.cache.ActDefCache;
import com.huazheng.bpm.activiti.def.dao.ProDefineDao;
import com.huazheng.bpm.activiti.def.impl.WebDefTransform;
import com.huazheng.bpm.activiti.engine.ProcessEngineConfiguration;
import com.huazheng.bpm.activiti.engine.RepositoryService;
import com.huazheng.bpm.activiti.engine.impl.cfg.ProcessEngineConfigurationImpl;
import com.huazheng.bpm.activiti.engine.impl.persistence.deploy.DeploymentCache;
import com.huazheng.bpm.activiti.engine.impl.persistence.deploy.DeploymentManager;
import com.huazheng.bpm.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import com.huazheng.bpm.activiti.engine.repository.Deployment;
import com.huazheng.bpm.activiti.engine.repository.DeploymentBuilder;
import com.huazheng.bpm.activiti.engine.repository.ProcessDefinition;
import com.huazheng.bpm.service.DefTransform;
import com.huazheng.bpm.service.NatProDefineService;
import com.huazheng.bpm.util.core.AppUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Date;



/**
 * 流程定义服务类
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-act
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月16日-上午10:30:26
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Transactional
@Service
public class ProDefineServiceImpl implements NatProDefineService {

	@Resource
	private RepositoryService repositoryService;
	@Resource
	private ProDefineDao proDefineDao;

	public String deploy(String tenantId, String name, String defXml) throws UnsupportedEncodingException {
		InputStream stream = new ByteArrayInputStream(defXml.getBytes(StandardCharsets.UTF_8));
		DeploymentBuilder deploymentBuilder = repositoryService.createDeployment();
		deploymentBuilder.tenantId(tenantId);
		deploymentBuilder.name(name);
		deploymentBuilder.addInputStream("bpmn20.xml", stream);
		Deployment deploy = deploymentBuilder.deploy();

		return deploy.getId();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.lc.ibps.bpmn.natapi.proc.NatProDefinitionService#getDefXmlByDeployId(
	 * java.lang.String)
	 */
	public String getDefXmlByDeployId(String deployId) {
		return proDefineDao.getDefXmlByDeployId(deployId);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.lc.ibps.bpmn.natapi.proc.NatProDefinitionService#writeDefXml(java.
	 * lang.String, java.lang.String)
	 */
	public void writeDefXml(String deployId, String defXml) {
		proDefineDao.writeDefXml(deployId, defXml);
		String actDefId = getProcessDefinitionIdByDeployId(deployId);
		ActDefCache.clearByDefId(actDefId);

		/*2021-6-21 xurui 清理activiti全局流程定义解析数据缓存，人工干预造成DeploymentManager类resolveProcessDefinition方法中获取的流程定义解析数据缓存为null，
		 * 迫使activiti重新获取该版本流程定义解析数据，解决流程定义在当前版本流程定义上修改出现缓存与实际流程定义不同步问题。
		 */
		DeploymentManager deploymentManager = AppUtil.getBean(ProcessEngineConfigurationImpl.class).getDeploymentManager();
		DeploymentCache<ProcessDefinitionEntity> deploymentCache = deploymentManager.getProcessDefinitionCache();
		deploymentCache.remove(actDefId);
	}

	public String getProcessDefinitionIdByDeployId(String deployId) {
		ProcessDefinition proDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deployId)
				.singleResult();
		if (proDefinition == null)
			return null;
		return proDefinition.getId();
	}

	@Override
	public void clearCacheByBpmnDefId(String bpmnDefId) {
		ActDefCache.clearByDefId(bpmnDefId);
	}

	@Override
	public DefTransform getDefTransform() {
		DefTransform trans = new WebDefTransform();

		return trans;
	}

	@Override
	public void suspendProcessDefinitionById(String processDefinitionId, boolean suspendProcessInstances,
			Date suspensionDate) {
		repositoryService.suspendProcessDefinitionById(processDefinitionId, suspendProcessInstances, suspensionDate);
	}

	@Override
	public void suspendProcessDefinitionByKey(String processDefinitionKey, boolean suspendProcessInstances,
			Date suspensionDate) {
		repositoryService.suspendProcessDefinitionByKey(processDefinitionKey, suspendProcessInstances, suspensionDate);
	}

	@Override
	public void activateProcessDefinitionById(String processDefinitionId, boolean activateProcessInstances,
			Date activationDate) {
		repositoryService.activateProcessDefinitionById(processDefinitionId, activateProcessInstances, activationDate);
	}

	@Override
	public void activateProcessDefinitionByKey(String processDefinitionKey, boolean activateProcessInstances,
			Date activationDate) {
		repositoryService.activateProcessDefinitionByKey(processDefinitionKey, activateProcessInstances, activationDate);
	}

}
