/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.huazheng.bpm.activiti.engine.delegate.event.impl;


import com.huazheng.bpm.activiti.engine.delegate.event.ActivitiEventType;
import com.huazheng.bpm.activiti.engine.delegate.event.ActivitiMembershipEvent;

/**
 * Implementation of {@link ActivitiMembershipEvent}.
 * <AUTHOR>
 */
public class ActivitiMembershipEventImpl extends ActivitiEventImpl implements ActivitiMembershipEvent {

	protected String userId;
	protected String groupId;

	public ActivitiMembershipEventImpl(ActivitiEventType type) {
	  super(type);
  }

	public void setUserId(String userId) {
	  this.userId = userId;
  }

	public String getUserId() {
	  return userId;
  }

	public void setGroupId(String groupId) {
	  this.groupId = groupId;
  }

	public String getGroupId() {
	  return groupId;
  }
}
