package com.huazheng.bpm.activiti.def.graph.draw.activiti;

import com.huazheng.bpm.activiti.def.graph.draw.bpmn.*;
import com.huazheng.bpm.activiti.def.graph.draw.entity.ImageType;
import com.huazheng.bpm.activiti.def.graph.draw.util.ElementUtil;
import com.huazheng.bpm.activiti.def.graph.draw.util.MathUtil;
import com.huazheng.bpm.entity.constant.ServiceTaskType;
import com.huazheng.bpm.model.base.FlowImageStyle;
import com.huazheng.bpm.util.base.Dom4jUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import org.dom4j.Document;
import org.dom4j.Element;

import java.awt.*;
import java.awt.geom.Point2D;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.util.*;
import java.util.List;


/**
 * 此类用于能过bpmn的xml文档生成相应的图片。此类中主要使用静态方法。。
 *
 * <pre>
 * 构建组：ibps-bpmn-act
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年3月23日-上午9:56:16
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class ProcessDiagramGenerator {

	protected static final Map<BPMNShapType, com.huazheng.bpm.activiti.def.graph.draw.activiti.GraphDrawInstruction> graphDrawInstructions = new HashMap<BPMNShapType, GraphDrawInstruction>();

	// 静态代码段，将实现画图的功能GraphDrawInstructions实现类实例存放到Map
	// graphDrawInstructions中，使用时，直接通过Key从Map中取出相应的实例，并调用实例的draw方法进行画图操作。
	static {
		// StartEvent （开始节点）
		graphDrawInstructions.put(BPMNShapType.START_EVENT, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawNoneStartEvent(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});
		// ErrorEvent （异常节点）
		graphDrawInstructions.put(BPMNShapType.ERROR_EVENT, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawErrorEndEvent(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// EndEvent（结束节点）
		graphDrawInstructions.put(BPMNShapType.END_EVENT, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawNoneEndEvent(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});
		// CancelEvent
		graphDrawInstructions.put(BPMNShapType.CANCEL_EVENT, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawErrorEndEvent(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// Task
		graphDrawInstructions.put(BPMNShapType.TASK, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawTask(shap.getName(), MathUtil.round(shap.getX()), MathUtil.round(shap.getY()),
						MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// UserTask
		graphDrawInstructions.put(BPMNShapType.USER_TASK, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawUserTask(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// ScriptTask
		graphDrawInstructions.put(BPMNShapType.SCRIPT_TASK, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawScriptTask(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// ServiceTask
		graphDrawInstructions.put(BPMNShapType.SERVICE_TASK, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawServiceTask(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// ReceiveTask
		graphDrawInstructions.put(BPMNShapType.RECEIVE_TASK, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawReceiveTask(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// SendTask
		graphDrawInstructions.put(BPMNShapType.SEND_TASK, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawSendTask(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// ManualTask
		graphDrawInstructions.put(BPMNShapType.MANUAL_TASK, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawManualTask(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// ExclusiveGateway
		graphDrawInstructions.put(BPMNShapType.EXCLUSIVE_GATEWAY, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawExclusiveGateway(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});
		// InclusiveGateway
		graphDrawInstructions.put(BPMNShapType.INCLUSIVE_GATEWAY, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawInclusiveGateway(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// ParallelGateway
		graphDrawInstructions.put(BPMNShapType.PARALLEL_GATEWAY, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawParallelGateway(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// SubProcess
		graphDrawInstructions.put(BPMNShapType.SUB_PROCESS, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {

				if (shap.isExpanded() != null && shap.isExpanded() == false) {
					processDiagramCanvas.drawCollapsedSubProcess(shap.getName(), MathUtil.round(shap.getX()),
							MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()),
							MathUtil.round(shap.getHeight()));
				} else {
					processDiagramCanvas.drawExpandedSubProcess(shap.getName(), MathUtil.round(shap.getX()),
							MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()),
							MathUtil.round(shap.getHeight()));
				}
			}
		});

		// CallActivity
		graphDrawInstructions.put(BPMNShapType.CALL_ACTIVITY, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawCollapsedCallActivity(shap.getName(), MathUtil.round(shap.getX()),
						MathUtil.round(shap.getY()), MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

		// HPool
		graphDrawInstructions.put(BPMNShapType.H_POOL, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawHPool(shap.getName(), MathUtil.round(shap.getX()), MathUtil.round(shap.getY()),
						MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});
		// HLane
		graphDrawInstructions.put(BPMNShapType.H_LANE, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawHLane(shap.getName(), MathUtil.round(shap.getX()), MathUtil.round(shap.getY()),
						MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});
		// VPool
		graphDrawInstructions.put(BPMNShapType.V_POOL, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawVPool(shap.getName(), MathUtil.round(shap.getX()), MathUtil.round(shap.getY()),
						MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});
		// VLane
		graphDrawInstructions.put(BPMNShapType.V_LANE, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
				processDiagramCanvas.drawVLane(shap.getName(), MathUtil.round(shap.getX()), MathUtil.round(shap.getY()),
						MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});
		//textAnnotation
		graphDrawInstructions.put(BPMNShapType.TEXT_ANNOTATION, new GraphDrawInstruction() {
			@Override
			public void draw(ProcessDiagramCanvas processDiagramCanvas, BPMNShap shap) {
					processDiagramCanvas.drawTextAnnotation(shap.getName(), MathUtil.round(shap.getX()), MathUtil.round(shap.getY()),
						MathUtil.round(shap.getWidth()), MathUtil.round(shap.getHeight()));
			}
		});

	}

	/**
	 *
	 * 通过用xml表示的流程定义和流程图样式状态，生成PNG图片。
	 *
	 * @param bpmnXml
	 *            xml表示的流程定义
	 * @param imageStyle
	 *            流程图样式
	 *
	 * @return 生成的PNG图片
	 */
	public static InputStream generatePngDiagram(String bpmnXml, Map<String, FlowImageStyle> imageStyle) {
		return generateDiagram(bpmnXml, imageStyle, ImageType.PNG);
	}

	/**
	 * 通过用xml表示的流程定义、要求用高亮表示的图形组件和要求生成图片的类型，生成相应类型图片。 支持的图形类型，请参考
	 * <code>{@link javax.imageio.ImageIO}</code>
	 *
	 * @param bpmnXml
	 *            xml表示的流程定义
	 * @param highLightedActivities
	 *            要求用高亮表示的图形组件
	 * @param imageType
	 *            要求生成图片的类型
	 * @return 生成的PNG图片
	 * @see javax.imageio.ImageIO
	 */
	public static InputStream generateDiagram(String bpmnXml, Map<String, FlowImageStyle> imageStyle,
			ImageType imageType) {
		return generateDiagram(bpmnXml, imageStyle).generateImage(imageType);
	}

	/**
	 *
	 * 通过用xml表示的流程定义和流程图样式状态，生成指定的类型(png,jpg)图片。
	 *
	 * @param bpmnXml
	 *            xml表示的流程定义
	 * @param imageStyle
	 *            流程图样式
	 * @param imageType
	 *            图片格式
	 * @return 生成的指定格式的图片
	 */
	public static ProcessDiagramCanvas generateDiagram(String bpmnXml, Map<String, FlowImageStyle> imageStyle) {
		// 获取定义流程的xml文档中的所有图形组件
		List<BPMNShap> bpmnShaps = extractBPMNShap(bpmnXml);
		// 获取xml文件中定义的所有连线。
		List<BPMNEdge> bpmnEdges = extractBPMNEdge(bpmnXml);
		ProcessDiagramCanvas processDiagramCanvas = initProcessDiagramCanvas(bpmnShaps, bpmnEdges);
		for (BPMNShap bpmnShap : bpmnShaps) {
			drawActivity(processDiagramCanvas, imageStyle, bpmnShap);
		}
		// 画之间的连线
		drawSequenceFlows(processDiagramCanvas, bpmnEdges);
		return processDiagramCanvas;
	}

	/**
	 * 在指定画布上，画出指定的图形。如果指定的图形是要求高亮的图形组件highLightedActivities中的一个，刚将图形高亮表示。
	 *
	 * @param processDiagramCanvas
	 *            用于绘画图形的画布
	 * @param highLightedActivities
	 *            要求用高亮表示的图形组件
	 * @param bpmnShap
	 *            要示绘画的图形
	 */
	public static void drawActivity(ProcessDiagramCanvas processDiagramCanvas, Map<String, FlowImageStyle> imageStyle,
			BPMNShap bpmnShap) {
		GraphDrawInstruction drawInstruction = graphDrawInstructions.get(bpmnShap.getType());
		if (BeanUtils.isEmpty(drawInstruction))
			return;
		// 画流程图节点
		drawInstruction.draw(processDiagramCanvas, bpmnShap);

		// 收集信息的多实例标记（Gather info on the multi instance marker）
		boolean multiInstanceSequential = false, multiInstanceParallel = false, collapsed = false;
		Properties properties = bpmnShap.getProperties();
		// 是否是串行
		String isSequential = (properties != null ? ((String) properties.get("isSequential")) : null);
		// 是否多实例并行或串行
		if (isSequential != null) {
			if ("true".equals(isSequential)) {
				multiInstanceSequential = true;
			} else {
				multiInstanceParallel = true;
			}
		}

		// Gather info on the collapsed marker
		collapsed = bpmnShap.isExpanded() !=null ? !(bpmnShap.isExpanded()):collapsed;

		// Actually draw the markers
		processDiagramCanvas.drawActivityMarkers(MathUtil.round(bpmnShap.getX()), MathUtil.round(bpmnShap.getY()),
				MathUtil.round(bpmnShap.getWidth()), MathUtil.round(bpmnShap.getHeight()), multiInstanceSequential,
				multiInstanceParallel, collapsed);

		//画水印
//		processDiagramCanvas.markLogo();

		// Draw highlighted activities
		if (BeanUtils.isEmpty(imageStyle))
			return;
		String bpmnElement = bpmnShap.getBpmnElement();
		if (imageStyle.containsKey(bpmnElement))
			drawHighLight(processDiagramCanvas, bpmnShap, imageStyle.get(bpmnElement));
	}

	private static void drawHighLight(ProcessDiagramCanvas processDiagramCanvas, BPMNShap bpmnShap,
			FlowImageStyle flowImageStyle) {
		processDiagramCanvas.drawHighLight(MathUtil.round(bpmnShap.getX()), MathUtil.round(bpmnShap.getY()),
				MathUtil.round(bpmnShap.getWidth()), MathUtil.round(bpmnShap.getHeight()), flowImageStyle);
	}

	/**
	 * 在指定画布上，画出指定的连线。
	 *
	 * @param processDiagramCanvas
	 *            用于绘画图形的画布
	 * @param bpmnEdges
	 *            要示绘画的连线
	 */
	public static void drawSequenceFlows(ProcessDiagramCanvas processDiagramCanvas, List<BPMNEdge> bpmnEdges) {
		for (BPMNEdge bpmnEdge : bpmnEdges) {
			processDiagramCanvas.drawSequenceflowWidthLabel(bpmnEdge);
		}
	}

	/**
	 * 初始化画布。设定画布的大小。
	 *
	 * @param shaps
	 *            流程定义中的所有图形组件。
	 * @param edges
	 *            流程定义中的所有连线。
	 * @return 初始化后的画布。
	 */
	public static ProcessDiagramCanvas initProcessDiagramCanvas(List<BPMNShap> shaps, List<BPMNEdge> edges) {
		Point2D.Double[] points = caculateCanvasSize(shaps, edges);
		double shiftX = points[0].getX() < 0 ? points[0].getX() : 0;
		double shiftY = points[0].getY() < 0 ? points[0].getY() : 0;
		shiftProcessDefinition(shaps, edges, shiftX, shiftY);

		int width = MathUtil.round((points[1].getX() + 10 - shiftX));
		int height = MathUtil.round((points[1].getY() + 10 - shiftY));
		int minX = MathUtil.round((points[0].getX() - shiftX));
		int minY = MathUtil.round((points[0].getY() - shiftY));
		return new ProcessDiagramCanvas(width, height, minX, minY);

	}

	/**
	 * 根据所有将绘画的图形组件和连线，计算所需画布的大小。 返回用数组类型<code>Point2D.Double[]
	 * <code>表示的画布大小。Point2D.Double[0]<code>中的x,y分别表示所有将绘画的图形组件和连线
	 * 中的最小坐标。Point2D.Double[1]<code>中的x,y分别表示所有将绘画的图形组件和连线中的最大坐标。
	 *
	 * @param shaps
	 *            所有将绘画的图形
	 * @param edges
	 *            所有将绘画的连线
	 * @return 画布的大小。
	 */
	public static Point2D.Double[] caculateCanvasSize(List<BPMNShap> shaps, List<BPMNEdge> edges) {
		double minX = Double.MAX_VALUE;
		double minY = Double.MAX_VALUE;
		double maxX = 0;
		double maxY = 0;

		for (BPMNShap shap : shaps) {
			if (shap.getX() < minX) {
				minX = shap.getX();
			}
			if (shap.getY() < minY) {
				minY = shap.getY();
			}

			if (shap.getX() + shap.getWidth() > maxX) {
				maxX = shap.getX() + shap.getWidth();
			}

			if (shap.getY() + shap.getHeight() > maxY) {
				maxY = shap.getY() + shap.getHeight();
			}
		}

		for (BPMNEdge edge : edges) {
			for (Point2D.Double point : edge.getPoints()) {
				if (point.getX() < minX) {
					minX = point.getX();
				}
				if (point.getY() < minY) {
					minY = point.getY();
				}

				if (point.getX() > maxX) {
					maxX = point.getX();
				}

				if (point.getY() > maxY) {
					maxY = point.getY();
				}
			}

			// 计算线上标签的最大最小坐标
			String label = edge.getName() == null ? "" : edge.getName();
			Point2D.Double midPoint = edge.getMidpoint();
			DirectionType directionType = edge.getDirection();
			FontMetrics fontMetrics = getFontMetrics();
			double labelMinX;
			double labelMinY;
			double labelMaxX;
			double labelMaxY;

			if (directionType.equals(DirectionType.UP_TO_DOWN)) {
				labelMinX = midPoint.getX() + fontMetrics.getHeight() / 2;
				labelMinY = midPoint.getY();
			} else if (directionType.equals( DirectionType.DOWN_TO_UP)) {
				labelMinX = midPoint.getX() - fontMetrics.stringWidth(label) - fontMetrics.getHeight() / 2;
				labelMinY = midPoint.getY() - fontMetrics.getHeight() / 2 - fontMetrics.getHeight();
			} else if (directionType.equals(DirectionType.LEFT_TO_RIGHT)) {
				labelMinX = midPoint.getX() - fontMetrics.stringWidth(label) / 2;
				labelMinY = midPoint.getY();
			} else {
				labelMinX = fontMetrics.stringWidth(label) / 2;
				labelMinY = midPoint.getY() + fontMetrics.getHeight() - fontMetrics.getHeight();
			}

			labelMaxX = labelMinX + fontMetrics.stringWidth(label);
			labelMaxY = labelMinY + fontMetrics.getHeight();

			if (labelMinX < minX) {
				minX = labelMinX;
			}
			if (labelMinY < minY) {
				minY = labelMinY;
			}
			if (labelMaxX > maxX) {
				maxX = labelMaxX;
			}
			if (labelMaxY > maxY) {
				maxY = labelMaxY;
			}

		}

		return new Point2D.Double[] { new Point2D.Double(minX, minY), new Point2D.Double(maxX, maxY) };
	}

	/**
	 *
	 * 字体
	 *
	 * @return
	 */
	private static FontMetrics getFontMetrics() {
		BufferedImage processDiagram = new BufferedImage(2, 2, 2);
		Graphics2D g = processDiagram.createGraphics();
		g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
		g.setPaint(Color.black);
		Font font = new Font("宋体", 1, 12);
		g.setFont(font);
		FontMetrics fontMetrics = g.getFontMetrics();
		return fontMetrics;
	}

	/**
	 * 图形和连线的坐标点中存在负值时，就对整个流程定义中的图形和连线的进行移位。
	 *
	 * @param processDefinition
	 *            所要进行移位的图形。
	 * @param x
	 *            图形中，最的x坐标点。
	 * @param y
	 *            图形中，最的y坐标点。
	 * @return
	 */
	private static void shiftProcessDefinition(List<BPMNShap> shaps, List<BPMNEdge> edges, double x, double y) {
		for (BPMNShap shap : shaps) {
			shap.setX(shap.getX() - x);
			shap.setY(shap.getY() - y);
		}
		for (BPMNEdge edge : edges) {
			for (Point2D.Double point : edge.getPoints()) {
				point.x = point.getX() - x;
				point.y = point.getY() - y;
			}
			edge.getMidpoint().x = edge.getMidpoint().getX() - x;
			edge.getMidpoint().y = edge.getMidpoint().getY() - y;
		}
	}

	/**
	 * 获取定义流程的xml文档中的所有图形组件。
	 *
	 * @param bpmnXml
	 *            定义流程的xml文档
	 * @return 从xml文档中取得的组件的列表
	 */
	@SuppressWarnings("unchecked")
	public static List<BPMNShap> extractBPMNShap(String bpmnXml) {
		bpmnXml = bpmnXml.replace("xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\"", "");
		Document doc = Dom4jUtil.loadXml(bpmnXml);
		Element root = doc.getRootElement();

		List<Element> shaps = root.selectNodes("//bpmndi:BPMNShape");
		List<BPMNShap> bpmnShaps = new ArrayList<BPMNShap>();
		for (Element shap : shaps) {
			BPMNShap bpmnShap = new BPMNShap();

			// BPMNShap节点属性
			bpmnShap.setBpmnElement(shap.attributeValue("bpmnElement"));
			bpmnShap.setChoreographyActivityShape(shap.attributeValue("choreographyActivityShape"));
			bpmnShap.setHorizontal(ElementUtil.getBoolean(shap, "isHorizontal"));
			bpmnShap.setExpanded(ElementUtil.getBoolean(shap, "isExpanded"));
			bpmnShap.setMarkerVisible(ElementUtil.getBoolean(shap, "isMarkerVisible"));
			bpmnShap.setMessageVisible(ElementUtil.getBoolean(shap, "isMessageVisible"));
			bpmnShap.setParticipantBandKind(shap.attributeValue("participantBandKind"));

			// Bounds节点属性，坐标点
			Element bound = (Element) shap.selectSingleNode("./omgdc:Bounds");
			bpmnShap.setX(ElementUtil.getDouble(bound, "x"));
			bpmnShap.setY( ElementUtil.getDouble(bound, "y"));
			bpmnShap.setWidth(ElementUtil.getDouble(bound, "width"));
			bpmnShap.setHeight(ElementUtil.getDouble(bound, "height"));

			// 组件类型
			Element component = (Element) root.selectSingleNode("//*[@id='" + bpmnShap.getBpmnElement() + "']");
			if (component == null)
				continue;
			BPMNShapType type = getBPMNShapType(component);

			bpmnShap.setType(type);
			// 组件标签名字
			bpmnShap.setName(component.attributeValue("name"));
			// 设置其它属性
			setBPMNShapProperties(component, bpmnShap);
			bpmnShaps.add(bpmnShap);
		}
		return bpmnShaps;
	}

	/**
	 * 获取xml文件中定义的所有连线。
	 *
	 * @param bpmnXml
	 *            定义流程的xml文档
	 * @return 从xml文档中取得的连线的列表
	 */
	@SuppressWarnings("unchecked")
	public static List<BPMNEdge> extractBPMNEdge(String bpmnXml) {
		List<BPMNEdge> bpmnEdges = new ArrayList<BPMNEdge>();

		bpmnXml = bpmnXml.replace("xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\"", "");
		Document doc = Dom4jUtil.loadXml(bpmnXml);
		Element root = doc.getRootElement();

		List<Element> edges = root.selectNodes("//bpmndi:BPMNEdge");
		for (Element edge : edges) {
			BPMNEdge bpmnEdge = new BPMNEdge();
			// 取得所有点
			List<Point2D.Double> points = new ArrayList<Point2D.Double>();
			List<Element> waypoints = edge.selectNodes("./omgdi:waypoint");
			for (Element waypoint : waypoints) {
				double x = Double.parseDouble(waypoint.attributeValue("x"));
				double y = Double.parseDouble(waypoint.attributeValue("y"));
				Point2D.Double point = new Point2D.Double(x, y);
				points.add(point);
			}
			bpmnEdge.setPoints(points);
			// 取得标签名字
			String bpmnElement = edge.attributeValue("bpmnElement");
			Element sequenceFlow = (Element) root.selectSingleNode("//sequenceFlow[@id='" + bpmnElement + "']");
			String labelName = "";
			FlowType flowType = FlowType.SEQUENCE_FLOW;
			if(BeanUtils.isEmpty(sequenceFlow)){
				Element association = (Element) root.selectSingleNode("//association[@id='" + bpmnElement + "']");
				labelName = association != null?association.attributeValue("name"):"";
				flowType = FlowType.ASSOCIATION;
			}else{
				labelName = sequenceFlow != null?sequenceFlow.attributeValue("name"):"";
			}
			bpmnEdge.setName(labelName);
			bpmnEdge.setFlowType(flowType);

			// 计算中点
			double x = 0, y = 0;
			DirectionType directionType;
			List<Double> lens = new ArrayList<Double>();
			for (int i = 1; i < points.size(); i++) {
				lens.add(Math.abs(points.get(i - 1).getX() - points.get(i).getX())
						+ Math.abs(points.get(i - 1).getY() - points.get(i).getY()));
			}
			double halfLen = 0;
			for (double len : lens) {
				halfLen += len;
			}
			halfLen = halfLen / 2;

			double accumulativeLen = 0;

			int i;
			for (i = 0; i < lens.size(); i++) {
				accumulativeLen += lens.get(i);
				if (accumulativeLen > halfLen) {
					break;
				}
			}

			// x坐标相等，
			if (points.get(i).getX() == points.get(i + 1).getX()) {
				// 前一个点在后一个点的上边
				if (points.get(i).getY() < points.get(i + 1).getY()) {
					// accumulativeLen-halfLen=points.get(i+1).getY()-y;
					y = halfLen - accumulativeLen + points.get(i + 1).getY();
					directionType = DirectionType.UP_TO_DOWN;
				} else {
					// accumulativeLen-halfLen=y-points.get(i+1).getY();
					y = accumulativeLen - halfLen + points.get(i + 1).getY();
					directionType = DirectionType.DOWN_TO_UP;
				}
				x = points.get(i).getX();

			} else {// y坐标相等，
					// 前一个点在后一个点的左边
				if (points.get(i).getX() < points.get(i + 1).getX()) {
					// accumulativeLen-halfLen=points.get(i+1).getX()-x;
					x = halfLen - accumulativeLen + points.get(i + 1).getX();
					directionType = DirectionType.LEFT_TO_RIGHT;
				} else {
					// accumulativeLen-halfLen=x-points.get(i+1).getX();
					x = accumulativeLen - halfLen + points.get(i + 1).getX();
					directionType = DirectionType.RIGHT_TO_LEFT;
				}
				y = points.get(i).getY();
			}
			Point2D.Double midpoint = new Point2D.Double(x, y);

			// 取得中点
			bpmnEdge.setMidpoint(midpoint);
			// 取得中点所有直线的方向
			bpmnEdge.setDirection(directionType);

			bpmnEdges.add(bpmnEdge);

		}

		return bpmnEdges;

	}

	/**
	 * 设置BPMNShap的properties属性。properties中存放的是与特定类型的图形组件相关的图形组件的属性。
	 *
	 * @param component
	 *            流程定义的xml文档中与<code>bpmnShap</code>对应的节点元素
	 * @param bpmnShap
	 *            将对其进行操作的图形组件
	 * @return 对其进行properties属性设置后的图形组件
	 */
	private static BPMNShap setBPMNShapProperties(Element component, BPMNShap bpmnShap) {
		BPMNShapType type = bpmnShap.getType();
		Properties properties = bpmnShap.getProperties();
		if (properties == null) {
			properties = new Properties();
		}


		if (type == BPMNShapType.TASK || type == BPMNShapType.SCRIPT_TASK || type == BPMNShapType.SERVICE_TASK
				|| type == BPMNShapType.BUSINESS_RULE_TASK || type == BPMNShapType.MANUAL_TASK
				|| type == BPMNShapType.USER_TASK || type == BPMNShapType.CALL_ACTIVITY
				|| type == BPMNShapType.SUB_PROCESS) {
			Element multiInstanceLoopCharacteristics = (Element) component
					.selectSingleNode("./multiInstanceLoopCharacteristics");
			if (multiInstanceLoopCharacteristics != null) {
				String isSequential = multiInstanceLoopCharacteristics.attributeValue("isSequential");
				properties.put("isSequential", isSequential);
			}
		}

		if (type == BPMNShapType.ERROR_EVENT) {
			Element errorEventDefinition = (Element) component.selectSingleNode("errorEventDefinition");
			String errorRef = errorEventDefinition.attributeValue("errorRef");
			properties.put("errorRef", errorRef);
		}

		bpmnShap.setProperties(properties);
		return bpmnShap;
	}

	/**
	 * 根据流程定义的xml文档中节点图形节点元素，获取相应的图形节点的类型。
	 *
	 * @param component
	 *            流程定义的xml文档中节点图形节点元素
	 * @return 图形节点元素的类型
	 */
	public static BPMNShapType getBPMNShapType(Element component) {
 		BPMNShapType retVal = BPMNShapType.UNKNOW_TYPE;

		if (component.getName().equals(BPMNShapType.START_EVENT.getKey())) {
			retVal = BPMNShapType.START_EVENT;
		} else if (component.getName().equals(BPMNShapType.END_EVENT.getKey())) {
			Element errorEventDefinition = (Element) component.selectSingleNode("errorEventDefinition");
			if (errorEventDefinition == null) {
				retVal = BPMNShapType.END_EVENT;
			} else {
				retVal = BPMNShapType.ERROR_EVENT;
			}
		} else if (component.getName().equals( BPMNShapType.EXCLUSIVE_GATEWAY.getKey())) {
			retVal = BPMNShapType.EXCLUSIVE_GATEWAY;
		} else if (component.getName().equals( BPMNShapType.INCLUSIVE_GATEWAY.getKey())) {
			retVal = BPMNShapType.INCLUSIVE_GATEWAY;
		} else if (component.getName().equals(BPMNShapType.PARALLEL_GATEWAY.getKey())) {
			retVal = BPMNShapType.PARALLEL_GATEWAY;
		} else if (component.getName().equals(BPMNShapType.SCRIPT_TASK.getKey())) {
			retVal = BPMNShapType.SCRIPT_TASK;
		} else if (component.getName().equals(BPMNShapType.SERVICE_TASK.getKey())) {
			String sertype = BeanUtils.isNotEmpty(component.attribute("sertype"))? component.attribute("sertype").getStringValue():"";
			if(ServiceTaskType.MESSAGE.getKey().equals(sertype)){
				retVal = BPMNShapType.RECEIVE_TASK;
			}else if( ServiceTaskType.SCRIPT.getKey().equals(sertype)){
				retVal = BPMNShapType.SCRIPT_TASK;
			}else{
				retVal = BPMNShapType.SERVICE_TASK;
			}
		} else if (component.getName().equals( BPMNShapType.BUSINESS_RULE_TASK.getKey())) {
			retVal = BPMNShapType.BUSINESS_RULE_TASK;
		} else if (component.getName().equals( BPMNShapType.TASK.getKey())) {
			retVal = BPMNShapType.TASK;
		} else if (component.getName().equals(BPMNShapType.MANUAL_TASK.getKey())) {
			retVal = BPMNShapType.MANUAL_TASK;
		} else if (component.getName().equals(BPMNShapType.USER_TASK.getKey())) {
			retVal = BPMNShapType.USER_TASK;
		} else if (component.getName().equals(BPMNShapType.SEND_TASK.getKey())) {
			retVal = BPMNShapType.SEND_TASK;
		} else if (component.getName().equals(BPMNShapType.RECEIVE_TASK.getKey())) {
			retVal = BPMNShapType.RECEIVE_TASK;
		} else if (component.getName().equals(BPMNShapType.SUB_PROCESS.getKey())) {
			retVal = BPMNShapType.SUB_PROCESS;
		} else if (component.getName().equals(BPMNShapType.CALL_ACTIVITY.getKey())) {
			retVal = BPMNShapType.CALL_ACTIVITY;
		} else if (component.getName().equals(BPMNShapType.INTERMEDIATE_CATCH_EVENT.getKey())) {
			retVal = BPMNShapType.INTERMEDIATE_CATCH_EVENT;
		} else if (component.getName().equals(BPMNShapType.COMPLEX_GATEWAY.getKey())) {
			retVal = BPMNShapType.COMPLEX_GATEWAY;
		} else if (component.getName().equals(BPMNShapType.EVENT_BASED_GATEWAY.getKey())) {
			retVal = BPMNShapType.EVENT_BASED_GATEWAY;
		} else if (component.getName().equals(BPMNShapType.TRANSACTION.getKey())) {
			retVal = BPMNShapType.TRANSACTION;
		}else if (component.getName().equals(BPMNShapType.TEXT_ANNOTATION.getKey())) {
			retVal = BPMNShapType.TEXT_ANNOTATION;
		}else if (component.getName().equals("participant")) {
			String id = component.attributeValue("id");
			String processRef = component.attributeValue("processRef");
			Element root = component.getDocument().getRootElement();
			Element process = (Element) root.selectSingleNode("//*[@id='" + processRef + "']");
			if (process!=null&&process.element("laneSet") != null) {
				Element shap = (Element) root.selectSingleNode("//*[@bpmnElement='" + id + "']");
				String isHorizontal = shap.attributeValue("isHorizontal");
				if (isHorizontal != null && isHorizontal.equalsIgnoreCase("false")) {
					retVal = BPMNShapType.V_POOL;
				} else {
					retVal = BPMNShapType.H_POOL;
				}
			}else{// TODO 默认水平泳道
				retVal = BPMNShapType.H_POOL;
			}
		} else if (component.getName().equals("lane")) {
			String id = component.attributeValue("id");
			Element root = component.getDocument().getRootElement();
			Element shap = (Element) root.selectSingleNode("//*[@bpmnElement='" + id + "']");
			String isHorizontal = shap.attributeValue("isHorizontal");
			if (isHorizontal != null && isHorizontal.equalsIgnoreCase("false")) {
				retVal = BPMNShapType.V_LANE;
			} else {
				retVal = BPMNShapType.H_LANE;
			}
		}
		return retVal;
	}

}
