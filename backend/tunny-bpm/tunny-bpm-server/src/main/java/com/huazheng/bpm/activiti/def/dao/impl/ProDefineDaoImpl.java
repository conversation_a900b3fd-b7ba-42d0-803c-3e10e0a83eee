package com.huazheng.bpm.activiti.def.dao.impl;

import com.huazheng.bpm.activiti.def.dao.ProDefineDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.support.AbstractLobCreatingPreparedStatementCallback;
import org.springframework.jdbc.core.support.AbstractLobStreamingResultSetExtractor;
import org.springframework.jdbc.support.lob.DefaultLobHandler;
import org.springframework.jdbc.support.lob.LobCreator;
import org.springframework.jdbc.support.lob.LobHandler;
import org.springframework.stereotype.Repository;
import org.springframework.util.FileCopyUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;


/**
 * TODO 是否将事物提至上层
 * 流程定义实现
 * <pre>
 * 构建组：ibps-bpmn-act
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月16日-上午10:34:23
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Repository
public class ProDefineDaoImpl implements ProDefineDao {

	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	private JdbcTemplate jdbcTemplate;

	/**
	 * 取得流程定义的XML
	 *
	 * @param deployId
	 * @return
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public String getDefXmlByDeployId(String deployId) {
		String sql = "select a.* from ACT_GE_BYTEARRAY a where NAME_ LIKE '%bpmn20.xml' and DEPLOYMENT_ID_= ? ";
		final LobHandler lobHandler = new DefaultLobHandler();
		final ByteArrayOutputStream contentOs = new ByteArrayOutputStream();
		String defXml = null;
		try {
			jdbcTemplate.query(sql, new Object[] { deployId }, new AbstractLobStreamingResultSetExtractor() {
				public void streamData(ResultSet rs) throws SQLException, IOException {
					FileCopyUtils.copy(lobHandler.getBlobAsBinaryStream(rs, "BYTES_"), contentOs);
				}
			});
			defXml = new String(contentOs.toByteArray(), StandardCharsets.UTF_8);
		} catch (Exception ex) {
			logger.debug(ex.getMessage());
		}
		return defXml;
	}

	/**
	 * 把修改过的xml更新至回流程定义中
	 *
	 * @param deployId
	 * @param defXml
	 */
	public void writeDefXml(final String deployId, String defXml) {
		try {
			LobHandler lobHandler = new DefaultLobHandler();
			final byte[] btyesXml = defXml.getBytes(StandardCharsets.UTF_8);
			String sql = "update ACT_GE_BYTEARRAY set BYTES_=? where NAME_ LIKE '%bpmn20.xml' and DEPLOYMENT_ID_= ? ";
			jdbcTemplate.execute(sql, new AbstractLobCreatingPreparedStatementCallback(lobHandler) {
				@Override
				protected void setValues(PreparedStatement ps, LobCreator lobCreator) throws SQLException, DataAccessException {
					lobCreator.setBlobAsBytes(ps, 1, btyesXml);
					ps.setString(2, deployId);
				}
			});
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
}
