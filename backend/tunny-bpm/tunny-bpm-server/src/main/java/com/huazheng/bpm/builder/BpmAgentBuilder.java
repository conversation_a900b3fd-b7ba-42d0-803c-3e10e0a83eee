package com.huazheng.bpm.builder;


import com.huazheng.bpm.entity.bpm.BpmAgentPo;
import com.huazheng.bpm.model.define.IBpmDefine;
import com.huazheng.bpm.service.BpmDefineService;
import com.huazheng.bpm.service.IPartyEntityService;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.JacksonUtil;

import java.util.List;

/**
 * 流程代理对象构建工具
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年3月31日-上午9:14:55
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmAgentBuilder {

	/**
	 * 构建流程代理对象展示属性
	 *
	 * @param bpmAgentList
	 */
	public static void build(List<BpmAgentPo> bpmAgentList){
		if(BeanUtils.isEmpty(bpmAgentList)){
			return;
		}

		for(BpmAgentPo po : bpmAgentList){
			build(po);
		}
	}

	/**
	 * 构建流程代理对象展示属性
	 *
	 * @param bpmAgent
	 */
	public static void build(BpmAgentPo bpmAgent){
		if(BeanUtils.isEmpty(bpmAgent)){
			return;
		}

		IPartyEntityService entityService = AppUtil.getBean(IPartyEntityService.class);
		String data = null;

		if(BeanUtils.isNotEmpty(bpmAgent.getAgenterId())){
			data = entityService.getByIdJson(bpmAgent.getAgenterId());
			if(JacksonUtil.isJsonObject(data))bpmAgent.setAgenterName(JacksonUtil.getString(data, "name"));
		}

		if(BeanUtils.isNotEmpty(bpmAgent.getDelegatorId())){
			data = entityService.getByIdJson(bpmAgent.getDelegatorId());
			if(JacksonUtil.isJsonObject(data))bpmAgent.setDelegatorName(JacksonUtil.getString(data, "name"));
		}

		if(BeanUtils.isNotEmpty(bpmAgent.getProcDefKey())){
			BpmDefineService bpmDefineService = AppUtil.getBean(BpmDefineService.class);
			IBpmDefine bpmDefine = bpmDefineService.getBpmDefinitionByDefKey(bpmAgent.getProcDefKey(), false);
			bpmAgent.setProcDefId(bpmDefine.getDefId());
			bpmAgent.setProcDefName(bpmDefine.getName());
		}
	}
}
