/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.huazheng.bpm.activiti.engine.impl.bpmn.behavior;

import com.huazheng.bpm.activiti.engine.impl.context.Context;
import com.huazheng.bpm.activiti.engine.impl.entity.GatewayMetJoinEventModel;
import com.huazheng.bpm.activiti.engine.impl.entity.GatewayUnmetJoinEventModel;
import com.huazheng.bpm.activiti.engine.impl.event.GatewayMetJoinEvent;
import com.huazheng.bpm.activiti.engine.impl.event.GatewayUnmetJoinEvent;
import com.huazheng.bpm.activiti.engine.impl.persistence.entity.ExecutionEntity;
import com.huazheng.bpm.activiti.engine.impl.pvm.PvmActivity;
import com.huazheng.bpm.activiti.engine.impl.pvm.PvmTransition;
import com.huazheng.bpm.activiti.engine.impl.pvm.delegate.ActivityExecution;
import com.huazheng.bpm.util.core.AppUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


/**
 * Implementation of the Parallel Gateway/AND gateway as definined in the BPMN
 * 2.0 specification.
 *
 * The Parallel Gateway can be used for splitting a path of execution into
 * multiple paths of executions (AND-split/fork behavior), one for every
 * outgoing sequence flow.
 *
 * The Parallel Gateway can also be used for merging or joining paths of
 * execution (AND-join). In this case, on every incoming sequence flow an
 * execution needs to arrive, before leaving the Parallel Gateway (and
 * potentially then doing the fork behavior in case of multiple outgoing
 * sequence flow).
 *
 * Note that there is a slight difference to spec (p. 436): "The parallel
 * gateway is activated if there is at least one Token on each incoming sequence
 * flow." We only check the number of incoming tokens to the number of
 * sequenceflow. So if two tokens would arrive through the same sequence flow,
 * our implementation would activate the gateway.
 *
 * Note that a Parallel Gateway having one incoming and multiple ougoing
 * sequence flow, is the same as having multiple outgoing sequence flow on a
 * given activity. However, a parallel gateway does NOT check conditions on the
 * outgoing sequence flow.
 *
 * <AUTHOR> Barrez
 * <AUTHOR> Baeyens
 */
public class ParallelGatewayActivityBehavior extends GatewayActivityBehavior
{

	private static final long serialVersionUID = 1L;

	private static Logger log = LoggerFactory.getLogger(ParallelGatewayActivityBehavior.class);

	public void execute(ActivityExecution execution) throws Exception
	{

		// Join
		PvmActivity activity = execution.getActivity();
		List<PvmTransition> outgoingTransitions = execution.getActivity().getOutgoingTransitions();
		execution.inactivate();
		lockConcurrentRoot(execution);

		List<ActivityExecution> joinedExecutions = execution.findInactiveConcurrentExecutions(activity);
		int nbrOfExecutionsToJoin = execution.getActivity().getIncomingTransitions().size();
		int nbrOfExecutionsJoined = joinedExecutions.size();
		Context.getCommandContext().getHistoryManager().recordActivityEnd((ExecutionEntity) execution);
		if (nbrOfExecutionsJoined == nbrOfExecutionsToJoin)
		{
			// 记录活动实例||nbrOfExecutionsJoined==1
			// Fork
			if (log.isDebugEnabled())
			{
				log.debug("parallel gateway '"+activity.getId()+"' activates: "+nbrOfExecutionsJoined+" of "+nbrOfExecutionsToJoin+" joined");
			}

			// 聚合条件满足时记录执行信息--自定义扩展部分
			GatewayMetJoinEventModel model=new GatewayMetJoinEventModel();
			model.activity=activity;
			model.activityExecution=execution;
			model.noteType="parallelGateway";
			GatewayMetJoinEvent event=new  GatewayMetJoinEvent(model);
			AppUtil.publishEvent(event);

			execution.takeAll(outgoingTransitions, joinedExecutions);
		}
		else
		{
			// 聚会条件未满足时记录执行信息--自定义扩展部分
			GatewayUnmetJoinEventModel model=new GatewayUnmetJoinEventModel();
			model.activity=activity;
			model.activityExecution=execution;
			model.noteType="parallelGateway";
			GatewayUnmetJoinEvent event=new  GatewayUnmetJoinEvent(model);
			AppUtil.publishEvent(event);
			if (log.isDebugEnabled())
				log.debug("parallel gateway '"+activity.getId()+"' does not activate: "
						+nbrOfExecutionsJoined+" of "+nbrOfExecutionsToJoin+" joined");
		}
	}

}
