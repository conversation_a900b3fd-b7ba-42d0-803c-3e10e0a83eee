package com.huazheng.bpm.config;

import com.huazheng.tunny.common.security.component.ResourceAuthExceptionEntryPoint;
import com.huazheng.tunny.common.security.component.TunnyAccessDeniedHandler;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;


@Configuration
@EnableResourceServer
@AllArgsConstructor
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class ResourceServerConfigurer extends ResourceServerConfigurerAdapter {
    private final TunnyAccessDeniedHandler tunnyAccessDeniedHandler;
    private final ResourceAuthExceptionEntryPoint resourceAuthExceptionEntryPoint;

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
                .antMatchers(
                        "/v2/api-docs"
//                        ,"/info"
                        //JSP页面静态资源白名单-开始
                        ,"/commons/**"
                        ,"/js/**"
                        ,"/styles/**"
                        //JSP页面静态资源白名单-结束

                        //JSP页面白名单-开始（因其通过get直接访问url地址，没走ajax，加不了全局hearder）
                        ,"/platform/bpmn/bpmDefine/list"
                        ,"/platform/bpmn/bpmDefine/setting"
                        ,"/platform/bpmn/bpmDefine/export"
                        ,"/platform/bpmn/bpmDefine/import"
                        ,"/platform/bpmn/bpmDefine/instDataPage"
                        ,"/platform/bpmn/bpmDefine/changeAssignPage"
                        ,"/platform/bpmn/bpmModeler/editor"
                        ,"/platform/bpmn/bpmNodeDef/person"
                        ,"/platform/bpmn/bpmNodeDef/conditionEdit"
                        ,"/platform/bpmn/bpmNodeDef/nodeDef/cusersDialog"
                        ,"/platform/bpmn/bpmNodeDef/nodeDef/sameNodeDialog"
                        ,"/platform/bpmn/bpmNodeDef/nodeDef/posDialog"
                        ,"/platform/bpmn/bpmNodeDef/nodeDef/roleDialog"
                        ,"/platform/bpmn/bpmNodeDef/settingPrivileges"
                        ,"/platform/bpmn/bpmNodeDef/partyPosDialog"
                        ,"/platform/bpmn/bpmNodeDef/partyRoleDialog"
                        ,"/platform/bpmn/instance/bpmInst/flowImage"
                        ,"/platform/bpmn/image/gen"
                        ,"/platform/bpmn/bpmUserCondition/preview"
                        ,"/platform/bpmn/bpmNodeDef/ruleEdit.htm"
                        ,"/platform/bpmn/bpmDefine/getXml.htm"
                        ,"/platform/cat/type/edit"
                        ,"/platform/cat/type/sortList"
                        ,"/platform/bpmn/bpmDefine/setCategoryDialog"
                        //JSP页面白名单-结束

                        //暴露所有接口及页面
//                        ,"/platform/**"
//                        ,"/**"
                ).permitAll()
                .anyRequest().authenticated()
                .and().csrf().disable();
        // 因spring Boot采取的java config，在配置spring security的位置添加下列代码，禁用frameOptions
        http.headers().frameOptions().disable();
    }

    /**
     * why add  resourceId
     * https://stackoverflow.com/questions/28703847/how-do-you-set-a-resource-id-for-a-token
     *
     * @param resources
     * @throws Exception
     */
    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        resources.authenticationEntryPoint(resourceAuthExceptionEntryPoint)
                .accessDeniedHandler(tunnyAccessDeniedHandler);
    }
}
