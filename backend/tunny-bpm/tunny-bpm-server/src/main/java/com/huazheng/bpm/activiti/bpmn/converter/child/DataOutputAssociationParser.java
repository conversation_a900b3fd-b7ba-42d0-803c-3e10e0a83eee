/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.huazheng.bpm.activiti.bpmn.converter.child;

import javax.xml.stream.XMLStreamReader;

import com.huazheng.bpm.activiti.bpmn.converter.util.BpmnXMLUtil;
import org.activiti.bpmn.model.Activity;
import org.activiti.bpmn.model.BaseElement;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.DataAssociation;
import org.activiti.bpmn.model.SendTask;
import org.activiti.bpmn.model.ServiceTask;

/**
 * <AUTHOR>
 */
public class DataOutputAssociationParser extends BaseChildElementParser {

  public String getElementName() {
    return ELEMENT_INPUT_ASSOCIATION;
  }

  public void parseChildElement(XMLStreamReader xtr, BaseElement parentElement, BpmnModel model) throws Exception {

    if (parentElement instanceof ServiceTask == false && parentElement instanceof SendTask == false) return;

    DataAssociation dataAssociation = new DataAssociation();
    BpmnXMLUtil.addXMLLocation(dataAssociation, xtr);
    DataAssociationParser.parseDataAssociation(dataAssociation, getElementName(), xtr);

    ((Activity) parentElement).getDataInputAssociations().add(dataAssociation);
  }
}
