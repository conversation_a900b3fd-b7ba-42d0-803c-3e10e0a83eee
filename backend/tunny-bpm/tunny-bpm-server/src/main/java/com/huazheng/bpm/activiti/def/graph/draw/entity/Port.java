package com.huazheng.bpm.activiti.def.graph.draw.entity;

/**
 * 程序设计器 表示节点与连接之间的端口。
 *
 * <pre>
 * 构建组：ibps-bpmn-activiti
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月29日-下午3:16:20
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class Port {
	//端口类型
	private PortType portType;
	//端口在节点中的x坐标，(x,y)=(0,0)表左上角，(x,y)=(1,1)表右下角
	private double x;
	//端口在节点中的y坐标
	private double y;
	//垂直位移
	private double verticalOffset;
	//垂直位移
	private double horizontalOffset;
	private String nodePartReference;
	private boolean clipOnShape;

	public Port() {
	}

	public Port(PortType portType,double x,double y,double horizontalOffset, double verticalOffset,String nodePartReference,boolean clipOnShape){
		this.portType=portType;
		this.x=x;
		this.y=y;
		this.verticalOffset=verticalOffset;
		this.horizontalOffset=horizontalOffset;
		this.nodePartReference=nodePartReference;
		this.clipOnShape=clipOnShape;
	}

	public PortType getPortType() {
		return portType;
	}

	public void setPortType(PortType portType) {
		this.portType = portType;
	}

	public double getX() {
		return x;
	}

	public void setX(double x) {
		this.x = x;
	}

	public double getY() {
		return y;
	}

	public void setY(double y) {
		this.y = y;
	}

	public double getVerticalOffset() {
		return verticalOffset;
	}

	public void setVerticalOffset(double verticalOffset) {
		this.verticalOffset = verticalOffset;
	}

	public double getHorizontalOffset() {
		return horizontalOffset;
	}

	public void setHorizontalOffset(double horizontalOffset) {
		this.horizontalOffset = horizontalOffset;
	}

	public String getNodePartReference() {
		return nodePartReference;
	}

	public void setNodePartReference(String nodePartReference) {
		this.nodePartReference = nodePartReference;
	}

	public boolean isClipOnShape() {
		return clipOnShape;
	}

	public void setClipOnShape(boolean clipOnShape) {
		this.clipOnShape = clipOnShape;
	}

}
