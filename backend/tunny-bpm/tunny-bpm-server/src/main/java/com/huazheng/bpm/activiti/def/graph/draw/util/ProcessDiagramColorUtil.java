package com.huazheng.bpm.activiti.def.graph.draw.util;


import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.string.StringUtil;

import java.awt.*;

/**
 * 流程图帮助类。
 *
 * <pre>
 * 构建组：ibps-bpmn-activiti
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月29日-下午3:11:45
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class ProcessDiagramColorUtil {

	public static String PROCESS_DIAGRAM_KEY = "processDiagram";

	/**
	 * 获取流程图的颜色
	 *
	 * @param key
	 * @param defaultValue
	 * @return
	 */
	public static Color getColor(String key, String defaultValue) {
		String color = getColorString(key, defaultValue);
		return Color.decode(color);
	}

	/**
	 * 获取流程图的颜色
	 *
	 * @param key
	 * @param defaultValue
	 * @return
	 */
	public static String getColorString(String key, String defaultValue) {
		String color = AppUtil.getProperty(PROCESS_DIAGRAM_KEY + "." + key);
		if (StringUtil.isEmpty(color))
			color = defaultValue;
		return color;
	}
}
