package com.huazheng.bpm.controller;

import com.huazheng.bpm.entity.bpm.BpmInstHisPo;
import com.huazheng.bpm.entity.bpm.BpmInstPo;
import com.huazheng.bpm.model.def.BpmDefLayout;
import com.huazheng.bpm.model.image.BpmProcessStatusColor;
import com.huazheng.bpm.model.task.IBpmTaskApproval;
import com.huazheng.bpm.repository.BpmInstHisRepository;
import com.huazheng.bpm.repository.BpmInstRepository;
import com.huazheng.bpm.service.*;
import com.huazheng.bpm.util.base.*;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.string.StringUtil;
import com.huazheng.bpm.util.web.RequestUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 流程实例 控制类
 *
 * <pre>
 *
 * 开发公司：广州流辰信息技术有限公司
 * 开发人员：huangchunyan
 * 邮箱地址：<EMAIL>
 * 创建时间：2017-01-06 09:26:18
 * </pre>
 */
@Controller
@RequestMapping("/platform/bpmn/instance/bpmInst/")
public class BpmInstController extends GenericController {
	@Resource
	private BpmInstRepository bpmInstRepository;
	@Resource
	private BpmApprovalService bpmApprovalService;
	@Resource
	private DiagramService diagramService;
	@Resource
	private BpmInstHisRepository bpmInstHisRepository;

	/**
	 * 流程审批历史-会签任务
	 */
	@RequestMapping("flowHistorySign")
	public ModelAndView flowHistorySign(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String batch = RequestUtil.getString(request, "batch");
		List<IBpmTaskApproval> bpmTaskOpinions = null;
		if (StringUtil.isNotEmpty(batch)) {
			bpmTaskOpinions = bpmApprovalService.setAuditorInfo(bpmApprovalService.findSignApprovalHis(batch));
		}
		return getAutoView().addObject("bpmTaskOpinions", bpmTaskOpinions);
	}

	/**
	 * 流程审批历史-外部子流程
	 */
	@RequestMapping("flowHistoryCallSub")
	public ModelAndView flowHistoryCallSub(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String procInstanceId = RequestUtil.getString(request, "instId");
		String nodeId = RequestUtil.getString(request, "nodeId");
		List<List<IBpmTaskApproval>> bpmTaskOpinions = bpmApprovalService.findCallSubApprovalHis(procInstanceId, nodeId);
		return getAutoView().addObject("bpmTaskOpinions", bpmTaskOpinions);
	}



	/**
	 * 流程图
	 *
	 * @param
	 * @param
	 * @return
	 * @throws Exception
	 */
    @RequestMapping("flowImage")
	public ModelAndView flowImage(@RequestParam("id") String id,@RequestParam(value="tkey",required = false) String tkey) throws Exception {
		BpmInstPo bpmInstPo = null;
		BpmInstHisPo bpmInstHisPo = null;
		BpmDefLayout bpmDefLayout = null;
		if (StringUtil.isNotEmpty(id)) {
			bpmInstPo = bpmInstRepository.get(id);
			if(BeanUtils.isEmpty(bpmInstPo))
				bpmInstHisPo = bpmInstHisRepository.get(id);
			String defId = BeanUtils.isNotEmpty(bpmInstPo)?bpmInstPo.getProcDefId():bpmInstHisPo.getProcDefId();
			// 流程图layout
			bpmDefLayout = diagramService.getLayoutByDefId(defId);
		}
		List<BpmProcessStatusColor> list  = FlowStatusColorUtil.getProcessStatusColorList();

		return getAutoView().addObject("bpmProcInst", BeanUtils.isNotEmpty(bpmInstPo)?bpmInstPo:bpmInstHisPo)
							.addObject("instId", id)
							.addObject("bpmDefLayout", bpmDefLayout)
							.addObject("statusColorList",list)
							.addObject("tkey", tkey);
	}
}
