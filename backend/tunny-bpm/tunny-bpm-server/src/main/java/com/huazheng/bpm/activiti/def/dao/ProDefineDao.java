package com.huazheng.bpm.activiti.def.dao;

/**
 * 流程定义接口
 * <pre>
 * 构建组：ibps-bpmn-act
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2016年12月16日-上午10:33:22
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public interface ProDefineDao {

	/**
	 * 取得流程定义的XML
	 * @param deployId
	 * @return
	 * String
	 * @exception
	 * @since  1.0.0
	 */
    String getDefXmlByDeployId(String deployId);

	/**
	 * 更新流程定义的
	 * @param deployId
	 * @param defXml
	 * void
	 * @exception
	 * @since  1.0.0
	 */
    void writeDefXml(String deployId, String defXml);
}
