package com.huazheng.bpm.builder;


import com.huazheng.bpm.entity.bpm.BpmTaskAssignPo;
import com.huazheng.bpm.entity.bpm.BpmTaskChangePo;
import com.huazheng.bpm.entity.bpm.BpmTaskPo;
import com.huazheng.bpm.entity.constant.TaskType;
import com.huazheng.bpm.model.base.BpmIdentity;
import com.huazheng.bpm.model.define.BpmDefineAttributes;
import com.huazheng.bpm.model.define.IBpmProcDefine;
import com.huazheng.bpm.model.define.IBpmProcExtendDefine;
import com.huazheng.bpm.repository.BpmTaskAssignRepository;
import com.huazheng.bpm.repository.BpmTaskChangeRepository;
import com.huazheng.bpm.repository.BpmTaskReminderRecRepository;
import com.huazheng.bpm.service.IBpmDefineReader;
import com.huazheng.bpm.service.IPartyEntityService;
import com.huazheng.bpm.service.IPartyGroupService;
import com.huazheng.bpm.util.base.ReminderJob;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.JacksonUtil;
import com.huazheng.bpm.util.core.StringPool;
import com.huazheng.bpm.util.string.StringUtil;
import com.huazheng.bpm.util.web.ContextUtil;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 构建流程任务
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年3月30日-下午2:53:52
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmTaskBuilder {

	/**
	 * 构建流程任务
	 *
	 * @param list
	 */
	public static void build(List<BpmTaskPo> list){
		if(BeanUtils.isEmpty(list)){
			return;
		}

		for (BpmTaskPo po : list) {
			build(po);
		}
	}

	/**
	 * 构建流程任务
	 *
	 * @param task
	 */
	private static void build(BpmTaskPo task){
		if(BeanUtils.isEmpty(task)){
			return;
		}

		IBpmDefineReader bmpDefineReader = AppUtil.getBean(IBpmDefineReader.class);
		IBpmProcDefine<IBpmProcExtendDefine> bpmProcDefine = bmpDefineReader.getBpmProcDefine(task.getProcDefId());
		IBpmProcExtendDefine ext = bpmProcDefine.getBpmProcExtendDefine();
		BpmDefineAttributes extAttr = ext.getExtendAttributes();
		if(BeanUtils.isNotEmpty(extAttr) && extAttr.isAllowTransTo()){
			BpmTaskChangeRepository bpmTaskChangeRepository = AppUtil.getBean(BpmTaskChangeRepository.class);
			List<BpmTaskChangePo> chgList = bpmTaskChangeRepository.findByTask(task.getId(), BpmTaskChangePo.CHANGE_STATUS_RUNNING);
			if(BeanUtils.isNotEmpty(chgList)){
				task.setAllowShfit(StringPool.N);
			}
		}else{
			task.setAllowShfit(StringPool.N);
		}

		IPartyEntityService entityService = AppUtil.getBean(IPartyEntityService.class);
		BpmTaskReminderRecRepository bpmTaskReminderRecRepository = AppUtil.getBean(BpmTaskReminderRecRepository.class);
		BpmTaskAssignRepository bpmTaskAssignRepository = AppUtil.getBean(BpmTaskAssignRepository.class);
		String employee = null;
		List<BpmTaskAssignPo> assigns = bpmTaskAssignRepository.getByTask(task.getId());
		if(BeanUtils.isNotEmpty(assigns)){
			StringBuilder ownerName = new StringBuilder();
			for(BpmTaskAssignPo assign : assigns){
				//后续需要调整
				//employee = entityService.getByIdJson(assign.getExecutor());
				employee = "测试人员";
				if(StringUtil.isNotEmpty(employee)){
					if(JacksonUtil.isJsonObject(employee)) ownerName.append(JacksonUtil.getString(employee, "name")).append(",");
				}else{
					//当前为用户组
					//IPartyGroupService partyGroupService = AppUtil.getBean(IPartyGroupService.class);
//					PartyGroupPo partyGroup=PartyGroupPo.fromJsonString(partyGroupService.loadCascade(assign.getExecutor()));
//					List<PartyUserGroupPo> partyUserGroupPos = partyGroup.getPartyUserGroupPoList();
//					if(JacksonUtil.isNotEmpty(partyGroup)){
//						for (PartyUserGroupPo partyUserGroupPo : partyUserGroupPos) {									//循环list
//							String puName=partyUserGroupPo.getUserName();												//获取用户组中的员工信息
//							ownerName.append(puName).append(",");														//加入可变字符串
//						}
//					}
				}
			}
			if(ownerName.length()>0){
				ownerName.setLength(ownerName.length()-1);
			}
			task.setOwnerName(ownerName.toString());
		}

		String status = null;
		BpmTaskChangeRepository bpmTaskChangeRepository = AppUtil.getBean(BpmTaskChangeRepository.class);
		status = bpmTaskChangeRepository.getStatus(task.getId(), ContextUtil.getCurrentUserId(), BpmTaskChangePo.CHANGE_STATUS_RUNNING);

		if(BpmTaskChangePo.CHANGE_ASSIGNEE.equals(status)){
			status = TaskType.AGENT.getKey();
		}else if(BpmTaskChangePo.CHANGE_SHIFT.equals(status)){
			status = TaskType.DELIVERTO.getKey();
		}else{
			status = TaskType.NORMAL.getKey();
		}
		task.setStatus(status);
		//后续需要调整
		// 催办次数查询
		int reminderTimes = bpmTaskReminderRecRepository
				.getAmountByUserTaskId(task.getId(), "A0003149", ReminderJob.REMIND);
		task.setRemindTimes(reminderTimes);
	}


	/**
	 * 流程代办任务
	 *
	 * @param list
	 */
	public static void buildto(List<BpmTaskPo> list){
		if(BeanUtils.isEmpty(list)){
			return;
		}

		for (BpmTaskPo po : list) {
			buildto(po);
		}
	}

	/**
	 * 流程代办任务
	 *
	 * @param task
	 */
	private static void buildto(BpmTaskPo task){
		if(BeanUtils.isEmpty(task)){
			return;
		}

		IBpmDefineReader bmpDefineReader = AppUtil.getBean(IBpmDefineReader.class);
		IBpmProcDefine<IBpmProcExtendDefine> bpmProcDefine = bmpDefineReader.getBpmProcDefine(task.getProcDefId());		//流程id获取
		IBpmProcExtendDefine ext = bpmProcDefine.getBpmProcExtendDefine();
		BpmDefineAttributes extAttr = ext.getExtendAttributes();
		if(BeanUtils.isNotEmpty(extAttr) && extAttr.isAllowTransTo()){
			BpmTaskChangeRepository bpmTaskChangeRepository = AppUtil.getBean(BpmTaskChangeRepository.class);
			List<BpmTaskChangePo> chgList = bpmTaskChangeRepository.findByTask(task.getId(), BpmTaskChangePo.CHANGE_STATUS_RUNNING);
			if(BeanUtils.isNotEmpty(chgList)){
				task.setAllowShfit(StringPool.N);
			}
		}else{
			task.setAllowShfit(StringPool.N);
		}

		IPartyEntityService entityService = AppUtil.getBean(IPartyEntityService.class);									//注入service
		BpmTaskReminderRecRepository bpmTaskReminderRecRepository = AppUtil.getBean(BpmTaskReminderRecRepository.class);//注入仓库类
		BpmTaskAssignRepository bpmTaskAssignRepository = AppUtil.getBean(BpmTaskAssignRepository.class);				//注入任务候选人仓库
		String employee = null;
		List<BpmTaskAssignPo> assigns = bpmTaskAssignRepository.getByTask(task.getId());								//根据任务获取候选人列表
		if(BeanUtils.isNotEmpty(assigns)){																				//候选人不为空是
			StringBuilder ownerName = new StringBuilder();
			for(BpmTaskAssignPo assign : assigns){
				employee = entityService.getByIdJson(assign.getExecutor());									//根据执行人id获取职员信息返回json
				if(JacksonUtil.isJsonObject(employee)) ownerName.append(JacksonUtil.getString(employee, "name")).append(",");  //循环ownerName取得加上逗号
			}
			if(ownerName.length()>0){																		//如果是最后的就减去逗号
				ownerName.setLength(ownerName.length()-1);
			}
			task.setOwnerName(ownerName.toString());														//OwnerName存入
		}

		String status = null;
		BpmTaskChangeRepository bpmTaskChangeRepository = AppUtil.getBean(BpmTaskChangeRepository.class);	//流程变更接口仓库
		status = bpmTaskChangeRepository.getStatus(task.getId(), ContextUtil.getCurrentUserId(), BpmTaskChangePo.CHANGE_STATUS_RUNNING);

		/**
		 * 新增判断代办人方法
		 *
		 * */
		//获取代办人信息
		List<Map<String, String>> lists=bpmTaskChangeRepository.findUserByTask(task.getId(), BpmTaskChangePo.CHANGE_STATUS_RUNNING);
		if(BeanUtils.isNotEmpty(lists)){													//代办人不为空
			String mid=null;
			for (Map<String, String> map : lists) {
				for (Entry<String, String> m :map.entrySet())  {
					 System.out.println(m.getKey()+"\t"+m.getValue());
					 if(m.getKey()=="id"){
						 mid=m.getValue();													//获取代办人id
					 }
		        }
			}
			String TaskChangeUser = entityService.getByIdJson(mid);							//代办人id获取职员信息
			StringBuilder TaskChangeUserName = new StringBuilder();

			TaskChangeUserName.append(JacksonUtil.getString(TaskChangeUser, "name")).append(",");					//加入候选人
			if(TaskChangeUserName.length()>0){																		//如果是最后的就减去逗号
				TaskChangeUserName.setLength(TaskChangeUserName.length()-1);
			}
			task.setOwnerName(TaskChangeUserName.toString());
		}//代办

		/**
		 *用户组候选人获取
		 * */
		for (BpmTaskAssignPo assign : assigns) {
			// 执行人id（用户组）
			if (assign.getType().equals(BpmIdentity.TYPE_GROUP)) {
				String id = assign.getExecutor();
				IPartyGroupService partyGroupService = AppUtil.getBean(IPartyGroupService.class); 		  // 注入service
//				PartyGroupPo partyGroup = PartyGroupPo.fromJsonString(partyGroupService.loadCascade(id)); // 根据用户组id获取用户组信息
//				List<PartyUserGroupPo> partyUserGroupPos = partyGroup.getPartyUserGroupPoList();          // 获取用户组里面的员工信息
//				StringBuilder TaskChangeUserName = new StringBuilder();
//				for (PartyUserGroupPo partyUserGroupPo : partyUserGroupPos) {           				// 循环list
//					String puName = partyUserGroupPo.getUserName(); 									// 获取用户组中的员工信息
//					TaskChangeUserName.append(puName).append(","); 										// 加入可变字符串
//				}
//				if (TaskChangeUserName.length() > 0) { 													// 如果是最后的就减去逗号
//					TaskChangeUserName.setLength(TaskChangeUserName.length() - 1);
//				}
//				task.setOwnerName(TaskChangeUserName.toString()); // 写入ownername
			}
		} // 候选人

		if(BpmTaskChangePo.CHANGE_ASSIGNEE.equals(status)){
			status = TaskType.AGENT.getKey();
		}else if(BpmTaskChangePo.CHANGE_SHIFT.equals(status)){
			status = TaskType.DELIVERTO.getKey();
		}else{
			status = TaskType.NORMAL.getKey();
		}
		task.setStatus(status);

		// 催办次数查询
		int reminderTimes = bpmTaskReminderRecRepository
				.getAmountByUserTaskId(task.getId(), ContextUtil.getCurrentUserId(), ReminderJob.REMIND);
		task.setRemindTimes(reminderTimes);
	}
}
