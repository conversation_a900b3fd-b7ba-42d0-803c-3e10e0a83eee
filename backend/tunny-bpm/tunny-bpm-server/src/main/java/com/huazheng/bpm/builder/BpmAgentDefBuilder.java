package com.huazheng.bpm.builder;

import com.huazheng.bpm.entity.bpm.BpmAgentDefPo;
import com.huazheng.bpm.model.define.IBpmDefine;
import com.huazheng.bpm.service.BpmDefineService;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;

import java.util.List;

/**
 * 流程代理定义对象构建工具
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年3月31日-上午10:35:01
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmAgentDefBuilder {

	/**
	 * 构建流程代理定义对象展示属性
	 *
	 * @param bpmAgentList
	 */
	public static void build(List<BpmAgentDefPo> bpmAgentDefList){
		if(BeanUtils.isEmpty(bpmAgentDefList)){
			return;
		}

		for(BpmAgentDefPo po : bpmAgentDefList){
			build(po);
		}
	}

	/**
	 * 构建流程代理定义对象展示属性
	 *
	 * @param bpmAgent
	 */
	public static void build(BpmAgentDefPo bpmAgentDef){
		if(BeanUtils.isEmpty(bpmAgentDef)){
			return;
		}

		BpmDefineService bpmDefineService = AppUtil.getBean(BpmDefineService.class);
		IBpmDefine bpmDefine = bpmDefineService.getBpmDefinitionByDefKey(bpmAgentDef.getProcDefKey(), false);
		bpmAgentDef.setProcDefName(bpmDefine.getName());
	}
}
