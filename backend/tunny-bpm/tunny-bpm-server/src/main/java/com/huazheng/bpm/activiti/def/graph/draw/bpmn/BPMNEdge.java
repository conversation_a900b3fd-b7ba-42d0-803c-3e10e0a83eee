package com.huazheng.bpm.activiti.def.graph.draw.bpmn;

import java.awt.geom.Point2D;
import java.util.List;

/**
 * 连线。
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-act
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年3月23日-上午10:17:27
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BPMNEdge {
	private List<Point2D.Double> points;
	// 名称
	private String name;
	private Point2D.Double midpoint;
	// 方向类型
	private com.huazheng.bpm.activiti.def.graph.draw.bpmn.DirectionType direction;
	// 源连线
	private String sourceRef;
	// 目标连线
	private String targetRef;
	// 连线类型
	private com.huazheng.bpm.activiti.def.graph.draw.bpmn.FlowType flowType;

	private float labelX = 0;
	private float labelY = 0;
	private float labelWidth = 0;
	private float labelHeight = 0;

	public List<Point2D.Double> getPoints() {
		return points;
	}

	public void setPoints(List<Point2D.Double> points) {
		this.points = points;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Point2D.Double getMidpoint() {
		return midpoint;
	}

	public void setMidpoint(Point2D.Double midpoint) {
		this.midpoint = midpoint;
	}

	public com.huazheng.bpm.activiti.def.graph.draw.bpmn.DirectionType getDirection() {
		return direction;
	}

	public void setDirection(com.huazheng.bpm.activiti.def.graph.draw.bpmn.DirectionType direction) {
		this.direction = direction;
	}

	public String getSourceRef() {
		return sourceRef;
	}

	public void setSourceRef(String sourceRef) {
		this.sourceRef = sourceRef;
	}

	public String getTargetRef() {
		return targetRef;
	}

	public void setTargetRef(String targetRef) {
		this.targetRef = targetRef;
	}

	public com.huazheng.bpm.activiti.def.graph.draw.bpmn.FlowType getFlowType() {
		return flowType;
	}

	public void setFlowType(com.huazheng.bpm.activiti.def.graph.draw.bpmn.FlowType flowType) {
		this.flowType = flowType;
	}

	public float getLabelX() {
		return labelX;
	}

	public void setLabelX(float labelX) {
		this.labelX = labelX;
	}

	public float getLabelY() {
		return labelY;
	}

	public void setLabelY(float labelY) {
		this.labelY = labelY;
	}

	public float getLabelWidth() {
		return labelWidth;
	}

	public void setLabelWidth(float labelWidth) {
		this.labelWidth = labelWidth;
	}

	public float getLabelHeight() {
		return labelHeight;
	}

	public void setLabelHeight(float labelHeight) {
		this.labelHeight = labelHeight;
	}

	@Override
	public String toString() {
		String str = "";
		for (Point2D.Double point : points) {
			str = str + point.getX() + ":" + point.getY() + "  ";
		}

		return "BPMNEdge [points=" + str + ", name=" + name + " <midpoint=" + midpoint.getX() + ":" + midpoint.getY()
				+ ">]";
	}

}
