package com.huazheng.bpm.activiti.def.graph.draw.activiti;

import com.huazheng.bpm.activiti.def.graph.draw.bpmn.BPMNEdge;
import com.huazheng.bpm.activiti.def.graph.draw.bpmn.DirectionType;
import com.huazheng.bpm.activiti.def.graph.draw.bpmn.FlowType;
import com.huazheng.bpm.activiti.def.graph.draw.entity.ImageType;
import com.huazheng.bpm.activiti.def.graph.draw.util.ProcessDiagramColorUtil;
import com.huazheng.bpm.activiti.engine.ActivitiException;
import com.huazheng.bpm.model.base.FlowImageStyle;
import org.activiti.engine.impl.util.IoUtil;
import org.activiti.engine.impl.util.ReflectUtil;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;


/**
 * 流程图核心。
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-act
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年3月7日-上午10:03:14
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class ProcessDiagramCanvas {
	protected static final Logger LOGGER = Logger.getLogger(ProcessDiagramCanvas.class.getName());
	protected static final int ARROW_WIDTH = 5;
	protected static final int CONDITIONAL_INDICATOR_WIDTH = 16;
	protected static final int MARKER_WIDTH = 12;
	// 默认
	protected static Color DEFAULT_COLOR = ProcessDiagramColorUtil.getColor("default", "#000000");
	// 开始节点
	protected static Color START_COLOR = ProcessDiagramColorUtil.getColor("start", "#00000");
	// 结束节点
	protected static Color END_COLOR = ProcessDiagramColorUtil.getColor("end", "#00000");

	// 水平泳道边框颜色
	protected static Color POOL_BOUNDARY_COLOR;
	// 竖直泳道边框颜色
	protected static Color LANE_BOUNDARY_COLOR;
	// 水平泳道背景颜色
	protected static Color POOL_BACKGROUP_COLOR;
	// 竖直泳道边框颜色
	protected static Color LANE_BACKGROUP_COLOR;
	// 用户任务背景颜色
	protected static Color TASK_COLOR = ProcessDiagramColorUtil.getColor("task", "#f2f7fd");
	// 事件边框颜色
	protected static Color EVENT_BOUNDARY_COLOR = ProcessDiagramColorUtil.getColor("eventBoundary", "#FFFFFF");
	// 分支、条件背景颜色
	protected static Color CONDITIONAL_INDICATOR_COLOR = ProcessDiagramColorUtil.getColor("conditional", "#FFFFFF");
	// 高亮背景颜色
	protected static Color HIGHLIGHT_COLOR = Color.RED;
	// 连线的颜色
	protected static Color LINE_COLOR = Color.black;

	protected static Stroke THIN_TASK_BORDER_STROKE = new BasicStroke(1.0F);
	protected static Stroke THICK2_TASK_BORDER_STROKE = new BasicStroke(2.0F);
	protected static Stroke THICK3_TASK_BORDER_STROKE = new BasicStroke(3.0F);
	// 网关
	protected static Stroke GATEWAY_TYPE_STROKE = new BasicStroke(3.0F);
	// 开始
	protected static Stroke START_EVENT_STROKE = new BasicStroke(1.0F);
	// 结束
	protected static Stroke END_EVENT_STROKE = new BasicStroke(3.5F);
	// 多实例
	protected static Stroke MULTI_INSTANCE_STROKE = new BasicStroke(1.3F);

	protected static Stroke LABEL_STROKE = new BasicStroke(1.0F);
	//虚线
	protected static Stroke DASHED_STROKE= new BasicStroke(2.5f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_ROUND, 3.5f,
			new float[] { 2, 2, }, 0f);
	// 图标大小
	protected static int ICON_SIZE = 16;
	// 流程的图标
	protected static Image USERTASK_IMAGE; // 用户任务
	protected static Image SCRIPTTASK_IMAGE; // 脚本任务
	protected static Image SERVICETASK_IMAGE; // 服务任务
	protected static Image RECEIVETASK_IMAGE; // 接收任务（消息任务）
	protected static Image SENDTASK_IMAGE; // 发送任务
	protected static Image MANUALTASK_IMAGE; // 人工任务
	protected static Image TIMER_IMAGE; // 定时
	protected static Image ERROR_THROW_IMAGE;
	protected static Image ERROR_CATCH_IMAGE;
	protected static Image CALLACTIVITY_IMAGE;

	protected static Image PARALLEL_IMAGE; // 并行
	protected static Image SEQUENTIAL_IMAGE; // 串行

	protected int canvasWidth = -1;
	protected int canvasHeight = -1;
	protected int minX = -1;
	protected int minY = -1;
	protected BufferedImage processDiagram;
	protected Graphics2D g;
	protected FontMetrics fontMetrics;
	protected boolean closed;

	protected static Map<Short, Color> colorsMap = new HashMap<Short, Color>();

	static {
		try {
			USERTASK_IMAGE = ImageIO.read(ReflectUtil.getResourceAsStream("image/user.png"));
			SCRIPTTASK_IMAGE = ImageIO
					.read(ReflectUtil.getResourceAsStream("image/script.png"));
			SERVICETASK_IMAGE = ImageIO
					.read(ReflectUtil.getResourceAsStream("image/service.png"));
			RECEIVETASK_IMAGE = ImageIO
					.read(ReflectUtil.getResourceAsStream("image/receive.png"));
			SENDTASK_IMAGE = ImageIO.read(ReflectUtil.getResourceAsStream("image/send.png"));
			MANUALTASK_IMAGE = ImageIO
					.read(ReflectUtil.getResourceAsStream("image/manual.png"));
			TIMER_IMAGE = ImageIO.read(ReflectUtil.getResourceAsStream("image/timer.png"));
			ERROR_THROW_IMAGE = ImageIO
					.read(ReflectUtil.getResourceAsStream("image/error_throw.png"));
			ERROR_CATCH_IMAGE = ImageIO
					.read(ReflectUtil.getResourceAsStream("image/error_catch.png"));
			CALLACTIVITY_IMAGE = ImageIO
					.read(ReflectUtil.getResourceAsStream("image/call_activity.png"));
			// 并行
			PARALLEL_IMAGE = ImageIO
					.read(ReflectUtil.getResourceAsStream("image/parallel-mi-marker.png"));
			SEQUENTIAL_IMAGE = ImageIO
					.read(ReflectUtil.getResourceAsStream("image/sequential-mi-marker.png"));

			POOL_BACKGROUP_COLOR = ProcessDiagramColorUtil.getColor("pool_backgroup", "#ffffff");
			LANE_BACKGROUP_COLOR =  ProcessDiagramColorUtil.getColor("lane_backgroup", "#ffffff");

			POOL_BOUNDARY_COLOR = new Color(138, 140, 142);
			LANE_BOUNDARY_COLOR = new Color(138, 140, 142);

		} catch (IOException e) {
			LOGGER.warning("Could not load image for process diagram creation: " + e.getMessage());
		}
	}

	public ProcessDiagramCanvas(int width, int height) {
		this.canvasWidth = width;
		this.canvasHeight = height;
		this.processDiagram = new BufferedImage(width, height, 2);
		this.g = this.processDiagram.createGraphics();
		this.g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
		this.g.setPaint(LINE_COLOR);
		// 字体的名称 \字体的样式
		Font font = new Font("宋体", Font.BOLD, 12);
		this.g.setFont(font);
		this.fontMetrics = this.g.getFontMetrics();
	}

	public ProcessDiagramCanvas(int width, int height, int minX, int minY) {
		this(width, height);
		this.minX = minX;
		this.minY = minY;
	}

	public InputStream generateImage(ImageType imageType) {
		if (this.closed) {
			throw new ActivitiException("ProcessDiagramGenerator already closed");
		}

		ByteArrayOutputStream out = new ByteArrayOutputStream();
		try {
			this.minX = (this.minX <= 5 ? 5 : this.minX);
			this.minY = (this.minY <= 5 ? 5 : this.minY);
			BufferedImage imageToSerialize = this.processDiagram;
			if ((this.minX >= 0) && (this.minY >= 0)) {
				imageToSerialize = this.processDiagram.getSubimage(this.minX - 5, this.minY - 5,
						this.canvasWidth - this.minX + 5, this.canvasHeight - this.minY + 5);
			}
			ImageIO.write(imageToSerialize, imageType.getKey(), out);
		} catch (IOException e) {
			throw new ActivitiException("Error while generating process image", e);
		} finally {
			IoUtil.closeSilently(out);
		}
		return new ByteArrayInputStream(out.toByteArray());
	}

	public void close() {
		this.g.dispose();
		this.closed = true;
	}

	/**
	 * 画没有图片的开始节点
	 *
	 * @param name
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 */
	public void drawNoneStartEvent(String name, int x, int y, int width, int height) {
		drawStartEvent(name, x, y, width, height, null);
	}

	/**
	 * 定时开始节点
	 *
	 * @param name
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 */
	public void drawTimerStartEvent(String name, int x, int y, int width, int height) {
		drawStartEvent(name, x, y, width, height, TIMER_IMAGE);
	}

	/**
	 * 开始节点
	 *
	 * @param name
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 * @param image
	 */
	public void drawStartEvent(String name, int x, int y, int width, int height, Image image) {
		Paint originalPaint = this.g.getPaint();
		Stroke originalStroke = this.g.getStroke();
		this.g.setPaint(START_COLOR);
		this.g.setStroke(START_EVENT_STROKE);
		this.g.draw(new Ellipse2D.Double(x, y, width, height));
		if (image != null)
			this.g.drawImage(image, x, y, width, height, null);
		this.g.setStroke(originalStroke);
		this.g.setPaint(originalPaint);
		// label
		if (name != null) {
			this.g.drawString(fitTextToWidth(name, width), x + 5, y + 50);
		}
	}

	/**
	 * 结束节点
	 *
	 * @param name
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 */
	public void drawNoneEndEvent(String name, int x, int y, int width, int height) {
		Paint originalPaint = this.g.getPaint();
		Stroke originalStroke = this.g.getStroke();
		this.g.setPaint(END_COLOR);
		this.g.setStroke(END_EVENT_STROKE);

		this.g.draw(new Ellipse2D.Double(x, y, width, height));
		this.g.setStroke(originalStroke);
		this.g.setPaint(originalPaint);
		if (name != null) {
			this.g.drawString( fitTextToWidth(name, width), x + 5, y + 50);
		}
	}

	/**
	 * 异常结束
	 *
	 * @param name
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 */
	public void drawErrorEndEvent(String name, int x, int y, int width, int height) {
		drawNoneEndEvent(name, x, y, width, height);
		this.g.drawImage(ERROR_THROW_IMAGE, x + 3, y + 3, width - 6, height - 6, null);
	}

	/**
	 * 抛异常
	 *
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 * @param image
	 */
	public void drawCatchingEvent(int x, int y, int width, int height, Image image) {
		Ellipse2D outerCircle = new Ellipse2D.Double(x, y, width, height);
		int innerCircleX = x + 3;
		int innerCircleY = y + 3;
		int innerCircleWidth = width - 6;
		int innerCircleHeight = height - 6;
		Ellipse2D innerCircle = new Ellipse2D.Double(innerCircleX, innerCircleY, innerCircleWidth, innerCircleHeight);

		Paint originalPaint = this.g.getPaint();
		this.g.setPaint(EVENT_BOUNDARY_COLOR);
		this.g.fill(outerCircle);

		this.g.setPaint(originalPaint);
		this.g.draw(outerCircle);
		this.g.draw(innerCircle);

		this.g.drawImage(image, innerCircleX, innerCircleY, innerCircleWidth, innerCircleHeight, null);
	}

	/**
	 * 时间事件
	 *
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 */
	public void drawCatchingTimerEvent(int x, int y, int width, int height) {
		drawCatchingEvent(x, y, width, height, TIMER_IMAGE);
	}

	public void drawCatchingErroEvent(int x, int y, int width, int height) {
		drawCatchingEvent(x, y, width, height, ERROR_CATCH_IMAGE);
	}

	/**
	 *
	 * 画顺序流
	 *
	 * @param srcX
	 * @param srcY
	 * @param targetX
	 * @param targetY
	 * @param conditional
	 */
	public void drawSequenceflow(int srcX, int srcY, int targetX, int targetY, boolean conditional) {
		Line2D.Double line = new Line2D.Double(srcX, srcY, targetX, targetY);
		this.g.draw(line);
		drawArrowHead(line);

		// 如果有条件，画条件顺序流
		if (conditional)
			drawConditionalSequenceFlowIndicator(line);
	}

	public void drawSequenceflowWithoutArrow(int srcX, int srcY, int targetX, int targetY, boolean conditional) {
		Line2D.Double line = new Line2D.Double(srcX, srcY, targetX, targetY);
		this.g.draw(line);
		if (conditional)
			drawConditionalSequenceFlowIndicator(line);
	}

	public void drawSequenceflow(List<Point2D.Double> points, FlowType flowType) {
		for (int i = 0; i < points.size() - 1; i++) {
			Line2D.Double line = new Line2D.Double(points.get(i).getX(), points.get(i).getY(), points.get(i + 1).getX(),
					points.get(i + 1).getY());
			if (flowType.equals(FlowType.ASSOCIATION)) {//虚线
				this.g.setStroke(DASHED_STROKE);
			} else {
				this.g.setStroke(LABEL_STROKE);
			}
			this.g.draw(line);
			// 箭头
			if (i == points.size() - 2 && flowType.equals(FlowType.SEQUENCE_FLOW)) {
				drawArrowHead(line);
			}
		}
	}

	public void drawSequenceflowWidthLabel(BPMNEdge bpmnEdge) {
		drawSequenceflow(bpmnEdge.getPoints(), bpmnEdge.getFlowType());
		drawSequenceflowLabel(bpmnEdge.getName(), (int) bpmnEdge.getMidpoint().getX(),
				(int) bpmnEdge.getMidpoint().getY(), bpmnEdge.getDirection());
	}

	/**
	 * 绘画连线上的标签。
	 *
	 * @param name
	 *            标签内容
	 * @param x
	 *            连线的中点的x坐标
	 * @param y
	 *            连线的中点的y坐标
	 * @param directionType
	 *            中点所在的那一段直线的方向:
	 *
	 */
	public void drawSequenceflowLabel(String name, int x, int y, DirectionType directionType) {
		if (name == null) {
			return;
		}
		int drawX = x, drawY = y;
		switch (directionType) {
		case UP_TO_DOWN:
			drawX = x + g.getFontMetrics().getHeight() / 2;
			drawY = y;
			break;
		case DOWN_TO_UP:
			drawX = x - g.getFontMetrics().stringWidth(name) - g.getFontMetrics().getHeight() / 2;
			drawY = y + g.getFontMetrics().getHeight();
			break;
		case LEFT_TO_RIGHT:
			drawX = x - g.getFontMetrics().stringWidth(name) / 2;
			drawY = y - g.getFontMetrics().getHeight() / 2;
			break;
		case RIGHT_TO_LEFT:
			drawX = x - g.getFontMetrics().stringWidth(name) / 2;
			drawY = y + g.getFontMetrics().getHeight();
			break;
		}
		Paint originalPaint = this.g.getPaint();
		Stroke originalStroke = this.g.getStroke();
		this.g.setPaint(LINE_COLOR);
		this.g.setStroke(LABEL_STROKE);
		this.g.drawString(name, drawX, drawY);

		this.g.setStroke(originalStroke);
		this.g.setPaint(originalPaint);
	}

	public void drawArrowHead(Line2D.Double line) {
		int doubleArrowWidth = 10;
		Polygon arrowHead = new Polygon();
		arrowHead.addPoint(0, 0);
		arrowHead.addPoint(-5, -doubleArrowWidth);
		arrowHead.addPoint(5, -doubleArrowWidth);

		AffineTransform transformation = new AffineTransform();
		transformation.setToIdentity();
		double angle = Math.atan2(line.y2 - line.y1, line.x2 - line.x1);
		transformation.translate(line.x2, line.y2);
		transformation.rotate(angle - 1.570796326794897D);

		AffineTransform originalTransformation = this.g.getTransform();
		this.g.setTransform(transformation);
		this.g.fill(arrowHead);
		this.g.setTransform(originalTransformation);
	}

	public void drawConditionalSequenceFlowIndicator(Line2D.Double line) {
		int horizontal = 11;
		int halfOfHorizontal = horizontal / 2;
		int halfOfVertical = 8;

		Polygon conditionalIndicator = new Polygon();
		conditionalIndicator.addPoint(0, 0);
		conditionalIndicator.addPoint(-halfOfHorizontal, halfOfVertical);
		conditionalIndicator.addPoint(0, 16);
		conditionalIndicator.addPoint(halfOfHorizontal, halfOfVertical);

		AffineTransform transformation = new AffineTransform();
		transformation.setToIdentity();
		double angle = Math.atan2(line.y2 - line.y1, line.x2 - line.x1);
		transformation.translate(line.x1, line.y1);
		transformation.rotate(angle - 1.570796326794897D);

		AffineTransform originalTransformation = this.g.getTransform();
		this.g.setTransform(transformation);
		this.g.draw(conditionalIndicator);

		Paint originalPaint = this.g.getPaint();
		this.g.setPaint(CONDITIONAL_INDICATOR_COLOR);
		this.g.fill(conditionalIndicator);

		this.g.setPaint(originalPaint);
		this.g.setTransform(originalTransformation);
	}

	public void drawTask(String name, int x, int y, int width, int height) {
		drawTask(name, x, y, width, height, false);
	}

	protected void drawTask(String name, int x, int y, int width, int height, boolean thickBorder) {
		Paint originalPaint = this.g.getPaint();
		this.g.setPaint(TASK_COLOR);

		RoundRectangle2D rect = new RoundRectangle2D.Double(x, y, width, height, 20.0D, 20.0D);
		this.g.fill(rect);
		this.g.setPaint(originalPaint);

		if (thickBorder) {
			Stroke originalStroke = this.g.getStroke();
			this.g.setStroke(THICK2_TASK_BORDER_STROKE);
			this.g.draw(rect);
			this.g.setStroke(originalStroke);
		} else {
			this.g.draw(rect);
		}

		if (name != null) {
			String[] nameAry = this.fitTextToWidthAndBreak(name, width);
			int offsetY = this.fontMetrics.getHeight();
			int textY = y + (height - this.fontMetrics.getHeight()) / 2 + 10;
			for (int i = 0; i < nameAry.length; i++) {
				String l = nameAry[i];
				int textX = x + (width - this.fontMetrics.stringWidth(l)) / 2;
				this.g.drawString(l, textX, textY + i * offsetY);
			}
		}
	}

	private String[] fitTextToWidthAndBreak(String original, int width) {
		String line1 = "";
		String tmp1 = "";
		String line2 = "";

		int maxWidth = width;

		if (this.fontMetrics.stringWidth(original) <= maxWidth) {
			return new String[] { original };
		}

		while (this.fontMetrics.stringWidth(tmp1) <= maxWidth) {
			line1 = tmp1;
			tmp1 = original.substring(0, line1.length() + 1);
		}

		line2 = original.substring(line1.length());

		if (this.fontMetrics.stringWidth(line2) <= maxWidth) {
			return new String[] { line1, line2 };
		}

		while ((this.fontMetrics.stringWidth(line2 + "...") > maxWidth) && (line2.length() > 0)) {
			line2 = line2.substring(0, line2.length() - 1);
		}

		if (!line2.equals(original)) {
			line2 = line2 + "...";
		}

		return new String[] { line1, line2 };
	}

	protected String fitTextToWidth(String original, int width) {
		// String text = original;
		String line1 = "";
		String tmp1 = "";
		String line2 = "";

		int maxWidth = width - 10;

		if (this.fontMetrics.stringWidth(original) <= maxWidth) {
			return original;
		}

		while (this.fontMetrics.stringWidth(tmp1) <= maxWidth) {
			line1 = tmp1;
			tmp1 = original.substring(0, line1.length() + 1);
		}

		line2 = original.substring(line1.length());
		while ((this.fontMetrics.stringWidth(line2 + "...") > maxWidth) && (line2.length() > 0)) {
			line2 = line2.substring(0, line2.length() - 1);
		}

		if (!line2.equals(original)) {
			line2 = line2 + "...";
		}

		return line1 + "\r\n" + line2;

		/*
		 * 超过字数省略号 while ((this.fontMetrics.stringWidth(text + "...") >
		 * maxWidth)&& (text.length() > 0)) { text = text.substring(0,
		 * text.length() - 1); }
		 *
		 * if (!text.equals(original)) { text = text + "..."; }
		 *
		 * return text;
		 */
	}

	/**
	 * 画任务
	 */
	public void drawTask(Image img, String name, int x, int y, int width, int height) {
		this.drawTask(name, x, y, width, height);
		this.g.drawImage(img, x + 5, y + 8, ICON_SIZE, ICON_SIZE, null);
	}

	/**
	 * 画用户任务
	 */
	public void drawUserTask(String name, int x, int y, int width, int height) {
		drawTask(USERTASK_IMAGE, name, x, y, width, height);
	}

	/**
	 * 脚本任务
	 *
	 */
	public void drawScriptTask(String name, int x, int y, int width, int height) {
		drawTask(SCRIPTTASK_IMAGE, name, x, y, width, height);
	}

	/**
	 * 服务任务
	 */
	public void drawServiceTask(String name, int x, int y, int width, int height) {
		drawTask(SERVICETASK_IMAGE, name, x, y, width, height);
	}

	/**
	 * 接收任务
	 *
	 */
	public void drawReceiveTask(String name, int x, int y, int width, int height) {
		drawTask(RECEIVETASK_IMAGE, name, x, y, width, height);
	}

	/**
	 * 发送任务
	 *
	 */
	public void drawSendTask(String name, int x, int y, int width, int height) {
		drawTask(SENDTASK_IMAGE, name, x, y, width, height);
	}

	/**
	 * 手工任务
	 */
	public void drawManualTask(String name, int x, int y, int width, int height) {
		drawTask(MANUALTASK_IMAGE, name, x, y, width, height);
	}

	public void drawExpandedSubProcess(String name, int x, int y, int width, int height) {
		RoundRectangle2D rect = new RoundRectangle2D.Double(x, y, width, height, 20.0D, 20.0D);
		this.g.draw(rect);

		String text = fitTextToWidth(name, width);
		this.g.drawString(text, x + 10, y + 15);
	}

	public void drawCollapsedSubProcess(String name, int x, int y, int width, int height) {
		drawCollapsedTask(name, x, y, width, height, false);
	}

	public void drawCollapsedCallActivity(String name, int x, int y, int width, int height) {
		drawCollapsedTask(name, x, y, width, height, false);
		int ix = x + (width - ICON_SIZE) / 2;
		int iy = y + height - ICON_SIZE - 5;
		this.g.drawImage(CALLACTIVITY_IMAGE, ix, iy, ICON_SIZE, ICON_SIZE, null);
	}

	protected void drawCollapsedTask(String name, int x, int y, int width, int height, boolean thickBorder) {
		this.drawTask(name, x, y, width, height, thickBorder);
	}

	public void drawCollapsedMarker(int x, int y, int width, int height) {
		int rectangleWidth = 12;
		int rectangleHeight = 12;
		Rectangle rect = new Rectangle(x + (width - rectangleWidth) / 2, y + height - rectangleHeight - 3,
				rectangleWidth, rectangleHeight);
		this.g.draw(rect);

		Line2D.Double line = new Line2D.Double(rect.getCenterX(), rect.getY() + 2.0D, rect.getCenterX(),
				rect.getMaxY() - 2.0D);
		this.g.draw(line);
		line = new Line2D.Double(rect.getMinX() + 2.0D, rect.getCenterY(), rect.getMaxX() - 2.0D, rect.getCenterY());
		this.g.draw(line);
	}

	public void drawActivityMarkers(int x, int y, int width, int height, boolean multiInstanceSequential,
			boolean multiInstanceParallel, boolean collapsed) {
		if (collapsed) {
			if ((!multiInstanceSequential) && (!multiInstanceParallel)) {
				drawCollapsedMarker(x, y, width, height);
			} else {
				drawCollapsedMarker(x - 6 - 2, y, width, height);
				if (multiInstanceSequential)
					drawMultiInstanceMarker(true, x + 6 + 2, y, width, height);
				else if (multiInstanceParallel) {
					drawMultiInstanceMarker(false, x + 6 + 2, y, width, height);
				}
			}
		} else if (multiInstanceSequential) {
			drawMultiInstanceMarker(false, x, y, width, height);
		} else if (multiInstanceParallel) {
			drawMultiInstanceMarker(true, x, y, width, height);
		}
	}

	/**
	 *
	 * 网关
	 *
	 * @param name
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 */
	public void drawGateway(String name, int x, int y, int width, int height) {
		Polygon rhombus = new Polygon();
		rhombus.addPoint(x, y + height / 2);
		rhombus.addPoint(x + width / 2, y + height);
		rhombus.addPoint(x + width, y + height / 2);
		rhombus.addPoint(x + width / 2, y);
		this.g.draw(rhombus);

		if (name != null && !name.endsWith("Gateway")) {
			int textX = x + width / 4 - this.fontMetrics.stringWidth(name) - this.fontMetrics.getHeight() / 4;
			int textY = y + height / 4 - this.fontMetrics.getHeight() / 4;
			this.g.drawString(name, textX, textY);
		}
	}

	/**
	 * 串行网关(同步网关)
	 */
	public void drawParallelGateway(String name, int x, int y, int width, int height) {
		drawGateway(name, x, y, width, height);
		Stroke orginalStroke = this.g.getStroke();
		this.g.setStroke(GATEWAY_TYPE_STROKE);
		Line2D.Double line = new Line2D.Double(x + 10, y + height / 2, x + width - 10, y + height / 2);
		this.g.draw(line);
		line = new Line2D.Double(x + width / 2, y + height - 10, x + width / 2, y + 10);
		this.g.draw(line);
		this.g.setStroke(orginalStroke);
	}

	/**
	 *
	 * 分支网关
	 *
	 * @param name
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 */
	public void drawExclusiveGateway(String name, int x, int y, int width, int height) {
		drawGateway(name, x, y, width, height);

		int quarterWidth = width / 4;
		int quarterHeight = height / 4;

		Stroke orginalStroke = this.g.getStroke();
		this.g.setStroke(GATEWAY_TYPE_STROKE);
		Line2D.Double line = new Line2D.Double(x + quarterWidth + 3, y + quarterHeight + 3, x + 3 * quarterWidth - 3,
				y + 3 * quarterHeight - 3);
		this.g.draw(line);
		line = new Line2D.Double(x + quarterWidth + 3, y + 3 * quarterHeight - 3, x + 3 * quarterWidth - 3,
				y + quarterHeight + 3);
		this.g.draw(line);

		this.g.setStroke(orginalStroke);
	}

	/**
	 *
	 * 分支同步
	 *
	 * @param name
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 */
	public void drawInclusiveGateway(String name, int x, int y, int width, int height) {
		drawGateway(name, x, y, width, height);

		int diameter = width / 2;

		Stroke orginalStroke = this.g.getStroke();
		this.g.setStroke(GATEWAY_TYPE_STROKE);
		Ellipse2D.Double circle = new Ellipse2D.Double((width - diameter) / 2 + x, (height - diameter) / 2 + y,
				diameter, diameter);
		this.g.draw(circle);
		this.g.setStroke(orginalStroke);
	}

	/**
	 * 多实例【会签线】 （串行、并行）
	 *
	 * @param sequential
	 *            是否串行
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 */
	public void drawMultiInstanceMarker(boolean sequential, int x, int y, int width, int height) {
		int ix = x + (width - ICON_SIZE) / 2;
		int iy = y + height - ICON_SIZE - 2;
		Image image = sequential ? PARALLEL_IMAGE : SEQUENTIAL_IMAGE;
		this.g.drawImage(image, ix, iy, ICON_SIZE, ICON_SIZE, null);
	}

	public void drawHighLight(int x, int y, int width, int height) {
		Paint originalPaint = this.g.getPaint();
		Stroke originalStroke = this.g.getStroke();

		this.g.setPaint(HIGHLIGHT_COLOR);
		this.g.setStroke(THICK2_TASK_BORDER_STROKE);

		RoundRectangle2D rect = new RoundRectangle2D.Double(x, y, width, height, 20.0D, 20.0D);
		this.g.draw(rect);

		this.g.setPaint(originalPaint);
		this.g.setStroke(originalStroke);
	}

	/**
	 * 设置高亮（主要设置样式）
	 *
	 * @param x
	 * @param y
	 * @param width
	 * @param height
	 * @param style
	 */
	public void drawHighLight(int x, int y, int width, int height, FlowImageStyle style) {
		// 默认样式
		String borderColor = "";
		float borderWidth = 1.0f;
		if (style != null) {
			borderColor = style.getBorderColor() != null ? style.getBorderColor() : "#000000";
			borderWidth = style.getBorderWidth() != null ? style.getBorderWidth() : 2.0f;
		} else {
			borderColor = ProcessDiagramColorUtil.getColorString("default", "#000000");
		}

		Paint originalPaint = this.g.getPaint();
		Stroke originalStroke = this.g.getStroke();

		// 边框颜色
		this.g.setPaint(Color.decode(borderColor));
		// 边框粗细
		this.g.setStroke(new BasicStroke(borderWidth));

		RoundRectangle2D rect = new RoundRectangle2D.Double(x, y, width, height, 20.0D, 20.0D);
		this.g.draw(rect);

		this.g.setPaint(originalPaint);
		this.g.setStroke(originalStroke);
	}

	public void drawHPool(String name, int x, int y, int width, int height) {
		Rectangle2D rect = new Rectangle2D.Double(x, y, width, height);
		Paint originalPaint = this.g.getPaint();

		this.g.setPaint(POOL_BACKGROUP_COLOR);
		RoundRectangle2D roundRectangle2D = new RoundRectangle2D.Double(x, y,width, height, 0, 0);
		this.g.fill(roundRectangle2D);

		Stroke originalStroke = this.g.getStroke();
		this.g.setPaint(POOL_BOUNDARY_COLOR);
		this.g.draw(rect);

		// String text = fitTextToWidth(name, width);

		int textLen = fontMetrics.stringWidth(name);
		int textX = x + fontMetrics.getHeight() / 2;
		int textY = y + height / 2 - textLen / 2;

		AffineTransform oldAffineTransform = g.getTransform();
		AffineTransform newAffineTransform = AffineTransform.getRotateInstance(-Math.PI / 2.0, textX, textY);
		g.setTransform(newAffineTransform);
		this.g.setPaint(DEFAULT_COLOR);
		this.g.drawString(name, textX - textLen, textY + fontMetrics.getHeight() / 2);
		g.setTransform(oldAffineTransform);
		g.setStroke(originalStroke);
		g.setPaint(originalPaint);
	}

	public void drawHLane(String name, int x, int y, int width, int height) {
		Rectangle2D rect = new Rectangle2D.Double(x, y, width, height);
		Paint originalPaint = this.g.getPaint();

		this.g.setPaint(LANE_BACKGROUP_COLOR);
		RoundRectangle2D roundRectangle2D = new RoundRectangle2D.Double(x, y, 20, height, 0, 0);
		this.g.fill(roundRectangle2D);

		Stroke originalStroke = this.g.getStroke();
		this.g.setPaint(LANE_BOUNDARY_COLOR);
		this.g.draw(rect);

		// String text = fitTextToWidth(name, width);
		int textLen = fontMetrics.stringWidth(name);
		int textX = x + fontMetrics.getHeight() / 2;
		int textY = y + height / 2 - textLen / 2;

		AffineTransform oldAffineTransform = g.getTransform();
		AffineTransform newAffineTransform = AffineTransform.getRotateInstance(-Math.PI / 2.0, textX, textY);

		g.setTransform(newAffineTransform);
		this.g.setPaint(DEFAULT_COLOR);
		this.g.drawString(name, textX - textLen, textY + fontMetrics.getHeight() / 2);
		g.setTransform(oldAffineTransform);
		g.setStroke(originalStroke);
		g.setPaint(originalPaint);
	}

	public void drawVPool(String name, int x, int y, int width, int height) {
		Rectangle2D rect = new Rectangle2D.Double(x, y, width, height);
		Paint originalPaint = this.g.getPaint();
		Stroke originalStroke = this.g.getStroke();

		this.g.setPaint(POOL_BACKGROUP_COLOR);
		RoundRectangle2D roundRectangle2D = new RoundRectangle2D.Double(x, y, width, 20, 0, 0);
		this.g.fill(roundRectangle2D);

		this.g.setPaint(POOL_BOUNDARY_COLOR);
		this.g.draw(rect);

		name = fitTextToWidth(name, width);
		int textLen = fontMetrics.stringWidth(name);
		int textY = y + fontMetrics.getHeight();
		int textX = x + width / 2 - textLen / 2;
		this.g.setPaint(DEFAULT_COLOR);
		this.g.drawString(name, textX, textY);
		g.setStroke(originalStroke);
		g.setPaint(originalPaint);

	}

	public void drawVLane(String name, int x, int y, int width, int height) {
		Rectangle2D rect = new Rectangle2D.Double(x, y, width, height);
		Paint originalPaint = this.g.getPaint();
		Stroke originalStroke = this.g.getStroke();

		this.g.setPaint(LANE_BACKGROUP_COLOR);
		RoundRectangle2D roundRectangle2D = new RoundRectangle2D.Double(x, y, width, 20, 0, 0);
		this.g.fill(roundRectangle2D);

		this.g.setPaint(LANE_BOUNDARY_COLOR);
		this.g.draw(rect);

		name = fitTextToWidth(name, width);
		int textLen = fontMetrics.stringWidth(name);
		int textY = y + fontMetrics.getHeight();
		int textX = x + width / 2 - textLen / 2;
		this.g.setPaint(DEFAULT_COLOR);
		this.g.drawString(name, textX, textY);
		g.setStroke(originalStroke);
		g.setPaint(originalPaint);
	}

	public void drawTextAnnotation(String name, int x, int y, int width, int height) {
		// TODO Auto-generated method stub
		//drawTask(name, x, y, width, height);
	}
}
