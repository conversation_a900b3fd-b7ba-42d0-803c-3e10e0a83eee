/**
 * 描述：触发流程对象构建
 * 包名：com.huazheng.bpm.biz.bpmn.builder
 * 文件名：BpmTrigerFlowBuilder.java
 * 作者：eddy
 * 日期：2017年8月25日-上午10:13:19
 * 版权：广州流辰信息技术有限公司版权所有
 *
 */
package com.huazheng.bpm.builder;


import com.huazheng.bpm.entity.bpm.BpmTrigerFlowPo;
import com.huazheng.bpm.entity.constant.NodeStatus;
import com.huazheng.bpm.model.define.IBpmDefine;
import com.huazheng.bpm.service.BpmDefineService;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.string.StringUtil;

import java.util.List;

/**
 * 触发流程对象构建
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年8月25日-上午10:13:19
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmTrigerFlowBuilder {

	/**
	 * 构建流程触发对象展示属性
	 *
	 * @param bpmAgentList
	 */
	public static void build(List<BpmTrigerFlowPo> bpmTrigerFlowPo){
		if(BeanUtils.isEmpty(bpmTrigerFlowPo)){
			return;
		}

		for(BpmTrigerFlowPo po : bpmTrigerFlowPo){
			build(po);
		}
	}

	/**
	 * 构建流程触发对象展示属性
	 *
	 * @param bpmAgent
	 */
	public static void build(BpmTrigerFlowPo bpmTrigerFlowPo){
		if(BeanUtils.isEmpty(bpmTrigerFlowPo)){
			return;
		}

		if(StringUtil.isNotEmpty(bpmTrigerFlowPo.getTrigerFlowKey())){
			BpmDefineService bpmDefineService = AppUtil.getBean(BpmDefineService.class);
			IBpmDefine bpmDefine = bpmDefineService.getBpmDefinitionByDefKey(bpmTrigerFlowPo.getTrigerFlowKey());
			if(BeanUtils.isEmpty(bpmTrigerFlowPo)){
				bpmTrigerFlowPo.setTrigerFlowName(bpmTrigerFlowPo.getTrigerFlowKey());
			}else{
				bpmTrigerFlowPo.setTrigerFlowName(bpmDefine.getName());
			}
		}

		if(StringUtil.isNotEmpty(bpmTrigerFlowPo.getAction())){
			NodeStatus status = NodeStatus.fromKey(bpmTrigerFlowPo.getAction());
			if(BeanUtils.isEmpty(status)){
				bpmTrigerFlowPo.setActionName(bpmTrigerFlowPo.getAction());
			}else{
				bpmTrigerFlowPo.setActionName(status.getValue());
			}
		}
	}
}
