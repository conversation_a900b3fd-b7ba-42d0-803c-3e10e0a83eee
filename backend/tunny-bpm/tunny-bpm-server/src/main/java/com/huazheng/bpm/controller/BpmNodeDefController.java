package com.huazheng.bpm.controller;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huazheng.bpm.entity.party.PartyRolePo;
import com.huazheng.bpm.entity.party.PartyUserPo;
import com.huazheng.bpm.model.base.User;
import com.huazheng.bpm.model.define.BpmProcExtendDefine;
import com.huazheng.bpm.model.define.IBpmProcDefine;
import com.huazheng.bpm.model.define.IBpmProcExtendDefine;
import com.huazheng.bpm.model.define.IBpmVariableDefine;
import com.huazheng.bpm.model.http.PostParameter;
import com.huazheng.bpm.model.node.AutoTaskDefine;
import com.huazheng.bpm.model.node.IBpmNodeDefine;
import com.huazheng.bpm.plugin.AbstractBpmPluginContext;
import com.huazheng.bpm.plugin.IBpmPluginDefine;
import com.huazheng.bpm.service.IBpmDefineReader;
import com.huazheng.bpm.util.base.FieldLogic;
import com.huazheng.bpm.util.base.QueryFilter;
import com.huazheng.bpm.util.base.WhereClause;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.helper.IUserPluginQueryHelper;
import com.huazheng.bpm.util.http.HttpClient;
import com.huazheng.bpm.util.http.Response;
import com.huazheng.bpm.util.json.JsonUtil;
import com.huazheng.bpm.util.json.PageJson;
import com.huazheng.bpm.util.page.Page;
import com.huazheng.bpm.util.page.PageList;
import com.huazheng.bpm.util.page.PageResult;
import com.huazheng.bpm.util.string.StringUtil;
import com.huazheng.bpm.util.web.RequestUtil;
import com.huazheng.bpm.api.entity.SysRole;
import com.huazheng.bpm.api.entity.SysUser;
import com.huazheng.bpm.api.feign.RemoteUserService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 *
 * <pre>
 *
 * 描述：流程节点配置管理
 * 构建组：ibps-platform-admin
 * 作者：caixy
 * 邮箱：<EMAIL>
 * 日期：2015-12-30 11:11:20
 * 版权：广州流辰信息技术有限公司
 * </pre>
 */

@Controller
@RequestMapping("/platform/bpmn/bpmNodeDef/")
public class BpmNodeDefController extends GenericController {

	@Resource
	private IBpmDefineReader bpmDefineReader;
	@Resource
	private IUserPluginQueryHelper userPluginQueryHelper;
	@Resource
	private RemoteUserService remoteUserService;

	@Resource
    private List<Object> nodeUserPluginList;

	/**
	 * 节点人员条件
	 */
	@RequestMapping("conditionEdit")
	public ModelAndView conditionEdit(HttpServletRequest request, HttpServletResponse response) throws Exception {
		//List<?> nodeUserPluginList1 = (List<?>) AppUtil.getBean("nodeUserPluginList");
		String extract = AppUtil.getProperty("bpm.def.node.user.extract");
		return getAutoView().addObject("nodeUserPluginList", nodeUserPluginList).addObject("extract", extract);
	}

	/**
	 * 节点集合
	 */
	@RequestMapping("nodeDef/sameNodeDialog")
	public ModelAndView sameNodeDialog(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String defId = RequestUtil.getString(request, "defId");
		String nodeId = RequestUtil.getString(request, "nodeId");
		List<IBpmNodeDefine> nodeDefList = bpmDefineReader.findNodeWithoutSD(defId);

		return getAutoView().addObject("defId", defId).addObject("nodeId", nodeId).addObject("nodeDefList",
				nodeDefList);
	}
	/**
	 * 用户选择
	 */
	@RequestMapping("nodeDef/cusersDialog")
	public String cusersDialog(HttpServletRequest request, HttpServletResponse response) throws Exception {
		return "/platform/bpmn/bpmNodeDef/nodeDefCusersDialog";
	}
	/**
	 * 用户选择
	 */
	@RequestMapping("person")
	public String person(HttpServletRequest request, HttpServletResponse response) throws Exception {
		return "/platform/org/partyDialogPerson";
	}
	/**
	 * 角色选择
	 */
	@RequestMapping("nodeDef/roleDialog")
	public String roleDialog(HttpServletRequest request, HttpServletResponse response) throws Exception {
		return "/platform/bpmn/bpmNodeDef/nodeDefRoleDialog";
	}

	/**
	 * 角色选择框
	 */
	@RequestMapping("partyRoleDialog")
	public String partyRoleDialog(HttpServletRequest request, HttpServletResponse response) throws Exception {
		return "/platform/org/partyRoleDialog";
	}
	/**
	 * 岗位选择
	 */
	@RequestMapping("nodeDef/posDialog")
	public String posDialog(HttpServletRequest request, HttpServletResponse response) throws Exception {
		return "/platform/bpmn/bpmNodeDef/nodeDefPosDialog";
	}
	/**
	 * 岗位选择框
	 */
	@RequestMapping("partyPosDialog")
	public String partyPosDialog(HttpServletRequest request, HttpServletResponse response) throws Exception {
		return "/platform/org/partyPositionDialog";
	}

	/**
	 * 会签特权配置
	 */
	@RequestMapping("settingPrivileges")
	public String settingPrivileges(HttpServletRequest request, HttpServletResponse response) throws Exception {
		return "/platform/bpmn/bpmNodeDefSettingPrivileges";
	}
	/**
	 * 角色列表(分页条件查询)数据
	 *
	 * @param request
	 * @param reponse
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("listBySubSysJson")
	public @ResponseBody PageJson listBySubSysJson(HttpServletRequest request,HttpServletResponse reponse) throws Exception{
		PageList<PartyRolePo> partyRoleList = new PageList<PartyRolePo>();
		QueryFilter queryFilter=getQuerFilter(request);
		Page page = queryFilter.getPage();
        FieldLogic fieldLogic = queryFilter.getFieldLogic();
        List<WhereClause> whereClauseList = fieldLogic.getWhereClauses();
		ObjectMapper mapper = new ObjectMapper();
		List<PartyRolePo> roleList = new ArrayList<>();
		List<SysRole> sysRoleList = remoteUserService.roleListAll();
		if (sysRoleList != null && sysRoleList.size()>0){
			for (SysRole sysRole:sysRoleList) {
			    //根据传入查询条件是否有name相匹配的数据
                if(whereClauseList.size()>0){
                    JSONObject whereClause = JSONObject.fromObject(whereClauseList.get(0));
                    String name_ = whereClause.getString("value").replaceAll("%", "");
                    if(sysRole.getRoleName().indexOf(name_)<0){
                        continue;
                    }
                }
				PartyRolePo rolePo = new PartyRolePo();
				rolePo.setId(sysRole.getRoleId().toString());
				rolePo.setRoleId(sysRole.getRoleCode());
				rolePo.setName(sysRole.getRoleName());
				rolePo.setRoleName(sysRole.getRoleName());
				roleList.add(rolePo);
			}
		}

			PageResult pageResult = new PageResult(page.getPageNo(),page.getPageSize(),roleList.size());
			List<PartyRolePo> resList = roleList.subList(
					(page.getPageNo() - 1) * page.getPageSize(),
                    page.getPageNo() * page.getPageSize() > roleList.size() ? roleList.size() : page.getPageNo() * page.getPageSize()
			);
			partyRoleList = new PageList<PartyRolePo>(resList, pageResult);
	//	}

		logger.debug("com.lc.ibps.platform.org.controller.PartyRoleController.listBySubSysJson()--->partyRoleList="
				+(partyRoleList!=null?Arrays.toString(partyRoleList.toArray()):"")
		);

		return new PageJson(partyRoleList);
	}
	/**
	 *
	 * 用户选择框查询数据
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("dialogUserJson")
	public @ResponseBody PageJson dialogUserJson(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		QueryFilter queryFilter = getQuerFilter(request);
		Map<String, Object>  pararms = queryFilter.getParams();
		Page page = queryFilter.getPage();
		Object searchName = pararms.get("userName");
		String searchName_="";
		if(searchName !=null ){
			searchName_ = searchName.toString();
		}
		List<SysUser> usersList = remoteUserService.selectUserList(searchName_);
		List<PartyUserPo> userList = new ArrayList<>();
		if(usersList != null && usersList.size()>0){
			for (int i=0;i<usersList.size();i++) {
				SysUser sysUser = JSON.parseObject(JSON.toJSONString(usersList.get(i)),SysUser.class);
				PartyUserPo partyUserPo = new PartyUserPo();
				partyUserPo.setUserId(sysUser.getUserId().toString());
				partyUserPo.setFullname(sysUser.getUserRealname());
				partyUserPo.setAccount(sysUser.getEmpno());
				userList.add(partyUserPo);

			}
		}
		PageResult pageResult = new PageResult(page.getPageNo(),page.getPageSize(),userList.size());
		List<PartyUserPo> resList = userList.subList(
				(page.getPageNo() - 1) * page.getPageSize(),
				page.getPageNo() * page.getPageSize() > userList.size() ? userList.size() : page.getPageNo() * page.getPageSize()
		);
		userList = new PageList<PartyUserPo>(resList, pageResult);
//		String listData = partyUserBaseQueryService.queryDialogUserByParamJson(queryFilter, orgId, inclueChild);
//		if(JacksonUtil.isJsonObject(listData)){
//			List<PartyUserPo> list = PartyUserPo.fromJsonArrayString(JacksonUtil.getString(listData, "data"));
//			PageResult pageResult = PageResult.fromJson(JacksonUtil.getString(listData, "pageResult"));
//			userList = new PageList<PartyUserPo>(list, pageResult);
//		}

		logger.debug("com.lc.ibps.platform.org.controller.PartyUserController.dialogUserJson()" + "-->userList:"
				+ (userList != null ? Arrays.toString(userList.toArray()) : ""));

		return new PageJson((PageList<PartyUserPo>) userList);
	}

	/**
	 * 预览人员条件
	 *
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping("previewCondition")
	@ResponseBody
	public PageJson previewCondition(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String conditionArray = RequestUtil.getString(request, "conditionArray", "[]");
		String variables = RequestUtil.getString(request, "variables", "{}");
		JSONObject obj = JSONObject.fromObject(variables);
		Map<String, Object> map = (Map<String, Object>) JSONObject.toBean(obj, Map.class);

		List<Map<String, String>> users = userPluginQueryHelper.queryUsersByConditions(conditionArray, map);
		List<User> us = new ArrayList<User>();
		if (BeanUtils.isNotEmpty(users)) {
			for (Map<String, String> userMap : users) {
//				User user = DefaultPartyUserPo
//						.fromJsonString2(defaultPartyUserService.getByIdJson(userMap.get(BpmIdentity.IDENT_ID)));
//				us.add(user);
			}
		}
		return new PageJson(us);
	}

	/**
	 * 自动节点，获取插件数据
	 */
	@RequestMapping("autoTaskPluginGet")
	public ModelAndView autoTaskPluginGet(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String defId = RequestUtil.getString(request, "defId");
		String nodeId = RequestUtil.getString(request, "nodeId");
		String pluginType = RequestUtil.getString(request, "pluginType");
		List<IBpmVariableDefine> bpmVariableList = getAllBpmVariableDef(defId, nodeId);
//		ModelAndView mv = new ModelAndView(
//				"/platform/bpmn/bpmNodeDefAutoTask" + StringUtil.convertFirst(pluginType, true) + "Edit.jsp");
		ModelAndView mv = new ModelAndView(
				"/platform/bpmn/bpmNodeDefAutoTask" + StringUtil.convertFirst(pluginType, true) + "Edit");

		AutoTaskDefine autoTaskDef = (AutoTaskDefine) bpmDefineReader.getNode(defId, nodeId);

		AbstractBpmPluginContext bpmPluginContext = (AbstractBpmPluginContext) autoTaskDef
				.getAutoTaskBpmPluginContext();

		// 已经选择并保存该插件。
		if (bpmPluginContext != null && (bpmPluginContext.getType().equals(pluginType)
				|| bpmPluginContext.getType().toLowerCase().indexOf("service") != -1)) {
			IBpmPluginDefine bpmPluginDef = bpmPluginContext.getBpmPluginDefine();
			mv.addObject("bpmPluginDef", bpmPluginDef);
			String json = bpmPluginContext.getJson();
			mv.addObject("bpmPluginDefJson", json);
		}

		return mv.addObject("defId", defId).addObject("nodeId", nodeId).addObject("bpmPluginContext", bpmPluginContext)
				.addObject("pluginType", pluginType).addObject("bpmVariableList", bpmVariableList);
	}

	/**
	 * 该节点能用的所有变量
	 */
	private List<IBpmVariableDefine> getAllBpmVariableDef(String defId, String nodeId) {
		List<IBpmVariableDefine> bpmVariableList = new ArrayList<IBpmVariableDefine>();
		// 全局变量
		IBpmProcDefine<IBpmProcExtendDefine> bpmProcessDefExt = bpmDefineReader.getBpmProcDefine(defId);
		BpmProcExtendDefine defExt = (BpmProcExtendDefine) bpmProcessDefExt.getBpmProcExtendDefine();
		if (defExt.getVarList() != null) {
			bpmVariableList.addAll(defExt.getVarList());
		}
		return bpmVariableList;
	}

	/**
	 * api path 的格式如下： http://127.0.0.1:8080/ibps/api/webapi/bpmService/myTasks
	 *
	 * @param request
	 * @param reponse
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("getRestApi")
	@ResponseBody
	public Map<String, Object> getRestApi(HttpServletRequest request, HttpServletResponse reponse) throws Exception {
		Map<String, Object> rs = new HashMap<String, Object>();

		String target = RequestUtil.getString(request, "target");
		String baseUrl = AppUtil.getProperty("webapi.baseURL");

		boolean success = true;
		String serPath = "";
		String apiKey = "";
		try {
			apiKey = target.substring(target.indexOf(baseUrl) + baseUrl.length());
			serPath = "/" + apiKey.split("/")[2];
		} catch (Exception e) {
			success = false;
			rs.put("msg", "此地址无效");
			return rs;
		}
		String baseApiUrl = baseUrl + AppUtil.getProperty("webapi.apiDocs") + serPath;
		HttpClient client = new HttpClient();
		Response res = client.get(baseApiUrl, new PostParameter[] {}, false, null);
		JSONArray apis = res.asJSONObject().getJSONArray("apis");
		if (apis.size() == 0) {
			success = false;
			rs.put("msg", "此地址无效");
			return rs;
		}
		JSONObject getPath = null;

		for (int ix = 0; ix < apis.size(); ix++) {
			JSONObject api = JSONObject.fromObject(apis.get(ix));
			if (apiKey.equals(api.get("path"))) {
				getPath = api;
			}
		}
		Map<String, Object> map = new HashMap<String, Object>();
		if (getPath != null) {
			JSONArray operations = JsonUtil.getJSONArray(getPath, "operations");
			JSONObject cont = operations.getJSONObject(0);
			map.put("url", target);
			map.put("methodName", cont.getString("nickname"));
			map.put("chose", cont.getString("method"));
			JSONArray arrs = cont.getJSONArray("parameters");
			List<Map<String, String>> params = new ArrayList<Map<String, String>>();
			for (int idx = 0; idx < arrs.size(); idx++) {
				Map<String, String> p = new HashMap<String, String>();
				JSONObject jo = arrs.getJSONObject(idx);
				p.put("name", JsonUtil.getString(jo, "name"));
				p.put("description", JsonUtil.getString(jo, "description"));
				p.put("defaultValue", JsonUtil.getString(jo, "defaultValue"));
				p.put("required",String.valueOf(JsonUtil.getBoolean(jo,"required")));
				p.put("type", jo.getString("type"));
				p.put("valtype", jo.getString("paramType"));
				params.add(p);
			}

			map.put("paramsSize", params.size() * 2);
			map.put("input", params);

		}

		// TODO=============================
		// for(int ix=0;ix<apis.size();ix++){
		// JSONObject api = JSONObject.fromObject(apis.get(ix));
		// if(apiKey.equals(api.get("path"))){
		// getPath = api;
		// }
		// }
		//// JSONObject getPath = json.getJSONArray("apis")
		//
		// Map<String, Object> map = new HashMap<String, Object>();
		// if (getPath != null) {
		// JSONObject cont = null;
		// if (getPath.has("get")) {
		// cont = getPath.getJSONObject("get");
		// } else {
		// cont = getPath.getJSONObject("post");
		// }
		// map.put("url", target);
		// System.out.println(cont);
		// map.put("methodName", cont.getString("operationId"));
		// JSONArray arrs = cont.getJSONArray("parameters");
		// List<Map<String, String>> params = new ArrayList<Map<String,
		// String>>();
		// for (int idx = 0; idx < arrs.size(); idx++) {
		// Map<String, String> p = new HashMap<String, String>();
		// JSONObject jo = arrs.getJSONObject(idx);
		// p.put("name", jo.getString("name"));
		// p.put("in", jo.getString("in"));
		// p.put("required", jo.getString("required"));
		// p.put("type", jo.getString("type"));
		// p.put("valtype", "0");
		// params.add(p);
		// }
		// map.put("paramsSize", params.size() * 2);
		// map.put("input", params);
		// } else {
		// success = false;
		// rs.put("msg", "此地址无效");
		// }
		rs.put("success", success);
		rs.put("serviceSet", map);
		return rs;
	}

	/**
	 * 获取流程节点的流程变量的选择框 bo变量，流程变量
	 */
	@RequestMapping("flowVarJson")
	@ResponseBody
	public JSONArray flowVarJson(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String defId = RequestUtil.getString(request, "defId");
		String nodeId = RequestUtil.getString(request, "nodeId");
		boolean includeBpmConstants = RequestUtil.getBoolean(request, "includeBpmConstants", false);
		List<IBpmVariableDefine> variables = getAllBpmVariableDef(defId, nodeId);
	//	BoDefPo boList = getBpmDefBo(defId);
		//return getFlowVarJson(boList, variables, includeBpmConstants);
        return null;
	}

	/***
	 * 获取流程定义的所有的BO 数据
	 *
	 * @param defId
	 * @return
	 */
//	private BoDefPo getBpmDefBo(String defId) {
//		IBpmProcDefine<IBpmProcExtendDefine> bpmProcessDefExt = bpmDefineReader.getBpmProcDefine(defId);
//		// if(bpmProcessDefExt == null) return Collections.emptyList();
//		BpmProcExtendDefine defaultBpmProcessDefExt = (BpmProcExtendDefine) bpmProcessDefExt.getBpmProcExtendDefine();
//		// if(defaultBpmProcessDefExt == null) return Collections.emptyList();
//		ProcBoDefine procBoDefine = defaultBpmProcessDefExt.getBoDefine();
//
//		// List<BoDefPo> boList = new ArrayList<BoDefPo>();
//		// for(ProcBoDefine boDef : procBoDefList){
//		BoDefPo boDef = null;
//		if (BeanUtils.isNotEmpty(procBoDefine)) {
//			boDef = boDefRepository.getByCode(procBoDefine.getKey());
//		}
//		// if(BeanUtils.isNotEmpty(bODef)) {
//		// boList.add(bODef);
//		// }
//		// }
//		return boDef;
//	}

	/**
	 * 拼装 流程bo(不包含子表)、 流程变量、流程常量的json 数据 {}
	 **/
//	private JSONArray getFlowVarJson(BoDefPo boDef, List<IBpmVariableDefine> variables, boolean includeBpmConstants) {
//		JSONArray varList = new JSONArray();
//
//		// BO变量
//		String boRootId = UniqueIdUtil.getId();
//		JSONObject boDefRoot = JSONObject.fromObject(
//				"{\"name\":\"业务对象\",\"id\":\"" + boRootId + "\",\"fromType\":\"root\",\"description\":\"业务对象\"}");
//		// for(BoDefPo boDef : boList){
//		if (BeanUtils.isNotEmpty(boDef)) {
//			JSONObject obj = new JSONObject();
//			obj.accumulate("id", boDef.getId());
//			obj.accumulate("parentId", boRootId);
//			obj.accumulate("name", boDef.getName());
//			obj.accumulate("description", boDef.getDesc());
//			obj.accumulate("fromType", "bo");
//			varList.add(obj);
//			for (BoAttributePo attr : boDef.getAttrList()) {
//				if (BoAttributePo.BoAttributeType.BASE.equals(attr.getType())) {
//					JSONObject attrObj = new JSONObject();
//					attrObj.accumulate("id", attr.getId());
//					attrObj.accumulate("parentId", boDef.getId());
//					attrObj.accumulate("name", attr.getName());
//					attrObj.accumulate("description", attr.getDesc());
//					attrObj.accumulate("fromType", "boAttr");
//					attrObj.accumulate("code", boDef.getCode());
//					attrObj.accumulate("dataType", attr.getDataType());
//					varList.add(attrObj);
//				}
//			}
//
//		}
//		// 流程变量
//		String varRootId = UniqueIdUtil.getId();
//		JSONObject varRoot = JSONObject.fromObject(
//				"{\"name\":\"流程变量\",\"id\":\"" + varRootId + "\",\"fromType\":\"root\",\"description\":\"流程变量\"}");
//		for (IBpmVariableDefine variable : variables) {
//			String name = variable.getName(); // 前端流程变量都是 取name， 而名字为description
//			variable.setName(variable.getKey());
//			variable.setDescription(name);
//			JSONObject obj = JSONObject.fromObject(variable);
//			obj.accumulate("id", Math.random() * 10000 + "");
//			obj.accumulate("parentId", varRootId);
//			obj.accumulate("fromType", "var");
//			varList.add(obj);
//		}
//		varList.add(boDefRoot);
//		varList.add(varRoot);
//		// 如果表单变量需要包含流程常量
//		if (includeBpmConstants) {
//			String BpmConstantId = UniqueIdUtil.getId();
//			JSONObject bpmConstantRoot = JSONObject.fromObject("{\"name\":\"流程常量\",\"id\":\"" + BpmConstantId
//					+ "\",\"fromType\":\"root\",\"description\":\"流程变量\"}");
//
//			JSONObject bmpnInstId = JSONObject.fromObject("{\"name\":\"" + BpmConstants.PROCESS_INST_ID
//					+ "\",\"id\":\"1212\"," + "\"parentId\":\"" + BpmConstantId
//					+ "\",\"fromType\":\"bpmConstants\",\"description\":\"流程实例ID\",\"dataType\":\"string\"}");
//			JSONObject bussinessKey = JSONObject.fromObject("{\"name\":\"" + BpmConstants.BPM_FLOW_KEY
//					+ "\",\"id\":\"asdfad\"," + "\"parentId\":\"" + BpmConstantId
//					+ "\",\"fromType\":\"bpmConstants\",\"description\":\"流程定义Key\",\"dataType\":\"string\"}");
//			JSONObject startUser = JSONObject.fromObject("{\"name\":\"" + BpmConstants.START_USER
//					+ "\",\"id\":\"asdfasdfass\"," + "\"parentId\":\"" + BpmConstantId
//					+ "\",\"fromType\":\"bpmConstants\",\"description\":\"发起人\",\"dataType\":\"string\"}");
//
//			varList.add(bmpnInstId);
//			varList.add(bussinessKey);
//			varList.add(startUser);
//			varList.add(bpmConstantRoot);
//		}
//		return varList;
//	}
	/**
	 *
	 * 从前台拿到数据，获取后台响应状态及结果
	 *
	 * @param request
	 * @param reponse
	 * @return
	 * @throws Exception
	 */
//	@SuppressWarnings("unchecked")
//	@RequestMapping(value = "getRestApiResult", method = RequestMethod.POST)
//	@ResponseBody
//	public Map<String, Object> getRestApiResult(HttpServletRequest request, HttpServletResponse reponse)
//			throws Exception {
//		WebAPIResult result = new WebAPIResult();
//		CloseableHttpResponse resp = null;
//		CloseableHttpClient apiclient = HttpClients.createDefault();
//		Map<String, Object> rs = new HashMap<String, Object>();
//		//boolean success = true;
//		//String access_token = "";
//		String getToken = RequestUtil.getString(request, "adress");
//		String detail = RequestUtil.getString(request, "params", "{}");
//		JSONObject obj = JSONObject.fromObject(detail);
//		Map<String, Object> map = (Map<String, Object>) JSONObject.toBean(obj, Map.class);
//		URIBuilder uriBuilder = new URIBuilder();
//		for (Map.Entry<String, Object> entry : map.entrySet()) {
//			String key = entry.getKey();
//			String value = entry.getValue().toString();
//			if (value.isEmpty() || value == null) {
//
//			} else {
//				uriBuilder.addParameter(key, value);
//			}
//		}
//		HttpGet get = new HttpGet(getToken + uriBuilder.build());
//		JSONObject res = new JSONObject();
//		try {
//			resp = apiclient.execute(get);
//			if (resp.getStatusLine().getStatusCode() == HttpStatus.OK.value()) {
//
//
//				 HttpEntity entity = resp.getEntity();
//				  res = JSONObject.fromObject(EntityUtils.toString(entity, "utf-8"));
//				 /** access_token = res.has("access_token") ?
//				 * res.get("access_token").toString() : "";
//				 */
//				result.setResult(WebAPIResult.SUCCESS);
//				result.setMessage(res.getString("message"));
//				rs.put("status", resp.getStatusLine().getStatusCode());
//				rs.put("result", result);
//				rs.put("sendurl", get);
//				return rs;
//			} else {
//				result.setResult(WebAPIResult.FAIL);
//				result.setMessage(res.getString("message"));
//				rs.put("status", resp.getStatusLine().getStatusCode());
//				rs.put("result", result);
//				rs.put("sendurl", get);
//				return rs;
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//			result.setResult(WebAPIResult.ERROR);
//			result.setMessage("请求出现错误！");
//			rs.put("status", resp.getStatusLine().getStatusCode());
//			rs.put("result", result);
//			rs.put("sendurl", get);
//			return rs;
//		} finally {
//			try {
//				if (apiclient != null) {
//					apiclient.close();
//				}
//				if (resp != null) {
//					resp.close();
//				}
//			} catch (IOException e) {
//				throw new BaseException("关闭操作错误！");
//			}
//		}
//	}

}
