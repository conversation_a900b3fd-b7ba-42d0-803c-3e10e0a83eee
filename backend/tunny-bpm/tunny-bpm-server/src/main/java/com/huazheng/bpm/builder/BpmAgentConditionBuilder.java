package com.huazheng.bpm.builder;


import com.huazheng.bpm.entity.bpm.BpmAgentConditionPo;
import com.huazheng.bpm.service.IPartyEntityService;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.JacksonUtil;

import java.util.List;


/**
 * 流程代理定义对象构建工具
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年3月31日-上午10:35:01
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmAgentConditionBuilder {

	/**
	 * 构建流程代理条件对象展示属性
	 *
	 * @param bpmAgentList
	 */
	public static void build(List<BpmAgentConditionPo> bpmAgentConditionList){
		if(BeanUtils.isEmpty(bpmAgentConditionList)){
			return;
		}

		for(BpmAgentConditionPo po : bpmAgentConditionList){
			build(po);
		}
	}

	/**
	 * 构建流程代理条件对象展示属性
	 *
	 * @param bpmAgent
	 */
	public static void build(BpmAgentConditionPo bpmAgentCondition){
		if(BeanUtils.isEmpty(bpmAgentCondition)){
			return;
		}

		IPartyEntityService entityService = AppUtil.getBean(IPartyEntityService.class);
		String entity = entityService.getByIdJson(bpmAgentCondition.getAgenterId());
		if(JacksonUtil.isJsonObject(entity)){
			bpmAgentCondition.setAgenterName(JacksonUtil.getString(entity, "name"));
		}
	}
}
