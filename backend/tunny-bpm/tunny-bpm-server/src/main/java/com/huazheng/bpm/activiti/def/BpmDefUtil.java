package com.huazheng.bpm.activiti.def;

import com.huazheng.bpm.activiti.engine.ProcessEngine;
import com.huazheng.bpm.activiti.engine.ProcessEngineConfiguration;
import com.huazheng.bpm.activiti.engine.RepositoryService;
import com.huazheng.bpm.activiti.engine.impl.RepositoryServiceImpl;
import com.huazheng.bpm.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import com.huazheng.bpm.activiti.engine.impl.pvm.PvmActivity;
import com.huazheng.bpm.activiti.engine.impl.pvm.PvmTransition;
import com.huazheng.bpm.activiti.engine.impl.pvm.process.ActivityImpl;
import com.huazheng.bpm.activiti.engine.impl.pvm.process.TransitionImpl;
import com.huazheng.bpm.activiti.exception.TransFormException;
import com.huazheng.bpm.util.base.Dom4jUtil;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class BpmDefUtil {

	/**
	 * 将通过设计器设计的流程定义xml添加监听器设置。
	 * @param id		流程定义ID
	 * @param name		流程定义名称
	 * @param xml		流程定义xml。
	 * @return			转化过的xml。
	 * @throws Exception
	 */
	public static String transBpmDef(String id, String name, String xml) {
		try{
			ClassLoader  loader  =  Thread.currentThread().getContextClassLoader();

			String transform = "xml/transform.xsl";
			InputStream  is=loader.getResourceAsStream(transform);
			if(is==null){
				is=BpmDefUtil.class.getResourceAsStream(transform);
			}

			Map<String, String> map =new HashMap<String, String>();
			map.put("id", id);
			map.put("name", name);
			String result= Dom4jUtil.transXmlByXslt(xml, is, map);
			result = result.replace("&lt;", "<").replace("&gt;", ">")
					.replace("xmlns=\"\"", "").replace("&amp;", "&");

			Pattern regex = Pattern.compile("name=\".*?\"");
			Matcher regexMatcher = regex.matcher(result);
			while (regexMatcher.find()) {
				String strReplace = regexMatcher.group(0);
				String strReplaceWith = strReplace.replace("&", "&amp;")
						.replace("<", "&lt;").replace(">", "&gt;");
				result = result.replace(strReplace, strReplaceWith);
			}
			return result;
		}
		catch(Exception ex){
			throw new TransFormException("转换流程定义出错", ex);
		}
	}

	/**
	 * 将节点之后的节点删除然后指向新的节点。
	 * @param actDefId			流程定义ID
	 * @param nodeId			流程节点ID
	 * @param aryDestination	需要跳转的节点
	 * @return Map<String,Object> 返回节点和需要恢复节点的集合。
	 */
	@SuppressWarnings("unchecked")
	public static Map<String,Object> prepare(String actDefId,String nodeId,String[] aryDestination){
		Map<String,Object> map=new HashMap<String, Object>();

	//	RepositoryService repositoryService=AppUtil.getBean(RepositoryService.class);
		ProcessEngineConfiguration processEngineConfiguration = AppUtil.getBean(ProcessEngineConfiguration.class);
		RepositoryService repositoryService =	processEngineConfiguration.getRepositoryService();

		//修改流程定义
		ProcessDefinitionEntity processDefinition = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService).getDeployedProcessDefinition(actDefId);

		ActivityImpl curAct= processDefinition.findActivity(nodeId);
		List<PvmTransition> outTrans= curAct.getOutgoingTransitions();
		List<PvmTransition> cloneOutTrans=(List<PvmTransition>) BeanUtils.cloneObject(outTrans);
		map.put("outTrans", cloneOutTrans);

		/**
		 * 解决通过选择自由跳转指向同步节点导致的流程终止的问题。
		 * 在目标节点中删除指向自己的流转。
		 */
		for(Iterator<PvmTransition> it=outTrans.iterator();it.hasNext();){
			PvmTransition transition=it.next();
			PvmActivity activity= transition.getDestination();
			List<PvmTransition> inTrans= activity.getIncomingTransitions();
			for(Iterator<PvmTransition> itIn=inTrans.iterator();itIn.hasNext();){
				PvmTransition inTransition=itIn.next();
				if(inTransition.getSource().getId().equals(curAct.getId())){
					itIn.remove();
				}
			}
		}

		curAct.getOutgoingTransitions().clear();

		if(aryDestination!=null && aryDestination.length>0){
			for(String dest:aryDestination){
				//创建一个连接
				ActivityImpl destAct= processDefinition.findActivity(dest);
				TransitionImpl transitionImpl = curAct.createOutgoingTransition();
				transitionImpl.setDestination(destAct);
			}
		}

		map.put("activity", curAct);

		return map;

	}

	/**
	 * 将临时节点清除掉，加回原来的节点。
	 * @param map
	 * void
	 */
	@SuppressWarnings("unchecked")
	public static void restore(Map<String,Object> map){
		ActivityImpl curAct=(ActivityImpl) map.get("activity");
		List<PvmTransition> outTrans=(List<PvmTransition>) map.get("outTrans");
		curAct.getOutgoingTransitions().clear();
		curAct.getOutgoingTransitions().addAll(outTrans);
	}

}
