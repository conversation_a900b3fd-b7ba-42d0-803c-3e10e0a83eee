//package com.huazheng.bpm.controller;
//
//import com.huazheng.bpm.util.base.ContextFactory;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//
//import javax.xml.bind.JAXBContext;
//
//@Controller
//public class TestController {
//
//    @RequestMapping("test")
//    public String test(){
//        return "/platform/bpmn/bpmModelerEditor";
//    }
//    @RequestMapping("list")
//    public String list(){
//        return "/platform/bpmn/bpmDefineList";
//    }
//
//    @RequestMapping("jaxb")
//    public void jsxb(){
//       // JAXBContext jctx = ContextFactory.newInstance(classes);
//    }
//
//}
