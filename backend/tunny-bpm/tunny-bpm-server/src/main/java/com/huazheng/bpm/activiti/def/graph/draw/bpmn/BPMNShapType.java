package com.huazheng.bpm.activiti.def.graph.draw.bpmn;

/**
 * 节点类型。
 *
 * <pre>
 * 构建组：ibps-bpmn-act
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年3月23日-上午9:17:56
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum BPMNShapType {

	TASK("task"),
	USER_TASK("userTask"),
	SCRIPT_TASK("scriptTask"),
	SERVICE_TASK("serviceTask"),
	BUSINESS_RULE_TASK("bnusinessRuleTask"),
	MANUAL_TASK("manualTask"),

	SEND_TASK("sendTask"),
	RECEIVE_TASK("receiveTask"),
	SUB_PROCESS("subProcess"),
	CALL_ACTIVITY("callActivity"),
	AD_HOC_SUB_PROCESS("AdHocSubProcess"),

	H_POOL("hPool"),//水平vertical
	V_POOL("vPool"),//垂直vertical
	H_LANE("hLane"),
	V_LANE("vLane"),

	START_EVENT("startEvent"),
	END_EVENT("endEvent"),
	CANCEL_EVENT("cancelEvent"),
	ERROR_EVENT("errorEvent"),
	INTERMEDIATE_CATCH_EVENT("intermediateCatchEvent"),

	PARALLEL_GATEWAY("parallelGateway"),
	EXCLUSIVE_GATEWAY("exclusiveGateway"),
	INCLUSIVE_GATEWAY("inclusiveGateway"),
	COMPLEX_GATEWAY("complexGateway"),
	EVENT_BASED_GATEWAY("eventBasedGateway"),

	TRANSACTION("transaction"),
	TEXT_ANNOTATION("textAnnotation"),
	UNKNOW_TYPE("unknowType");


	private String key;

	BPMNShapType(String key) {
		this.key = key;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

}
