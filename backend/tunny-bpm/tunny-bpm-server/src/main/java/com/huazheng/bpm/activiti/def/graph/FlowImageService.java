package com.huazheng.bpm.activiti.def.graph;

import com.huazheng.bpm.activiti.def.graph.draw.activiti.ProcessDiagramGenerator;
import com.huazheng.bpm.model.base.FlowImageStyle;
import com.huazheng.bpm.service.NatProcImageService;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Map;


/**
 * 构建流程图sevrice。
 *
 * <pre>
 *
 * 构建组：ibps-bpmn-act
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2017年3月21日-下午4:55:49
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
@Service("flowImageService")
public class FlowImageService implements NatProcImageService {
	@Override
	public InputStream getProcessImageByBpmnXml(String bpmnXml) {
		return getProcessImageByBpmnXml(bpmnXml, null);
	}

	@Override
	public InputStream getProcessImageByBpmnXml(String bpmnXml, Map<String, FlowImageStyle> styleMap) {
		return ProcessDiagramGenerator.generatePngDiagram(bpmnXml, styleMap);
	}
}
