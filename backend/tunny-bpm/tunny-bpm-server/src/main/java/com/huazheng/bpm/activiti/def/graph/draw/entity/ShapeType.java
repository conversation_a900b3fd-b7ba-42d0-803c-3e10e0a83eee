package com.huazheng.bpm.activiti.def.graph.draw.entity;

/**
 * 程序设计器 连接类型。
 *
 * <pre>
 * 构建组：ibps-bpmn-activiti
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月29日-下午3:17:11
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum ShapeType {
	STRAIGHT("straight"),
	FREE("free"),
	ORTHOGONAL("orthogonal"),
	OBLIQUE("oblique");

	private String text;

	ShapeType(String text) {
		this.text = text;
	}

	public String getText() {
		return this.text;
	}

	@Override
	public String toString() {
		return this.text;
	}

	public static ShapeType fromString(String text) {
		if (text != null) {
			for (ShapeType type : ShapeType.values()) {
				if (text.equalsIgnoreCase(type.text)) {
					return type;
				}
			}
		}
		return null;
	}
}
