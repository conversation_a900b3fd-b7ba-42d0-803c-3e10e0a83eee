package com.huazheng.bpm.builder;


import com.huazheng.bpm.entity.bpm.BpmTaskChangeAssignPo;
import com.huazheng.bpm.service.IPartyEntityService;
import com.huazheng.bpm.util.core.AppUtil;
import com.huazheng.bpm.util.core.BeanUtils;
import com.huazheng.bpm.util.core.JacksonUtil;

import java.util.List;


/**
 * 流程任务变更对象构建工具
 * <pre>
 * 构建组：ibps-bpmn-biz
 * 作者：eddy
 * 邮箱：<EMAIL>
 * 日期：2017年3月31日-上午9:14:55
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public class BpmTaskChangeAssignBuilder {

	/**
	 * 构建流程任务变更对象展示属性
	 *
	 * @param bpmAgentList
	 */
	public static void build(List<BpmTaskChangeAssignPo> bpmTaskChangeAssignList){
		if(BeanUtils.isEmpty(bpmTaskChangeAssignList)){
			return;
		}

		for(BpmTaskChangeAssignPo po : bpmTaskChangeAssignList){
			build(po);
		}
	}

	/**
	 * 构建流程任务变更对象展示属性
	 *
	 * @param bpmAgent
	 */
	public static void build(BpmTaskChangeAssignPo bpmTaskChangeAssign){
		if(BeanUtils.isEmpty(bpmTaskChangeAssign)){
			return;
		}

		IPartyEntityService entityService = AppUtil.getBean(IPartyEntityService.class);
		String entity = entityService.getByIdJson(bpmTaskChangeAssign.getExecutor());
		if(JacksonUtil.isJsonObject(entity)){
			bpmTaskChangeAssign.setExecutorName(JacksonUtil.getString(entity, "name"));
		}
	}
}
