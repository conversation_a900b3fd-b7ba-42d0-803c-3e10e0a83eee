package com.huazheng.bpm.activiti.def.graph.draw.entity;

/**
 * 生成图片类型。
 *
 * <pre>
 * 构建组：ibps-bpmn-activiti
 * 作者：hugh zhuang
 * 邮箱：<EMAIL>
 * 日期：2016年11月29日-下午3:15:08
 * 版权：广州流辰信息技术有限公司版权所有
 * </pre>
 */
public enum ImageType {
	PNG("png"), JPG("jpg");

	private String key;

	ImageType(String key) {
		this.key = key;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	@Override
	public String toString() {
		return key;
	}
}
