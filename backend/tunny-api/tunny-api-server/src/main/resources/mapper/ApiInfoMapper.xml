<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huazheng.tunny.api.mapper.ApiInfoMapper">
    <resultMap id="BaseResultMap" type="com.huazheng.tunny.api.entity.ApiInfo">
        <result property="apiId" column="api_id"/> <!-- 接口ID -->
        <result property="apiCode" column="api_code"/> <!-- 接口编码 -->
        <result property="apiName" column="api_name"/> <!-- 接口名称 -->
        <result property="apiCategory" column="api_category"/> <!-- 接口分类:default-默认分类 -->
        <result property="apiDesc" column="api_desc"/> <!-- 资源描述 -->
        <result property="requestMethod" column="request_method"/> <!-- 请求方式 -->
        <result property="contentType" column="content_type"/> <!-- 响应类型 -->
        <result property="serviceId" column="service_id"/> <!-- 服务ID -->
        <result property="path" column="path"/> <!-- 请求路径 -->
        <result property="priority" column="priority"/> <!-- 优先级 -->
        <result property="status" column="status"/> <!-- 状态:0-无效 1-有效 -->
        <result property="createTime" column="create_time"/> <!--  -->
        <result property="updateTime" column="update_time"/> <!--  -->
        <result property="isPersist" column="is_persist"/> <!-- 保留数据0-否 1-是 不允许删除 -->
        <result property="isAuth" column="is_auth"/> <!-- 是否需要认证: 0-无认证 1-身份认证 默认:1 -->
        <result property="isOpen" column="is_open"/> <!-- 是否公开: 0-内部的 1-公开的 -->
        <result property="className" column="class_name"/> <!-- 类名 -->
        <result property="methodName" column="method_name"/> <!-- 方法名 -->
    </resultMap>


    <sql id="selectBaseApiVo">
        select api_id, api_code, api_name, api_category, api_desc, request_method, content_type, service_id, path, priority, status, create_time, update_time, is_persist, is_auth, is_open, class_name, method_name from api_list
    </sql>

    <insert id="insertOnDuplicate" parameterType="com.huazheng.tunny.api.entity.ApiInfo">
        insert into api_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="apiId != null  ">api_id,</if>
            <if test="apiCode != null  and apiCode != ''  ">api_code,</if>
            <if test="apiName != null  and apiName != ''  ">api_name,</if>
            <if test="apiCategory != null  and apiCategory != ''  ">api_category,</if>
            <if test="apiDesc != null  and apiDesc != ''  ">api_desc,</if>
            <if test="requestMethod != null  and requestMethod != ''  ">request_method,</if>
            <if test="contentType != null  and contentType != ''  ">content_type,</if>
            <if test="serviceId != null  and serviceId != ''  ">service_id,</if>
            <if test="path != null  and path != ''  ">path,</if>
            <if test="priority != null  ">priority,</if>
            <if test="status != null  ">status,</if>
            <if test="createTime != null  ">create_time,</if>
            <if test="updateTime != null  ">update_time,</if>
            <if test="isPersist != null  ">is_persist,</if>
            <if test="isAuth != null  ">is_auth,</if>
            <if test="isOpen != null  ">is_open,</if>
            <if test="className != null  and className != ''  ">class_name,</if>
            <if test="methodName != null  and methodName != ''  ">method_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="apiId != null  ">#{apiId},</if>
            <if test="apiCode != null  and apiCode != ''  ">#{apiCode},</if>
            <if test="apiName != null  and apiName != ''  ">#{apiName},</if>
            <if test="apiCategory != null  and apiCategory != ''  ">#{apiCategory},</if>
            <if test="apiDesc != null  and apiDesc != ''  ">#{apiDesc},</if>
            <if test="requestMethod != null  and requestMethod != ''  ">#{requestMethod},</if>
            <if test="contentType != null  and contentType != ''  ">#{contentType},</if>
            <if test="serviceId != null  and serviceId != ''  ">#{serviceId},</if>
            <if test="path != null  and path != ''  ">#{path},</if>
            <if test="priority != null  ">#{priority},</if>
            <if test="status != null  ">#{status},</if>
            <if test="createTime != null  ">#{createTime},</if>
            <if test="updateTime != null  ">#{updateTime},</if>
            <if test="isPersist != null  ">#{isPersist},</if>
            <if test="isAuth != null  ">#{isAuth},</if>
            <if test="isOpen != null  ">#{isOpen},</if>
            <if test="className != null  and className != ''  ">#{className},</if>
            <if test="methodName != null  and methodName != ''  ">#{methodName},</if>
        </trim>
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides=",">
            <if test="apiCode != null">api_code = VALUES(api_code),</if>
            <if test="apiName != null">api_name = VALUES(api_name),</if>
            <if test="apiCategory != null">api_category = VALUES(api_category),</if>
            <if test="apiDesc != null">api_desc = VALUES(api_desc),</if>
            <if test="requestMethod != null">request_method = VALUES(request_method),</if>
            <if test="contentType != null">content_type = VALUES(content_type),</if>
            <if test="serviceId != null">service_id = VALUES(service_id),</if>
            <if test="path != null">path = VALUES(path),</if>
            <if test="priority != null">priority = VALUES(priority),</if>
            <if test="status != null">status = VALUES(status),</if>
            <if test="createTime != null">create_time = VALUES(create_time),</if>
            <if test="updateTime != null">update_time = VALUES(update_time),</if>
            <if test="isPersist != null">is_persist = VALUES(is_persist),</if>
            <if test="isAuth != null">is_auth = VALUES(is_auth),</if>
            <if test="isOpen != null">is_open = VALUES(is_open),</if>
            <if test="className != null">class_name = VALUES(class_name),</if>
            <if test="methodName != null">method_name = VALUES(method_name),</if>
        </trim>
    </insert>

    <select id="apiIndicator" resultType="java.util.Map">
        SELECT
        (SELECT COUNT(1) FROM api_list) AS totalApi,
        (SELECT COUNT(1) FROM api_app) AS totalApp,
        (SELECT COUNT(1) FROM gateway_route) AS totalServices
    </select>

    <select id="callStatistics" resultType="java.util.Map">
        SELECT
            COUNT(1) AS apicall,
            SUM(IF(http_status='200', 1, 0))/COUNT(1) AS successRate,
            SUM(use_time)/COUNT(1) AS avgTimConsum,
            MAX(use_time) AS maxTimeConsum
        FROM
            gateway_access_logs
        WHERE
            request_time >= date_sub(now(), INTERVAL #{day} DAY)
    </select>

    <select id="callStatistics_topList" resultType="java.util.Map">
        SELECT
            COUNT(1) AS apiTotal,
            log.path AS apiPath,
            api.api_name AS apiName
        FROM
            gateway_access_logs AS log
            LEFT JOIN gateway_route route ON route.service_id = log.service_id
            LEFT JOIN api_list api ON api.path = SUBSTR(log.path FROM LOCATE(SUBSTR(route.predicates_path FROM 1 FOR LOCATE("/**", route.predicates_path)-1), log.path)+ LENGTH(SUBSTR(route.predicates_path FROM 1 FOR LOCATE("/**", route.predicates_path)-1)))  AND api.service_id = log.service_id
        WHERE
            request_time >= date_sub(now(), INTERVAL #{day} DAY)
        GROUP BY log.path
        ORDER BY apiTotal DESC
        LIMIT 0,10
    </select>

    <select id="callStatistics_serviceTotalList" resultType="java.util.Map">
        SELECT * FROM (SELECT COUNT(1) AS `value`, service_id AS `name` FROM `api_list` GROUP BY service_id ORDER BY `value` DESC LIMIT 0,9) aa

        UNION ALL

        SELECT COUNT(1) AS `value`, '其他' AS `name` FROM `api_list`
        WHERE service_id NOT IN (
            SELECT * FROM (SELECT service_id FROM `api_list` GROUP BY service_id ORDER BY COUNT(1) DESC LIMIT 0,9) tmp
        )
        GROUP BY service_id
    </select>

    <select id="apiTraffic" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        timeA.b AS xTime, COUNT(log.access_id) AS yCount
        FROM (
            SELECT DATE_FORMAT(FROM_UNIXTIME(CAST(#{endTime} AS SIGNED)/1000),'%Y-%m-%d %H:') AS a , DATE_FORMAT(FROM_UNIXTIME(CAST(#{endTime} AS SIGNED)/1000), '%H:00') AS b
            <foreach collection="hours" item="i" index="index">
            UNION SELECT DATE_FORMAT(date_sub(FROM_UNIXTIME(CAST(#{endTime} AS SIGNED)/1000), interval ${i} hour),'%Y-%m-%d %H:'), DATE_FORMAT(date_sub(FROM_UNIXTIME(CAST(#{endTime} AS SIGNED)/1000), interval ${i} hour), '%H:00')
            </foreach>
            ORDER BY a
        ) AS timeA
        LEFT JOIN gateway_access_logs AS log ON log.request_time LIKE CONCAT(timeA.a,'%')
        GROUP BY timeA.a
    </select>
</mapper>
