<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huazheng.tunny.api.mapper.BaseAuthorityAppMapper">
    <resultMap id="BaseResultMap" type="com.huazheng.tunny.api.entity.BaseAuthorityApp">
        <result column="authority_id" jdbcType="BIGINT" property="authorityId"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
    </resultMap>

    <resultMap id="Authority" type="com.huazheng.tunny.api.common.security.Authority">
        <result column="authority_id" jdbcType="BIGINT" property="authorityId"/>
        <result column="authority" jdbcType="VARCHAR" property="authority"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
        <result column="owner" jdbcType="VARCHAR" property="owner"/>
        <result column="api_id" jdbcType="VARCHAR" property="apiId"/>
    </resultMap>

    <select id="selectAuthorityByApp" resultMap="Authority">
    SELECT
      a.authority_id,
      a.authority,
      ap.expire_time,
      'app' as owner
    FROM
      api_authority_app AS ap
        INNER JOIN api_authority AS a ON ap.authority_id = a.authority_id
    WHERE
      ap.app_id = #{appId}
      and  a.status = 1
  </select>

    <select id="selectAuthorityByApp2" resultType="java.util.Map">
    SELECT
      CONCAT(a.authority_id,'') AS authority_id,
      a.authority,
      ap.expire_time,
      CONCAT(a.api_id,'') AS api_id,
      'app' as owner
    FROM
      api_authority_app AS ap
        INNER JOIN api_authority AS a ON ap.authority_id = a.authority_id
    WHERE
      ap.app_id = #{appId}
      and  a.status = 1
  </select>
</mapper>
