<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huazheng.tunny.api.mapper.GatewayRateLimitMapper">
  <resultMap id="BaseResultMap" type="com.huazheng.tunny.api.entity.GatewayRateLimit">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="policy_id" jdbcType="BIGINT" property="policyId" />
    <result column="policy_name" jdbcType="VARCHAR" property="policyName" />
    <result column="policy_type" jdbcType="VARCHAR" property="policyType" />
    <result column="limit_quota" jdbcType="BIGINT" property="limitQuota" />
    <result column="interval_unit" jdbcType="VARCHAR" property="intervalUnit" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>
