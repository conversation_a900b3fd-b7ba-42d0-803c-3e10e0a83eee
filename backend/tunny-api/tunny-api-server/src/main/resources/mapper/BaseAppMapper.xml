<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huazheng.tunny.api.mapper.BaseAppMapper">
    <!-- 表名 -->
    <sql id="t_name">api_app</sql>
    <!-- 别名 -->
    <sql id="t_alias">api_app as app</sql>
    <!--外键表关联定义 -->
<!--    <sql id="join">-->
<!--        <if test="ew.aliasMap.user != null">-->
<!--            <![CDATA[left join base_user ${ew.aliasMap.user} on ${ew.aliasMap.user}.user_id = app.user_id]]>-->
<!--        </if>-->
<!--    </sql>-->
    <!-- 自定义分页 -->
    <select id="pageList" resultType="com.huazheng.tunny.api.common.mybatis.EntityMap">
        select ${ew.select} FROM
        <include refid="t_alias"/>
<!--        <include refid="join"/>-->
        ${ew.customSqlSegment}
    </select>
</mapper>
