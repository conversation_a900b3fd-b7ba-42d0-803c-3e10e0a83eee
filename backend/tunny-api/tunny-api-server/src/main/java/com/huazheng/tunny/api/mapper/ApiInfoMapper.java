package com.huazheng.tunny.api.mapper;

import com.huazheng.tunny.api.common.mybatis.base.mapper.SuperMapper;
import com.huazheng.tunny.api.entity.ApiInfo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


/**
 *
 */
@Repository
public interface ApiInfoMapper extends SuperMapper<ApiInfo> {

    void insertOnDuplicate(ApiInfo apiInfo);

    Map apiIndicator();

    Map<String, Object> callStatistics(Map map);

    List callStatistics_topList(Map map);

    List callStatistics_serviceTotalList(Map map);

    List<Map<String, Object>> apiTraffic(Map params);
}
