package com.huazheng.tunny.api.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.huazheng.tunny.api.common.PageParams;
import com.huazheng.tunny.api.common.constants.BaseConstants;
import com.huazheng.tunny.api.common.exception.OpenAlertException;
import com.huazheng.tunny.api.common.mybatis.base.service.impl.BaseServiceImpl;
import com.huazheng.tunny.api.constants.ResourceType;
import com.huazheng.tunny.api.entity.ApiInfo;
import com.huazheng.tunny.api.mapper.ApiInfoMapper;
import com.huazheng.tunny.api.service.ApiInfoService;
import com.huazheng.tunny.api.service.BaseAuthorityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

/**
 *
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ApiInfoServiceImpl extends BaseServiceImpl<ApiInfoMapper, ApiInfo> implements ApiInfoService {

    @Autowired
    private ApiInfoMapper apiInfoMapper;
    @Autowired
    private BaseAuthorityService baseAuthorityService;

    /**
     * 分页查询
     *
     * @param pageParams
     * @return
     */
    @Override
    public IPage<ApiInfo> findListPage(PageParams pageParams) {
        ApiInfo query = pageParams.mapToObject(ApiInfo.class);
        QueryWrapper<ApiInfo> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .like(ObjectUtils.isNotEmpty(query.getPath()), ApiInfo::getPath, query.getPath())
                .like(ObjectUtils.isNotEmpty(query.getApiName()), ApiInfo::getApiName, query.getApiName())
                .like(ObjectUtils.isNotEmpty(query.getApiCode()), ApiInfo::getApiCode, query.getApiCode())
                .eq(ObjectUtils.isNotEmpty(query.getServiceId()), ApiInfo::getServiceId, query.getServiceId())
                .eq(ObjectUtils.isNotEmpty(query.getStatus()), ApiInfo::getStatus, query.getStatus())
                .eq(ObjectUtils.isNotEmpty(query.getIsAuth()), ApiInfo::getIsAuth, query.getIsAuth());
        queryWrapper.orderByDesc("create_time");
        return apiInfoMapper.selectPage(pageParams, queryWrapper);
    }

    /**
     * 查询列表
     *
     * @return
     */
    @Override
    public List<ApiInfo> findAllList(String serviceId) {
        QueryWrapper<ApiInfo> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ObjectUtils.isNotEmpty(serviceId), ApiInfo::getServiceId, serviceId);
        List<ApiInfo> list = apiInfoMapper.selectList(queryWrapper);
        return list;
    }

    /**
     * 根据主键获取接口
     *
     * @param apiId
     * @return
     */
    @Override
    public ApiInfo getApi(Long apiId) {
        return apiInfoMapper.selectById(apiId);
    }


    /**
     * 检查接口编码是否存在
     *
     * @param apiCode
     * @return
     */
    @Override
    public Boolean isExist(String apiCode) {
        QueryWrapper<ApiInfo> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ApiInfo::getApiCode, apiCode);
        int count = getCount(queryWrapper);
        return count > 0 ? true : false;
    }

    /**
     * 添加接口
     *
     * @param api
     * @return
     */
    @Override
    public void addApi(ApiInfo api) {
        if (isExist(api.getApiCode())) {
            throw new OpenAlertException(String.format("%s编码已存在!", api.getApiCode()));
        }
        if (api.getPriority() == null) {
            api.setPriority(0);
        }
        if (api.getStatus() == null) {
            api.setStatus(BaseConstants.ENABLED);
        }
        if (api.getApiCategory() == null) {
            api.setApiCategory(BaseConstants.DEFAULT_API_CATEGORY);
        }
        if (api.getIsPersist() == null) {
            api.setIsPersist(0);
        }
        if (api.getIsAuth() == null) {
            api.setIsAuth(0);
        }
        api.setCreateTime(new Date());
        api.setUpdateTime(api.getCreateTime());
        apiInfoMapper.insert(api);
        // 同步权限表里的信息
        baseAuthorityService.saveOrUpdateAuthority(api.getApiId(), ResourceType.api);
    }

    /**
     * 修改接口
     *
     * @param api
     * @return
     */
    @Override
    public void updateApi(ApiInfo api) {
        ApiInfo saved = getApi(api.getApiId());
        if (saved == null) {
            throw new OpenAlertException("信息不存在!");
        }
        if (!saved.getApiCode().equals(api.getApiCode())) {
            // 和原来不一致重新检查唯一性
            if (isExist(api.getApiCode())) {
                throw new OpenAlertException(String.format("%s编码已存在!", api.getApiCode()));
            }
        }
        if (api.getPriority() == null) {
            api.setPriority(0);
        }
        if (api.getApiCategory() == null) {
            api.setApiCategory(BaseConstants.DEFAULT_API_CATEGORY);
        }
        api.setUpdateTime(new Date());
        apiInfoMapper.updateById(api);
        // 同步权限表里的信息
        baseAuthorityService.saveOrUpdateAuthority(api.getApiId(), ResourceType.api);
    }

    /**
     * 查询接口
     *
     * @param apiCode
     * @return
     */
    @Override
    public ApiInfo getApi(String apiCode) {
        QueryWrapper<ApiInfo> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ApiInfo::getApiCode, apiCode);
        return apiInfoMapper.selectOne(queryWrapper);
    }


    /**
     * 移除接口
     *
     * @param apiId
     * @return
     */
    @Override
    public void removeApi(Long apiId) {
        ApiInfo api = getApi(apiId);
        if (api != null && api.getIsPersist().equals(BaseConstants.ENABLED)) {
            throw new OpenAlertException(String.format("保留数据,不允许删除"));
        }
//        baseAuthorityService.removeAuthority(apiId, ResourceType.api);
        apiInfoMapper.deleteById(apiId);
    }

    /**
     * 获取数量
     *
     * @param queryWrapper
     * @return
     */
    @Override
    public int getCount(QueryWrapper<ApiInfo> queryWrapper) {
        return apiInfoMapper.selectCount(queryWrapper);
    }

    /**
     * 接口扫描
     *
     * @param list
     * @return
     */
    @Override
    public void nterfaceScanning(List<Map<String, String>> list) {

        for (Map<String, String> map : list) {
            ApiInfo api = null;
            try {
                api = BeanUtil.mapToBean(map, ApiInfo.class, true);
                api.setCreateTime(new Date());
                api.setUpdateTime(api.getCreateTime());
                apiInfoMapper.insert(api);
            } catch (Exception e) {
                if (api != null) {
                    QueryWrapper<ApiInfo> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("api_code", api.getApiCode());
                    apiInfoMapper.update(api, queryWrapper);
                }
            }
        }
    }

    @Override
    public Map apiIndicator() {
        return apiInfoMapper.apiIndicator();
    }

    @Override
    public Map callStatistics(Map map) {
        Map<String, Object> res = apiInfoMapper.callStatistics(map);
        res.put("topList", apiInfoMapper.callStatistics_topList(map));
        res.put("serviceTotalList", apiInfoMapper.callStatistics_serviceTotalList(map));
        return res;
    }

    @Override
    public List apiTraffic(Map map) {
        Map<String, Object> params = new HashMap<>();
        int hour = 0;
        //查询历史时长（小时）
        if (map.get("hour") != null) {
            hour = Integer.valueOf((String) map.get("hour"));
        }else{
            return new ArrayList();
        }
        List<Integer> hours = new ArrayList<>();
        for (int i = 1; i <= hour; i++) {
            hours.add(i);
        }
        params.put("hours", hours);

        if (map.get("endTime") != null) {
            params.put("endTime", map.get("endTime"));
        }else{
            params.put("endTime", LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
        return apiInfoMapper.apiTraffic(params);
    }
}
