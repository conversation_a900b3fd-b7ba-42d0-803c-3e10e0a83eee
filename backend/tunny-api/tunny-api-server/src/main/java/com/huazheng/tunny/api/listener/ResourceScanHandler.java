package com.huazheng.tunny.api.listener;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.huazheng.tunny.api.entity.ApiInfo;
import com.huazheng.tunny.api.feign.RemoteGatewayService;
import com.huazheng.tunny.api.service.ApiInfoService;
import com.huazheng.tunny.api.service.BaseAuthorityService;
import com.huazheng.tunny.common.core.constant.MqConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.annotation.Payload;

import java.time.Duration;
import java.util.Iterator;
import java.util.List;

/**
 * mq消息接收者
 */
@Configuration
@Slf4j
public class ResourceScanHandler {
    @Autowired
    private ApiInfoService apiInfoService;
    @Autowired
    private BaseAuthorityService baseAuthorityService;
//    @Autowired
//    private OpenRestTemplate restTemplate;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RemoteGatewayService remoteGatewayService;

    private final static String SCAN_API_RESOURCE_KEY_PREFIX = "scan_api_resource:";

    /**
     * 接收API资源扫描消息
     */
    @RabbitListener(queues = MqConstants.QUEUE_SCAN_API_RESOURCE)
    public void ScanApiResourceQueue(@Payload JSONObject resource) {
        try {
            String serviceId = resource.getString("application");
            String key = SCAN_API_RESOURCE_KEY_PREFIX + serviceId;
            Object object = redisTemplate.opsForValue().get(key);
            if (object != null) {
                // 3分钟内未失效,不再更新资源
                return;
            }
            JSONArray array = resource.getJSONArray("mapping");

            Iterator iterator = array.iterator();
            List<String> codes = Lists.newArrayList();
            log.info("------------------API服务开始接收接口注册，数量为{}-----------------------", array.size());
            while (iterator.hasNext()) {
                JSONObject jsonObject = (JSONObject) iterator.next();
                try {

                    ApiInfo api = jsonObject.toJavaObject(ApiInfo.class);
                    codes.add(api.getApiCode());
                    ApiInfo save = apiInfoService.getApi(api.getApiCode());
                    if (save == null) {
                        api.setIsOpen(1);
                        api.setIsPersist(1);
                        apiInfoService.addApi(api);
                    } else {
                        api.setApiId(save.getApiId());
                        apiInfoService.updateApi(api);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("添加资源error:", e.getMessage());
                }

            }
            if (array != null && array.size() > 0) {
                // 清理无效权限数据
                baseAuthorityService.clearInvalidApi(serviceId, codes);
//                restTemplate.refreshGateway();
                remoteGatewayService.refresh();
                redisTemplate.opsForValue().set(key, array.size(), 3*60);
            }

        } catch (Exception e) {
            log.error("error:", e);
        }
    }

}
