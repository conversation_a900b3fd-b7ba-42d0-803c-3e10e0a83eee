package com.huazheng.tunny.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.huazheng.tunny.api.common.constants.BaseConstants;
import com.huazheng.tunny.api.common.exception.OpenAlertException;
import com.huazheng.tunny.api.common.mybatis.base.service.impl.BaseServiceImpl;
import com.huazheng.tunny.api.common.security.Authority;
import com.huazheng.tunny.api.constants.ResourceType;
import com.huazheng.tunny.api.entity.*;
import com.huazheng.tunny.api.mapper.BaseAuthorityAppMapper;
import com.huazheng.tunny.api.mapper.BaseAuthorityMapper;
import com.huazheng.tunny.api.service.ApiInfoService;
import com.huazheng.tunny.api.service.BaseAppService;
import com.huazheng.tunny.api.service.BaseAuthorityService;
import com.huazheng.tunny.api.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


/**
 * 系统权限管理
 * 对菜单、操作、API等进行权限分配操作
 *
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class BaseAuthorityServiceImpl extends BaseServiceImpl<BaseAuthorityMapper, BaseAuthority> implements BaseAuthorityService {

    @Autowired
    private BaseAuthorityAppMapper baseAuthorityAppMapper;
    @Autowired
    private BaseAppService baseAppService;
    @Autowired
    private BaseAuthorityMapper baseAuthorityMapper;
    @Autowired
    private ApiInfoService apiInfoService;

    @Value("${spring.application.name}")
    private String DEFAULT_SERVICE_ID;

    /**
     * 移除应用权限
     *
     * @param appId
     */
    @Override
    public void removeAuthorityApp(String appId) {
        QueryWrapper<BaseAuthorityApp> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(BaseAuthorityApp::getAppId, appId);
        baseAuthorityAppMapper.delete(queryWrapper);
    }

    /**
     * 应用授权
     *
     * @param appId        应用ID
     * @param expireTime   过期时间,null表示长期,不限制
     * @param authorityIds 权限集合
     * @return
     */
    @CacheEvict(value = {"apps"}, key = "'client:'+#appId")
    @Override
    public void addAuthorityApp(String appId, Date expireTime, String... authorityIds) {
        if (appId == null) {
            return;
        }
        BaseApp baseApp = baseAppService.getAppInfo(appId);
        if (baseApp == null) {
            return;
        }
        if (baseApp.getIsPersist().equals(BaseConstants.ENABLED)) {
            throw new OpenAlertException(String.format("保留数据,不允许授权"));
        }
        // 清空应用已有授权
        QueryWrapper<BaseAuthorityApp> appQueryWrapper = new QueryWrapper();
        appQueryWrapper.lambda().eq(BaseAuthorityApp::getAppId, appId);
        baseAuthorityAppMapper.delete(appQueryWrapper);
        BaseAuthorityApp authorityApp = null;
        if (authorityIds != null && authorityIds.length > 0) {
            for (String id : authorityIds) {
                authorityApp = new BaseAuthorityApp();
                authorityApp.setAuthorityId(Long.parseLong(id));
                authorityApp.setAppId(appId);
                authorityApp.setExpireTime(expireTime);
                authorityApp.setCreateTime(new Date());
                authorityApp.setUpdateTime(authorityApp.getCreateTime());
                baseAuthorityAppMapper.insert(authorityApp);

            }
        }
        // 获取应用最新的权限列表
//        List<Authority> authorities = findAuthorityByApp(appId);
//        // 动态更新客户端生成的token
//        Collection<OAuth2AccessToken> accessTokens = redisTokenStore.findTokensByClientId(appId);
//        if (accessTokens != null && !accessTokens.isEmpty()) {
//            Iterator<OAuth2AccessToken> iterator = accessTokens.iterator();
//            while (iterator.hasNext()) {
//                OAuth2AccessToken token = iterator.next();
//                OAuth2Authentication oAuth2Authentication = redisTokenStore.readAuthentication(token);
//                // 由于没有set方法,使用反射机制强制赋值
//                ReflectionUtils.setFieldValue(oAuth2Authentication, "authorities", authorities);
//                // 重新保存
//                redisTokenStore.storeAccessToken(token, oAuth2Authentication);
//            }
//        }
    }

    /**
     * 获取应用已授权权限
     *
     * @param appId
     * @return
     */
    @Override
    public List<Authority> findAuthorityByApp(String appId) {
        List<Authority> authorities = Lists.newArrayList();
        List<Authority> list = baseAuthorityAppMapper.selectAuthorityByApp(appId);
        if (list != null) {
            authorities.addAll(list);
        }
        return authorities;
    }
    @Override
    public List<Map> findAuthorityByApp2(String appId) {
        List<Map> authorities = Lists.newArrayList();
        List<Map> list = baseAuthorityAppMapper.selectAuthorityByApp2(appId);
        if (list != null) {
            authorities.addAll(list);
        }
        return authorities;
    }


    @Override
//    public List<AuthorityApi> findAuthorityApi(String serviceId) {
    public List<Map> findAuthorityApi(String serviceId) {
        Map map = Maps.newHashMap();
        map.put("serviceId", serviceId);
        map.put("status", 1);
//        List<AuthorityApi> authorities = baseAuthorityMapper.selectAuthorityApi(map);
        List<Map> authorities = baseAuthorityMapper.selectAuthorityApi(map);
        return authorities;

    }



    /**
     * 保存或修改权限
     *
     * @param resourceId
     * @param resourceType
     * @return 权限Id
     */
    @Override
    public BaseAuthority saveOrUpdateAuthority(Long resourceId, ResourceType resourceType) {
        BaseAuthority baseAuthority = getAuthority(resourceId, resourceType);
        String authority = null;
        if (baseAuthority == null) {
            baseAuthority = new BaseAuthority();
        }
//        if (ResourceType.menu.equals(resourceType)) {
//            BaseMenu menu = baseMenuService.getMenu(resourceId);
//            authority = OpenSecurityConstants.AUTHORITY_PREFIX_MENU + menu.getMenuCode();
//            baseAuthority.setMenuId(resourceId);
//            baseAuthority.setStatus(menu.getStatus());
//        }
//        if (ResourceType.action.equals(resourceType)) {
//            BaseAction operation = baseActionService.getAction(resourceId);
//            authority = OpenSecurityConstants.AUTHORITY_PREFIX_ACTION + operation.getActionCode();
//            baseAuthority.setActionId(resourceId);
//            baseAuthority.setStatus(operation.getStatus());
//        }
        if (ResourceType.api.equals(resourceType)) {
            ApiInfo api = apiInfoService.getApi(resourceId);
            authority = "API_" + api.getApiCode();
            baseAuthority.setApiId(resourceId);
            baseAuthority.setStatus(api.getStatus());
        }
        if (authority == null) {
            return null;
        }
        // 设置权限标识
        baseAuthority.setAuthority(authority);
        if (baseAuthority.getAuthorityId() == null) {
            baseAuthority.setCreateTime(new Date());
            baseAuthority.setUpdateTime(baseAuthority.getCreateTime());
            // 新增权限
            baseAuthorityMapper.insert(baseAuthority);
        } else {
            // 修改权限
            baseAuthority.setUpdateTime(new Date());
            baseAuthorityMapper.updateById(baseAuthority);
        }
        return baseAuthority;
    }

    /**
     * 获取权限
     *
     * @param resourceId
     * @param resourceType
     * @return
     */
    @Override
    public BaseAuthority getAuthority(Long resourceId, ResourceType resourceType) {
        if (resourceId == null || resourceType == null) {
            return null;
        }
        QueryWrapper<BaseAuthority> queryWrapper = buildQueryWrapper(resourceId, resourceType);
        return baseAuthorityMapper.selectOne(queryWrapper);
    }


    /**
     * 构建权限对象
     *
     * @param resourceId
     * @param resourceType
     * @return
     */
    private QueryWrapper<BaseAuthority> buildQueryWrapper(Long resourceId, ResourceType resourceType) {
        QueryWrapper<BaseAuthority> queryWrapper = new QueryWrapper();
        if (ResourceType.menu.equals(resourceType)) {
            queryWrapper.lambda().eq(BaseAuthority::getMenuId, resourceId);
        }
        if (ResourceType.action.equals(resourceType)) {
            queryWrapper.lambda().eq(BaseAuthority::getActionId, resourceId);
        }
        if (ResourceType.api.equals(resourceType)) {
            queryWrapper.lambda().eq(BaseAuthority::getApiId, resourceId);
        }
        return queryWrapper;
    }


    /**
     * 清理无效数据
     *
     * @param serviceId
     * @param codes
     */
    @Override
    public void clearInvalidApi(String serviceId, Collection<String> codes) {
        if (StringUtils.isBlank(serviceId)) {
            return;
        }
        List<String> invalidApiIds = apiInfoService.listObjs(new QueryWrapper<ApiInfo>().select("api_id").eq("service_id", serviceId).notIn(codes!=null&&!codes.isEmpty(),"api_code", codes), e -> e.toString());
        if (invalidApiIds != null) {
            // 防止删除默认api
            invalidApiIds.remove("1");
            invalidApiIds.remove("2");
            // 获取无效的权限
            if (invalidApiIds.isEmpty()) {
                return;
            }
            List<String> invalidAuthorityIds = listObjs(new QueryWrapper<BaseAuthority>().select("authority_id").in("api_id", invalidApiIds), e -> e.toString());
            if (invalidAuthorityIds != null && !invalidAuthorityIds.isEmpty()) {
                // 移除关联数据
                baseAuthorityAppMapper.delete(new QueryWrapper<BaseAuthorityApp>().in("authority_id", invalidAuthorityIds));
//                baseAuthorityActionMapper.delete(new QueryWrapper<BaseAuthorityAction>().in("authority_id", invalidAuthorityIds));
//                baseAuthorityRoleMapper.delete(new QueryWrapper<BaseAuthorityRole>().in("authority_id", invalidAuthorityIds));
//                baseAuthorityUserMapper.delete(new QueryWrapper<BaseAuthorityUser>().in("authority_id", invalidAuthorityIds));
                // 移除权限数据
                this.removeByIds(invalidAuthorityIds);
                // 移除接口资源
                apiInfoService.removeByIds(invalidApiIds);
            }
        }
    }
}
