package com.huazheng.tunny.api.config;

import com.huazheng.tunny.common.core.constant.SecurityConstants;
import com.huazheng.tunny.common.security.component.ResourceAuthExceptionEntryPoint;
import com.huazheng.tunny.common.security.component.TunnyAccessDeniedHandler;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;


@Configuration
@EnableResourceServer
@AllArgsConstructor
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class ResourceServerConfigurer extends ResourceServerConfigurerAdapter {
    private final TunnyAccessDeniedHandler tunnyAccessDeniedHandler;
    private final ResourceAuthExceptionEntryPoint resourceAuthExceptionEntryPoint;

    private final RedisConnectionFactory redisConnectionFactory;
    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
                .antMatchers("/actuator/**"
                        , "/v2/api-docs"
                        , "/info"
                      //  , "/**"
                ).permitAll()
                .anyRequest().authenticated()
                .and().csrf().disable();
    }

    /**
     * why add  resourceId
     * https://stackoverflow.com/questions/28703847/how-do-you-set-a-resource-id-for-a-token
     *
     * @param resources
     * @throws Exception
     */
    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        resources.authenticationEntryPoint(resourceAuthExceptionEntryPoint)
                .accessDeniedHandler(tunnyAccessDeniedHandler);
    }

    /**
     * Token的存储机制 --Redis版
     * @return
     */
    @Bean
    public TokenStore tokenStore() {
        RedisTokenStore tokenStore = new RedisTokenStore(redisConnectionFactory);
        tokenStore.setPrefix(SecurityConstants.TUNNY_PREFIX + SecurityConstants.OAUTH_PREFIX);
        return tokenStore;
    }
}
