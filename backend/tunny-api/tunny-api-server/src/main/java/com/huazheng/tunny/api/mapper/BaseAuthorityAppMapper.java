package com.huazheng.tunny.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huazheng.tunny.api.common.security.Authority;
import com.huazheng.tunny.api.entity.BaseAuthorityApp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 *
 */
@Repository
public interface BaseAuthorityAppMapper extends BaseMapper<BaseAuthorityApp> {

    /**
     * 获取应用已授权权限
     *
     * @param appId
     * @return
     */
    List<Authority> selectAuthorityByApp(@Param("appId") String appId);
    List<Map> selectAuthorityByApp2(@Param("appId") String appId);
}
