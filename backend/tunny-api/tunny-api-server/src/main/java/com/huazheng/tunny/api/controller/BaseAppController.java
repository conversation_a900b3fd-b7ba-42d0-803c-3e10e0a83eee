package com.huazheng.tunny.api.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huazheng.tunny.api.common.PageParams;
import com.huazheng.tunny.api.common.ResultBody;
import com.huazheng.tunny.api.common.client.BaseAppRemoteApi;
import com.huazheng.tunny.api.common.security.Authority;
import com.huazheng.tunny.api.common.security.OpenClient;
import com.huazheng.tunny.api.entity.BaseApp;
import com.huazheng.tunny.api.feign.RemoteGatewayService;
import com.huazheng.tunny.api.service.BaseAppService;
import com.huazheng.tunny.api.service.BaseAuthorityService;
import com.huazheng.tunny.api.util.StringUtils;
import com.huazheng.tunny.tools.file.AttackmentDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * 系统用户信息
 *
 *
 */
@Api(tags = "系统应用管理")
@RestController
@RequestMapping("/app")
public class BaseAppController implements BaseAppRemoteApi {
    @Autowired
    private BaseAppService baseAppService;
    @Autowired
    private BaseAuthorityService baseAuthorityService;

    @Autowired
    private RemoteGatewayService remoteGatewayService;
//    @Autowired
//    private OpenRestTemplate openRestTemplate;

    /**
     * 获取分页应用列表
     *
     * @return
     */
    @ApiOperation(value = "获取分页应用列表", notes = "获取分页应用列表")
    @GetMapping("/appPage")
    public ResultBody<IPage<BaseApp>> getAppListPage(@RequestParam(required = false) Map map) {
        IPage<BaseApp> ipage = baseAppService.findListPage(new PageParams(map));
        return ResultBody.ok().data(ipage);
    }

    /**
     * 获取应用详情
     *
     * @param appId
     * @return
     */
    @ApiOperation(value = "获取应用详情", notes = "仅限系统内部调用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用ID", defaultValue = "1", required = true, paramType = "path"),
    })
    @GetMapping("/{appId}/info")
    @Override
    public ResultBody<BaseApp> getApp(
            @PathVariable("appId") String appId
    ) {
        BaseApp appInfo = baseAppService.getAppInfo(appId);
        return ResultBody.ok().data(appInfo);
    }

    /**
     * 获取应用开发配置信息
     *
     * @param appId
     * @return
     */
    // TODO: 2019/6/25 接口auth2 调用问题，500错误
    @ApiOperation(value = "获取应用开发配置信息", notes = "获取应用开发配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用ID", defaultValue = "1", required = true, paramType = "path"),
    })
    @GetMapping("/client/{appId}/info")
    @Override
    public ResultBody<OpenClient> getAppClientInfo(
            @PathVariable("appId") String appId
    ) {
        OpenClient clientInfo = baseAppService.getAppClientInfo(appId);
//        return ResultBody.ok().data(clientInfo);
        Map<String, Object> map = new HashMap();
//        String[] x = {"userProfile"};
        String[] x = {"server"};
        map.put("scope", x);
//        map.put("client_id", "1552294656514");
        map.put("client_id", clientInfo.getClientId());
//        map.put("client_secret", "$2a$10$UAzdXTnT9DAyfzSNInoX4.bt/8V0zdn23m7uQiwsyorHLucf4ftfO");
        map.put("client_secret", clientInfo.getClientSecret());
//        String[] authorized_grant_types = {"authorization_code", "client_credentials", "password"};
//        map.put("authorized_grant_types", authorized_grant_types);
        map.put("authorized_grant_types", clientInfo.getAuthorizedGrantTypes() != null ? clientInfo.getAuthorizedGrantTypes() : new ArrayList<>());
//        String[] redirect_uri = {"authorization_code", "client_credentials", "password"};
//        map.put("redirect_uri", redirect_uri);
        map.put("redirect_uri", clientInfo.getRegisteredRedirectUri() != null ? clientInfo.getRegisteredRedirectUri() : new ArrayList<>());
//        map.put("access_token_validity", 43200);
        map.put("access_token_validity", clientInfo.getAccessTokenValiditySeconds());
//        map.put("refresh_token_validity", 2592000);
        map.put("refresh_token_validity", clientInfo.getRefreshTokenValiditySeconds());
        String[] autoapprove = {};
        map.put("autoapprove", autoapprove);

//        Map additional_information = new HashMap();
//        additional_information.put("appDesc", "运营后台");
//        additional_information.put("appIcon", "");
//        additional_information.put("appId", "1552294656514");
//        additional_information.put("appName", "运营后台");
//        additional_information.put("appNameEn", "Admin");
//        additional_information.put("appOs", "");
//        additional_information.put("appSecret", "74a02bade18a42388c3127751b96e1f7");
//        additional_information.put("appType", "pc");
//        additional_information.put("createTime", "1542016125000");
//        additional_information.put("isPersist", 1);
//        additional_information.put("status", 1);
//        additional_information.put("updateTime", "1553049844000");
//        additional_information.put("userId", "521677655146233856");
//        additional_information.put("userType", "platform");
//        additional_information.put("website", "http://www.baidu.com");
//        map.put("additional_information", additional_information);
        BaseApp additionalInformation = baseAppService.getById(clientInfo.getClientId());
        map.put("additional_information", additionalInformation);
        return ResultBody.ok().data(map);
    }

    /**
     * 添加应用信息
     *
     * @param appName   应用名称
     * @param appNameEn 应用英文名称
     * @param appOs     手机应用操作系统:ios-苹果 android-安卓
     * @param appType   应用类型:server-应用服务 app-手机应用 pc-PC网页应用 wap-手机网页应用
     * @param appIcon   应用图标
     * @param appDesc   应用说明
     * @param status    状态
     * @param website   官网地址
     * @param developer 开发者
     * @return
     */
    // TODO: 2019/6/25 auth2问题
    @ApiOperation(value = "添加应用信息", notes = "添加应用信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appName", value = "应用名称", required = true, paramType = "form"),
            @ApiImplicitParam(name = "appNameEn", value = "应用英文名称", required = true, paramType = "form"),
            @ApiImplicitParam(name = "appType", value = "应用类型(server-应用服务 app-手机应用 pc-PC网页应用 wap-手机网页应用)", allowableValues = "server,app,pc,wap", required = true, paramType = "form"),
            @ApiImplicitParam(name = "appIcon", value = "应用图标", required = false, paramType = "form"),
            @ApiImplicitParam(name = "appOs", value = "手机应用操作系统", allowableValues = "android,ios", required = false, paramType = "form"),
            @ApiImplicitParam(name = "appDesc", value = "应用说明", required = false, paramType = "form"),
            @ApiImplicitParam(name = "status", required = true, defaultValue = "1", allowableValues = "0,1", value = "是否启用", paramType = "form"),
            @ApiImplicitParam(name = "website", value = "官网地址", required = true, paramType = "form"),
            @ApiImplicitParam(name = "developer", value = "开发者", required = false, paramType = "form")
    })
    @PostMapping("/add")
    public ResultBody<String> addApp(
            @RequestParam(value = "appName") String appName,
            @RequestParam(value = "appNameEn") String appNameEn,
            @RequestParam(value = "appType") String appType,
            @RequestParam(value = "appIcon", required = false) String appIcon,
            @RequestParam(value = "appOs", required = false) String appOs,
            @RequestParam(value = "appDesc", required = false) String appDesc,
            @RequestParam(value = "status", defaultValue = "1") Integer status,
            @RequestParam(value = "website") String website,
            @RequestParam(value = "developer", required = false) String developer
    ) {
        BaseApp app = new BaseApp();
        app.setAppName(appName);
        app.setAppNameEn(appNameEn);
        app.setAppType(appType);
        app.setAppOs(appOs);
        app.setAppIcon(appIcon);
        app.setAppDesc(appDesc);
        app.setStatus(status);
        app.setWebsite(website);
        app.setDeveloper(developer);
        BaseApp result = baseAppService.addAppInfo(app);
        String appId = null;
        if (result != null) {
            appId = result.getAppId();
        }
        return ResultBody.ok().data(appId);
    }

    /**
     * 编辑应用信息
     *
     * @param appId
     * @param appName   应用名称
     * @param appNameEn 应用英文名称
     * @param appOs     手机应用操作系统:ios-苹果 android-安卓
     * @param appType   应用类型:server-应用服务 app-手机应用 pc-PC网页应用 wap-手机网页应用
     * @param appIcon   应用图标
     * @param appDesc   应用说明
     * @param status    状态
     * @param website   官网地址
     * @param developer 开发者
     * @return
     * @
     */
    @ApiOperation(value = "编辑应用信息", notes = "编辑应用信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用Id", required = true, paramType = "form"),
            @ApiImplicitParam(name = "appName", value = "应用名称", required = true, paramType = "form"),
            @ApiImplicitParam(name = "appNameEn", value = "应用英文名称", required = true, paramType = "form"),
            @ApiImplicitParam(name = "appType", value = "应用类型(server-应用服务 app-手机应用 pc-PC网页应用 wap-手机网页应用)", allowableValues = "server,app,pc,wap", required = true, paramType = "form"),
            @ApiImplicitParam(name = "appIcon", value = "应用图标", paramType = "form"),
            @ApiImplicitParam(name = "appOs", value = "手机应用操作系统", allowableValues = "android,ios", required = false, paramType = "form"),
            @ApiImplicitParam(name = "appDesc", value = "应用说明", required = false, paramType = "form"),
            @ApiImplicitParam(name = "status", required = true, defaultValue = "1", allowableValues = "0,1", value = "是否启用", paramType = "form"),
            @ApiImplicitParam(name = "website", value = "官网地址", required = true, paramType = "form"),
            @ApiImplicitParam(name = "developer", value = "开发者", required = false, paramType = "form")
    })
    @PostMapping("/update")
    public ResultBody updateApp(
            @RequestParam("appId") String appId,
            @RequestParam(value = "appName") String appName,
            @RequestParam(value = "appNameEn") String appNameEn,
            @RequestParam(value = "appType") String appType,
            @RequestParam(value = "appIcon", required = false) String appIcon,
            @RequestParam(value = "appOs", required = false) String appOs,
            @RequestParam(value = "appDesc", required = false) String appDesc,
            @RequestParam(value = "status", defaultValue = "1") Integer status,
            @RequestParam(value = "website") String website,
            @RequestParam(value = "developer", required = false) String developer
    ) {
        BaseApp app = new BaseApp();
        app.setAppId(appId);
        app.setAppName(appName);
        app.setAppNameEn(appNameEn);
        app.setAppType(appType);
        app.setAppOs(appOs);
        app.setAppIcon(appIcon);
        app.setAppDesc(appDesc);
        app.setStatus(status);
        app.setWebsite(website);
        app.setDeveloper(developer);
        baseAppService.updateInfo(app);
        //openRestTemplate.refreshGateway();
        remoteGatewayService.refresh();
        return ResultBody.ok();
    }


    /**
     * 完善应用开发信息
     *
     * @param appId                应用名称
     * @param grantTypes           授权类型(多个使用,号隔开)
     * @param redirectUrls         第三方应用授权回调地址(多个使用,号隔开)
     * @param scopes               用户授权范围(多个使用,号隔开)
     * @param autoApproveScopes    用户自动授权范围(多个使用,号隔开)
     * @param accessTokenValidity  令牌有效期(秒)
     * @param refreshTokenValidity 刷新令牌有效期(秒)
     * @return
     */
    @ApiOperation(value = "完善应用开发信息", notes = "完善应用开发信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用Id", required = true, paramType = "form"),
            @ApiImplicitParam(name = "authorized_grant_types", value = "授权类型(多个使用,号隔开)", required = true, paramType = "form"),
            @ApiImplicitParam(name = "redirect_uri", value = "第三方应用授权回调地址", required = true, paramType = "form"),
            @ApiImplicitParam(name = "scope", value = "用户授权范围(多个使用,号隔开)", required = true, paramType = "form"),
            @ApiImplicitParam(name = "autoapprove", value = "用户自动授权范围(多个使用,号隔开)", required = false, paramType = "form"),
            @ApiImplicitParam(name = "access_token_validity", value = "令牌有效期(秒)", required = true, paramType = "form"),
            @ApiImplicitParam(name = "refresh_token_validity", value = "刷新令牌有效期(秒)", required = true, paramType = "form")
            /*@ApiImplicitParam(name = "grantTypes", value = "授权类型(多个使用,号隔开)", required = true, paramType = "form"),
            @ApiImplicitParam(name = "redirectUrls", value = "第三方应用授权回调地址", required = true, paramType = "form"),
            @ApiImplicitParam(name = "scopes", value = "用户授权范围(多个使用,号隔开)", required = true, paramType = "form"),
            @ApiImplicitParam(name = "autoApproveScopes", value = "用户自动授权范围(多个使用,号隔开)", required = false, paramType = "form"),
            @ApiImplicitParam(name = "accessTokenValidity", value = "令牌有效期(秒)", required = true, paramType = "form"),
            @ApiImplicitParam(name = "refreshTokenValidity", value = "刷新令牌有效期(秒)", required = true, paramType = "form")*/
    })
    @PostMapping("/client/update")
    public ResultBody<String> updateAppClientInfo(
            @RequestParam("appId") String appId,
            //@RequestParam(value = "grantTypes") String grantTypes,
            @RequestParam(value = "authorized_grant_types") String grantTypes,
            //@RequestParam(value = "redirectUrls") String redirectUrls,
            @RequestParam(value = "redirect_uri") String redirectUrls,
            //@RequestParam(value = "scopes") String scopes,
            @RequestParam(value = "scope") String scopes,
            //@RequestParam(value = "accessTokenValidity", required = true) Integer accessTokenValidity,
            @RequestParam(value = "access_token_validity", required = true) Integer accessTokenValidity,
            //@RequestParam(value = "refreshTokenValidity", required = true) Integer refreshTokenValidity,
            @RequestParam(value = "refresh_token_validity", required = true) Integer refreshTokenValidity,
            //@RequestParam(value = "autoApproveScopes", required = false) String autoApproveScopes
            @RequestParam(value = "autoapprove", required = false) String autoApproveScopes
    ) {
        OpenClient client = new OpenClient(appId, "", scopes, grantTypes, "", redirectUrls);
        client.setAccessTokenValiditySeconds(accessTokenValidity);
        client.setRefreshTokenValiditySeconds(refreshTokenValidity);
        client.setAutoApproveScopes(autoApproveScopes != null ? Arrays.asList(autoApproveScopes.split(",")) : null);
        baseAppService.updateAppClientInfo(client);
        return ResultBody.ok();
    }


    /**
     * 重置应用秘钥
     *
     * @param appId 应用Id
     * @return
     */
    @ApiOperation(value = "重置应用秘钥", notes = "重置应用秘钥")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用Id", required = true, paramType = "form"),
    })
    @PostMapping("/reset")
    public ResultBody<String> resetAppSecret(
            @RequestParam("appId") String appId
    ) {
        String result = baseAppService.restSecret(appId);
        return ResultBody.ok().data(result);
    }

    /**
     * 删除应用信息
     *
     * @param appId
     * @return
     */
    @ApiOperation(value = "删除应用信息", notes = "删除应用信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用Id", required = true, paramType = "form"),
    })
    @PostMapping("/remove")
    public ResultBody removeApp(
            @RequestParam("appId") String appId
    ) {
        baseAppService.removeApp(appId);
        //openRestTemplate.refreshGateway();
        remoteGatewayService.refresh();
        return ResultBody.ok();
    }


    /**
     * 上传头像
     *
     * @param files
     * @return
     */
    @PostMapping("/uploadProfilePhoto")
    public AttackmentDto uploadProfilePhoto(@RequestParam("file") MultipartFile[] files) {
        AttackmentDto dto = new AttackmentDto();
        try {
            if (files != null) {
                dto = baseAppService.uploadProfilePhoto(files);
            } else {
                dto.setCode(500);
                dto.setMsg("头像上传异常");
            }
            return dto;
        } catch (Exception e) {
            dto.setCode(500);
            dto.setMsg("头像上传异常");
            return dto;
        }
    }


    /**
     * 获取应用已分配接口权限
     *
     * @param appId 角色ID
     * @return
     */
    @ApiOperation(value = "获取应用已分配接口权限", notes = "获取应用已分配接口权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用Id", defaultValue = "", required = true, paramType = "form")
    })
    @GetMapping("/authority/app")
    public ResultBody<List<Authority>> findAuthorityApp(
            @RequestParam(value = "appId") String appId
    ) {
//        List<Authority> result = baseAuthorityService.findAuthorityByApp2(appId);
        List<Map> result = baseAuthorityService.findAuthorityByApp2(appId);
        return ResultBody.ok().data(result);
    }


    /**
     * 分配应用权限
     *
     * @param appId        应用Id
     * @param expireTime   授权过期时间
     * @param authorityIds 权限ID.多个以,隔开
     * @return
     */
    @ApiOperation(value = "分配应用权限", notes = "分配应用权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用Id", defaultValue = "", required = true, paramType = "form"),
            @ApiImplicitParam(name = "expireTime", value = "过期时间.选填", defaultValue = "", required = false, paramType = "form"),
            @ApiImplicitParam(name = "authorityIds", value = "权限ID.多个以,隔开.选填", defaultValue = "", required = false, paramType = "form")
    })
    @PostMapping("/authority/app/grant")
    public ResultBody grantAuthorityApp(
            @RequestParam(value = "appId") String appId,
            @RequestParam(value = "expireTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date expireTime,
            @RequestParam(value = "authorityIds", required = false) String authorityIds
    ) {
        baseAuthorityService.addAuthorityApp(appId, expireTime, StringUtils.isNotBlank(authorityIds) ? authorityIds.split(",") : new String[]{});
        //openRestTemplate.refreshGateway();
        remoteGatewayService.refresh();
        return ResultBody.ok();
    }
}
