package com.huazheng.tunny.api.mapper;

import com.huazheng.tunny.api.common.mybatis.base.mapper.SuperMapper;
import com.huazheng.tunny.api.entity.GatewayIpLimitApi;
import com.huazheng.tunny.api.entity.IpLimitApi;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 */
@Repository
public interface GatewayIpLimitApisMapper extends SuperMapper<GatewayIpLimitApi> {

    List<IpLimitApi> selectIpLimitApi(@Param("policyType") int policyType);
}
