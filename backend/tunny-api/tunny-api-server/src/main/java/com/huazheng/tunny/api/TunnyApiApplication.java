package com.huazheng.tunny.api;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;


@EnableDiscoveryClient
@EnableFeignClients
@SpringCloudApplication
@EnableAuthorizationServer
public class TunnyApiApplication implements CommandLineRunner {

    public static void main(String[] args) {

        SpringApplication.run(TunnyApiApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        // annotationScan();
    }
}
