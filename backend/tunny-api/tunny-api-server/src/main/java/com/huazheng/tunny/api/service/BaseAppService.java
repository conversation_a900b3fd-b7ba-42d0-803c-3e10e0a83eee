package com.huazheng.tunny.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huazheng.tunny.api.common.PageParams;
import com.huazheng.tunny.api.common.mybatis.base.service.IBaseService;
import com.huazheng.tunny.api.common.security.OpenClient;
import com.huazheng.tunny.api.entity.BaseApp;
import com.huazheng.tunny.tools.file.AttackmentDto;
import org.springframework.web.multipart.MultipartFile;

/**
 * 应用信息管理
 *
 *
 */
public interface BaseAppService extends IBaseService<BaseApp> {

    /**
     * 查询应用列表
     *
     * @param pageParams
     * @return
     */
    IPage<BaseApp> findListPage(PageParams pageParams);

    /**
     * 获取app信息
     *
     * @param appId
     * @return
     */
    BaseApp getAppInfo(String appId);

    /**
     * 获取app和应用信息
     *
     * @param appId
     * @return
     */
    OpenClient getAppClientInfo(String appId);


    /**
     * 更新应用开发新型
     *
     * @param client
     */
    void updateAppClientInfo(OpenClient client);

    /**
     * 添加应用
     *
     * @param app 应用
     * @return 应用信息
     */
    BaseApp addAppInfo(BaseApp app);

    /**
     * 修改应用
     *
     * @param app 应用
     * @return 应用信息
     */
    BaseApp updateInfo(BaseApp app);


    /**
     * 重置秘钥
     *
     * @param appId
     * @return
     */
    String restSecret(String appId);

    /**
     * 删除应用
     *
     * @param appId
     * @return
     */
    void removeApp(String appId);

    /**
     * 上传头像
     *
     * @param files
     * @return
     */
    AttackmentDto uploadProfilePhoto(MultipartFile[] files);
}
