package com.huazheng.tunny.api.mapper;

import com.huazheng.tunny.api.common.mybatis.base.mapper.SuperMapper;
import com.huazheng.tunny.api.entity.AuthorityApi;
import com.huazheng.tunny.api.entity.BaseAuthority;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 *
 */
@Repository
public interface BaseAuthorityMapper extends SuperMapper<BaseAuthority> {

    /**
     * 查询所有资源授权列表
     * @return
     */
//    List<AuthorityResource> selectAllAuthorityResource();

    /**
     * 查询已授权权限列表
     *
     * @param map
     * @return
     */
//    List<Authority> selectAuthorityAll(Map map);


    /**
     * 获取菜单权限
     *
     * @param map
     * @return
     */
//    List<AuthorityMenu> selectAuthorityMenu(Map map);

    /**
     * 获取操作权限
     *
     * @param map
     * @return
     */
//    List<AuthorityAction> selectAuthorityAction(Map map);

    /**
     * 获取API权限
     *
     * @param map
     * @return
     */
//    List<AuthorityApi> selectAuthorityApi(Map map);
    List<Map> selectAuthorityApi(Map map);


}
