package com.huazheng.tunny.api.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huazheng.tunny.api.common.PageParams;
import com.huazheng.tunny.api.common.mybatis.base.service.IBaseService;
import com.huazheng.tunny.api.entity.ApiInfo;

import java.util.List;
import java.util.Map;

/**
 * 接口资源管理
 *
 *
 */
public interface ApiInfoService extends IBaseService<ApiInfo> {
    /**
     * 分页查询
     *
     * @param pageParams
     * @return
     */
    IPage<ApiInfo> findListPage(PageParams pageParams);

    /**
     * 查询列表
     *
     * @return
     */
    List<ApiInfo> findAllList(String serviceId);

    /**
     * 根据主键获取接口
     *
     * @param apiId
     * @return
     */
    ApiInfo getApi(Long apiId);


    /**
     * 检查接口编码是否存在
     *
     * @param apiCode
     * @return
     */
    Boolean isExist(String apiCode);

    /**
     * 添加接口
     *
     * @param api
     * @return
     */
    void addApi(ApiInfo api);

    /**
     * 修改接口
     *
     * @param api
     * @return
     */
    void updateApi(ApiInfo api);

    /**
     * 查询接口
     *
     * @param apiCode
     * @return
     */
    ApiInfo getApi(String apiCode);

    /**
     * 移除接口
     *
     * @param apiId
     * @return
     */
    void removeApi(Long apiId);

    /**
     * 获取数量
     *
     * @param queryWrapper
     * @return
     */
    int getCount(QueryWrapper<ApiInfo> queryWrapper);

    /**
     * 接口扫描
     *
     * @param list
     * @returni
     */
    void nterfaceScanning(List<Map<String, String>> list);

    /**
     * 报表——API指标
     * */
    Map apiIndicator();
	/**
		调用统计
	*/
    Map callStatistics(Map map);

	/**
		API流量（单位：次）
	*/
    List apiTraffic(Map map);
}
