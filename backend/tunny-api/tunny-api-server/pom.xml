<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>tunny-api</artifactId>
		<version>1.3.2</version>
	</parent>

	<artifactId>tunny-api-server</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>

	<name>tunny-api-server</name>
	<description>tunny 通用api业务处理模块</description>

	<dependencies>
		<!--basic api、model 模块-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-api-client</artifactId>
			<version>1.3.2</version>
		</dependency>

		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>3.1.1</version>
		</dependency>


		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-core</artifactId>
			<version>3.1.1</version>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<!--common-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-core</artifactId>
			<version>1.3.2</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.security.oauth</groupId>
			<artifactId>spring-security-oauth2</artifactId>
			<version>2.2.2.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-oauth2</artifactId>
			<version>2.1.2.RELEASE</version>
		</dependency>
<!--
		<dependency>
			<groupId>org.springframework.security.oauth</groupId>
			<artifactId>spring-security-oauth2</artifactId>
			<version>2.3.4.RELEASE</version>
		</dependency>
-->

		<!--web 模块-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<!--排除tomcat依赖-->
				<exclusion>
					<artifactId>spring-boot-starter-tomcat</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
        <dependency>
            <groupId>com.huazheng</groupId>
            <artifactId>tunny-common-tools</artifactId>
            <version>1.3.2</version>
        </dependency>

		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-starter</artifactId>
			<version>1.3.2</version>
		</dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-amqp</artifactId>
            <version>2.1.5.RELEASE</version>
        </dependency>
		<dependency>
			<groupId>org.springframework.amqp</groupId>
			<artifactId>spring-rabbit</artifactId>
			<version>2.1.5.RELEASE</version>
		</dependency>


		<!--日志采集上报-->
		<dependency>
			<groupId>com.plumelog</groupId>
			<artifactId>plumelog-logback</artifactId>
			<version>3.0</version>
		</dependency>

		<!--swagger 内置安全模块-->
		<dependency>
			<groupId>com.huazheng</groupId>
			<artifactId>tunny-common-swagger</artifactId>
			<version>1.3.2</version>
			<exclusions>
				<!--为安全考虑移除actuator监控-->
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-actuator</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- spring oauth2 -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-oauth2</artifactId>
			<version>2.1.2.RELEASE</version>

			<exclusions>
				<!--为安全考虑移除actuator监控-->
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-actuator</artifactId>
				</exclusion>
			</exclusions>

		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.huazheng.tunny.api.TunnyApiApplication</mainClass>
				</configuration>
				<version>1.4.2.RELEASE</version>
			</plugin>
		</plugins>
		<resources>

		</resources>
		<finalName>${project.artifactId}-${maven.build.timestamp}</finalName>
	</build>
	<properties>
		<!--自定义默认的编码格式-->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!--自定义默认的时间格式 年-月-日-时-分-->
		<maven.build.timestamp.format>
			yyyy-MM-dd_HH_mm
		</maven.build.timestamp.format>
	</properties>
</project>
