package com.huazheng.tunny.api.common.client;

import com.huazheng.tunny.api.common.ResultBody;
import com.huazheng.tunny.api.common.security.OpenClient;
import com.huazheng.tunny.api.entity.BaseApp;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 */
public interface BaseAppRemoteApi {

    /**
     * 获取应用基础信息
     *
     * @param appId 应用Id
     * @return
     */
    @GetMapping("/app/{appId}/info")
    ResultBody<BaseApp> getApp(@PathVariable("appId") String appId);

    /**
     * 获取应用开发配置信息
     * @param appId
     * @return
     */
    @GetMapping("/app/client/{appId}/info")
    ResultBody<OpenClient> getAppClientInfo(@PathVariable("appId") String appId);
}
