package com.huazheng.tunny.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 */
@Data
@TableName("gateway_rate_limit_api")
public class GatewayRateLimitApi implements Serializable {
    /**
     * 限制数量
     */
    private String policyId;

    /**
     * 时间间隔(秒)
     */
    private String apiId;


    private static final long serialVersionUID = 1L;
}
