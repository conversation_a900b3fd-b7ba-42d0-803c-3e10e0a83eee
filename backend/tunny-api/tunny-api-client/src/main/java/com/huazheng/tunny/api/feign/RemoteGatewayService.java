package com.huazheng.tunny.api.feign;

import com.huazheng.tunny.api.entity.GatewayRoute;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(value = "tunny-gateway")
public interface RemoteGatewayService {

    /**
     * 刷新路由配置
     */
    @GetMapping("/route/refresh")
    public String refresh();

    /**
     * 新增指定路由配置
     */
    @PostMapping("/route/saveRoute")
    public void saveRoute(@RequestBody GatewayRoute gatewayRoute);

    /**
     * 修改指定路由配置
     */
    @PostMapping("/route/update")
    public void update(@RequestBody GatewayRoute gatewayRoute);

    /**
     * 删除指定路由配置
     */
    @GetMapping("/route/deleteRoute")
    public void deleteRoute(String serviceId);
}
