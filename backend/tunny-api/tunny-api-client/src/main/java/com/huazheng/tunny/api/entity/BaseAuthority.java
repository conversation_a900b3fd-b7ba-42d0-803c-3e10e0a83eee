package com.huazheng.tunny.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统权限-菜单权限、操作权限、API权限
 *
 */
@TableName("api_authority")
public class BaseAuthority implements Serializable {
    @TableId(type = IdType.ID_WORKER)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long authorityId;

    /**
     * 权限标识
     */
    private String authority;

    /**
     * 菜单资源ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long menuId;

    /**
     * API资源ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long apiId;

    /**
     * 操作资源ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long actionId;

    /**
     * 状态
     */
    private Integer status;


    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    public Date createTime;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    public Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return authority_id
     */
    public Long getAuthorityId() {
        return authorityId;
    }

    /**
     * @param authorityId
     */
    public void setAuthorityId(Long authorityId) {
        this.authorityId = authorityId;
    }

    /**
     * 获取权限标识
     *
     * @return authority - 权限标识
     */
    public String getAuthority() {
        return authority;
    }

    /**
     * 设置权限标识
     *
     * @param authority 权限标识
     */
    public void setAuthority(String authority) {
        this.authority = authority == null ? null : authority.trim();
    }

    /**
     * 获取菜单资源ID
     *
     * @return menu_id - 菜单资源ID
     */
    public Long getMenuId() {
        return menuId;
    }

    /**
     * 设置菜单资源ID
     *
     * @param menuId 菜单资源ID
     */
    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    /**
     * 获取API资源ID
     *
     * @return api_id - API资源ID
     */
    public Long getApiId() {
        return apiId;
    }

    /**
     * 设置API资源ID
     *
     * @param apiId API资源ID
     */
    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }

    public Long getActionId() {
        return actionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
