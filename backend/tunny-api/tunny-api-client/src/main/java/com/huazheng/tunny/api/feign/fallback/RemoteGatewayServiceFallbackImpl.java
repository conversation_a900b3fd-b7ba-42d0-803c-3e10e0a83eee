package com.huazheng.tunny.api.feign.fallback;

import com.huazheng.tunny.api.entity.GatewayRoute;
import com.huazheng.tunny.api.feign.RemoteGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class RemoteGatewayServiceFallbackImpl implements RemoteGatewayService {

	@Override
	public String refresh() {
		return null;
	}

	@Override
	public void saveRoute(GatewayRoute gatewayRoute) {

	}

	@Override
	public void update(GatewayRoute gatewayRoute) {

	}

	@Override
	public void deleteRoute(String serviceId) {

	}
}
