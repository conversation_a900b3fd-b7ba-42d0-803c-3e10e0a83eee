package com.huazheng.tunny.api.common.exception;

import com.huazheng.tunny.api.common.constants.ResultEnum;

/**
 * 基础错误异常
 *
 */
public class OpenException extends RuntimeException {

    private static final long serialVersionUID = 3655050728585279326L;

    private int code = ResultEnum.ERROR.getCode();

    public OpenException() {

    }

    public OpenException(String msg) {
        super(msg);
    }

    public OpenException(int code, String msg) {
        super(msg);
        this.code = code;
    }

    public OpenException(int code, String msg, Throwable cause) {
        super(msg, cause);
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }


}
